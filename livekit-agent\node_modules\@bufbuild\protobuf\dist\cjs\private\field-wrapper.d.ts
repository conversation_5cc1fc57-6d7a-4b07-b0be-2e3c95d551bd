import { Message } from "../message.js";
import type { MessageType } from "../message-type.js";
import type { DescExtension, DescField } from "../descriptor-set.js";
import { ScalarType } from "../scalar.js";
/**
 * A field wrapper unwraps a message to a primitive value that is more
 * ergonomic for use as a message field.
 *
 * Note that this feature exists for google/protobuf/wrappers.proto
 * and cannot be used to arbitrarily modify types in generated code.
 */
export interface FieldWrapper<T extends Message<T> = any, U = any> {
    wrapField(value: U): T;
    unwrapField(value: T): U;
}
/**
 * Wrap a primitive message field value in its corresponding wrapper
 * message. This function is idempotent.
 */
export declare function wrapField<T extends Message<T>>(type: MessageType<T>, value: any): T;
/**
 * If the given field uses one of the well-known wrapper types, return
 * the primitive type it wraps.
 */
export declare function getUnwrappedFieldType(field: DescField | DescExtension): ScalarType | undefined;
