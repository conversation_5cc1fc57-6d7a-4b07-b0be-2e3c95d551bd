"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const livekit_server_sdk_1 = require("livekit-server-sdk");
const openai_1 = __importDefault(require("openai"));
const dotenv_1 = __importDefault(require("dotenv"));
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const multer_1 = __importDefault(require("multer"));
// Load environment variables
dotenv_1.default.config();
class LingoLensVoiceAgent {
    constructor() {
        this.openai = new openai_1.default({
            apiKey: process.env.OPENAI_API_KEY,
        });
        this.roomService = new livekit_server_sdk_1.RoomServiceClient(process.env.LIVEKIT_URL, process.env.LIVEKIT_API_KEY, process.env.LIVEKIT_API_SECRET);
        // Set up Express server
        this.app = (0, express_1.default)();
        this.app.use((0, cors_1.default)());
        this.app.use(express_1.default.json());
        // Set up multer for file uploads
        this.upload = (0, multer_1.default)({ storage: multer_1.default.memoryStorage() });
        this.setupRoutes();
    }
    async start() {
        console.log('Starting LingoLens Voice Agent');
        // Create or ensure room exists
        const roomName = process.env.DEFAULT_ROOM || 'language-learning-room';
        try {
            await this.roomService.createRoom({
                name: roomName,
                emptyTimeout: 300, // 5 minutes
                maxParticipants: 10,
            });
            console.log(`Room ${roomName} created or already exists`);
        }
        catch (error) {
            // Room might already exist, that's okay
            console.log(`Room ${roomName} already exists or error creating:`, error);
        }
        // Set up WebSocket connection to monitor room events
        await this.connectWebSocket(roomName);
        console.log('Voice agent is running and listening for requests');
    }
    async connectWebSocket(roomName) {
        // For now, we'll use a simple HTTP-based approach
        // In a full implementation, you'd use WebSocket to listen for room events
        console.log(`Monitoring room: ${roomName}`);
        // Set up periodic check for room participants and handle TTS requests
        setInterval(async () => {
            try {
                const participants = await this.roomService.listParticipants(roomName);
                // Process any pending TTS requests here
            }
            catch (error) {
                // Room might be empty or not exist yet
            }
        }, 5000);
        // Start HTTP server
        const port = process.env.PORT || 3001;
        this.app.listen(port, () => {
            console.log(`Voice agent HTTP server running on port ${port}`);
        });
    }
    setupRoutes() {
        // Health check
        this.app.get('/health', (req, res) => {
            res.json({ status: 'ok', timestamp: new Date().toISOString() });
        });
        // TTS endpoint
        this.app.post('/tts', async (req, res) => {
            try {
                const { text, language = 'en-US', voice = 'alloy' } = req.body;
                if (!text) {
                    return res.status(400).json({ error: 'Text is required' });
                }
                const audioUrl = await this.handleTTSRequest(text, language, voice);
                res.json({ audioUrl });
            }
            catch (error) {
                console.error('TTS error:', error);
                res.status(500).json({ error: 'TTS generation failed' });
            }
        });
        // STT endpoint
        this.app.post('/stt', this.upload.single('audio'), async (req, res) => {
            try {
                if (!req.file) {
                    return res.status(400).json({ error: 'Audio file is required' });
                }
                const { language = 'en' } = req.body;
                const transcription = await this.handleSTTRequest(req.file.buffer, language);
                res.json({ transcription });
            }
            catch (error) {
                console.error('STT error:', error);
                res.status(500).json({ error: 'STT transcription failed' });
            }
        });
    }
    // Simplified TTS handler that can be called via HTTP API
    async handleTTSRequest(text, language = 'en-US', voice = 'alloy') {
        try {
            console.log(`Generating TTS for: "${text.substring(0, 50)}..."`);
            // Generate speech using OpenAI TTS
            const response = await this.openai.audio.speech.create({
                model: 'tts-1',
                voice: voice,
                input: text,
                response_format: 'mp3',
            });
            // Convert response to base64
            const audioBuffer = Buffer.from(await response.arrayBuffer());
            const audioBase64 = audioBuffer.toString('base64');
            const audioUrl = `data:audio/mp3;base64,${audioBase64}`;
            console.log('TTS audio generated successfully');
            return audioUrl;
        }
        catch (error) {
            console.error('Error handling TTS request:', error);
            throw error;
        }
    }
    // Simplified STT handler
    async handleSTTRequest(audioBuffer, language = 'en') {
        try {
            console.log('Processing audio for STT...');
            // Create a temporary file for the audio
            const tempFile = new File([audioBuffer], 'audio.wav', { type: 'audio/wav' });
            const response = await this.openai.audio.transcriptions.create({
                file: tempFile,
                model: 'whisper-1',
                language: language,
            });
            console.log('STT transcription completed');
            return response.text;
        }
        catch (error) {
            console.error('Error handling STT request:', error);
            throw error;
        }
    }
    static getInstance() {
        if (!LingoLensVoiceAgent.instance) {
            LingoLensVoiceAgent.instance = new LingoLensVoiceAgent();
        }
        return LingoLensVoiceAgent.instance;
    }
}
// Get the agent instance for external API calls
LingoLensVoiceAgent.instance = null;
// Start the agent
async function main() {
    try {
        const agent = new LingoLensVoiceAgent();
        await agent.start();
        // Keep the process running
        process.on('SIGINT', () => {
            console.log('Shutting down agent...');
            process.exit(0);
        });
    }
    catch (error) {
        console.error('Failed to start agent:', error);
        process.exit(1);
    }
}
// Run the agent if this file is executed directly
if (require.main === module) {
    main();
}
//# sourceMappingURL=index.js.map