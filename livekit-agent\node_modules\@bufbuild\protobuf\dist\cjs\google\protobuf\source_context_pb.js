"use strict";
// Copyright 2021-2024 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
Object.defineProperty(exports, "__esModule", { value: true });
exports.SourceContext = void 0;
const message_js_1 = require("../../message.js");
const proto3_js_1 = require("../../proto3.js");
/**
 * `SourceContext` represents information about the source of a
 * protobuf element, like the file in which it is defined.
 *
 * @generated from message google.protobuf.SourceContext
 */
class SourceContext extends message_js_1.Message {
    constructor(data) {
        super();
        /**
         * The path-qualified name of the .proto file that contained the associated
         * protobuf element.  For example: `"google/protobuf/source_context.proto"`.
         *
         * @generated from field: string file_name = 1;
         */
        this.fileName = "";
        proto3_js_1.proto3.util.initPartial(data, this);
    }
    static fromBinary(bytes, options) {
        return new SourceContext().fromBinary(bytes, options);
    }
    static fromJson(jsonValue, options) {
        return new SourceContext().fromJson(jsonValue, options);
    }
    static fromJsonString(jsonString, options) {
        return new SourceContext().fromJsonString(jsonString, options);
    }
    static equals(a, b) {
        return proto3_js_1.proto3.util.equals(SourceContext, a, b);
    }
}
exports.SourceContext = SourceContext;
SourceContext.runtime = proto3_js_1.proto3;
SourceContext.typeName = "google.protobuf.SourceContext";
SourceContext.fields = proto3_js_1.proto3.util.newFieldList(() => [
    { no: 1, name: "file_name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
]);
