"use strict";
// Copyright 2021-2024 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.toPlainMessage = exports.createRegistryFromDescriptors = exports.createMutableRegistry = exports.createRegistry = exports.createDescriptorSet = exports.BinaryReader = exports.BinaryWriter = exports.WireType = exports.MethodIdempotency = exports.MethodKind = exports.clearExtension = exports.hasExtension = exports.setExtension = exports.getExtension = exports.ScalarType = exports.LongType = exports.isMessage = exports.Message = exports.codegenInfo = exports.protoDelimited = exports.protoBase64 = exports.protoInt64 = exports.protoDouble = exports.proto2 = exports.proto3 = void 0;
var proto3_js_1 = require("./proto3.js");
Object.defineProperty(exports, "proto3", { enumerable: true, get: function () { return proto3_js_1.proto3; } });
var proto2_js_1 = require("./proto2.js");
Object.defineProperty(exports, "proto2", { enumerable: true, get: function () { return proto2_js_1.proto2; } });
var proto_double_js_1 = require("./proto-double.js");
Object.defineProperty(exports, "protoDouble", { enumerable: true, get: function () { return proto_double_js_1.protoDouble; } });
var proto_int64_js_1 = require("./proto-int64.js");
Object.defineProperty(exports, "protoInt64", { enumerable: true, get: function () { return proto_int64_js_1.protoInt64; } });
var proto_base64_js_1 = require("./proto-base64.js");
Object.defineProperty(exports, "protoBase64", { enumerable: true, get: function () { return proto_base64_js_1.protoBase64; } });
var proto_delimited_js_1 = require("./proto-delimited.js");
Object.defineProperty(exports, "protoDelimited", { enumerable: true, get: function () { return proto_delimited_js_1.protoDelimited; } });
var codegen_info_js_1 = require("./codegen-info.js");
Object.defineProperty(exports, "codegenInfo", { enumerable: true, get: function () { return codegen_info_js_1.codegenInfo; } });
var message_js_1 = require("./message.js");
Object.defineProperty(exports, "Message", { enumerable: true, get: function () { return message_js_1.Message; } });
var is_message_js_1 = require("./is-message.js");
Object.defineProperty(exports, "isMessage", { enumerable: true, get: function () { return is_message_js_1.isMessage; } });
var scalar_js_1 = require("./scalar.js");
Object.defineProperty(exports, "LongType", { enumerable: true, get: function () { return scalar_js_1.LongType; } });
Object.defineProperty(exports, "ScalarType", { enumerable: true, get: function () { return scalar_js_1.ScalarType; } });
var extension_accessor_js_1 = require("./extension-accessor.js");
Object.defineProperty(exports, "getExtension", { enumerable: true, get: function () { return extension_accessor_js_1.getExtension; } });
Object.defineProperty(exports, "setExtension", { enumerable: true, get: function () { return extension_accessor_js_1.setExtension; } });
Object.defineProperty(exports, "hasExtension", { enumerable: true, get: function () { return extension_accessor_js_1.hasExtension; } });
Object.defineProperty(exports, "clearExtension", { enumerable: true, get: function () { return extension_accessor_js_1.clearExtension; } });
var service_type_js_1 = require("./service-type.js");
Object.defineProperty(exports, "MethodKind", { enumerable: true, get: function () { return service_type_js_1.MethodKind; } });
Object.defineProperty(exports, "MethodIdempotency", { enumerable: true, get: function () { return service_type_js_1.MethodIdempotency; } });
var binary_encoding_js_1 = require("./binary-encoding.js");
Object.defineProperty(exports, "WireType", { enumerable: true, get: function () { return binary_encoding_js_1.WireType; } });
Object.defineProperty(exports, "BinaryWriter", { enumerable: true, get: function () { return binary_encoding_js_1.BinaryWriter; } });
Object.defineProperty(exports, "BinaryReader", { enumerable: true, get: function () { return binary_encoding_js_1.BinaryReader; } });
var create_descriptor_set_js_1 = require("./create-descriptor-set.js");
Object.defineProperty(exports, "createDescriptorSet", { enumerable: true, get: function () { return create_descriptor_set_js_1.createDescriptorSet; } });
var create_registry_js_1 = require("./create-registry.js");
Object.defineProperty(exports, "createRegistry", { enumerable: true, get: function () { return create_registry_js_1.createRegistry; } });
Object.defineProperty(exports, "createMutableRegistry", { enumerable: true, get: function () { return create_registry_js_1.createMutableRegistry; } });
var create_registry_from_desc_js_1 = require("./create-registry-from-desc.js");
Object.defineProperty(exports, "createRegistryFromDescriptors", { enumerable: true, get: function () { return create_registry_from_desc_js_1.createRegistryFromDescriptors; } });
var to_plain_message_js_1 = require("./to-plain-message.js");
Object.defineProperty(exports, "toPlainMessage", { enumerable: true, get: function () { return to_plain_message_js_1.toPlainMessage; } });
// ideally, we would export these types with sub-path exports:
__exportStar(require("./google/protobuf/compiler/plugin_pb.js"), exports);
__exportStar(require("./google/protobuf/api_pb.js"), exports);
__exportStar(require("./google/protobuf/any_pb.js"), exports);
__exportStar(require("./google/protobuf/descriptor_pb.js"), exports);
__exportStar(require("./google/protobuf/duration_pb.js"), exports);
__exportStar(require("./google/protobuf/empty_pb.js"), exports);
__exportStar(require("./google/protobuf/field_mask_pb.js"), exports);
__exportStar(require("./google/protobuf/source_context_pb.js"), exports);
__exportStar(require("./google/protobuf/struct_pb.js"), exports);
__exportStar(require("./google/protobuf/timestamp_pb.js"), exports);
__exportStar(require("./google/protobuf/type_pb.js"), exports);
__exportStar(require("./google/protobuf/wrappers_pb.js"), exports);
