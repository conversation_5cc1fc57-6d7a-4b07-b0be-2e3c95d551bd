import type { JsonFormat } from "../json-format.js";
import type { BinaryFormat } from "../binary-format.js";
import type { AnyMessage } from "../message.js";
import type { Message } from "../message.js";
import type { EnumType, EnumValueInfo } from "../enum.js";
import type { MessageType } from "../message-type.js";
import type { FieldListSource } from "./field-list.js";
import type { EnumObject } from "./enum.js";
import type { Util } from "./util.js";
import type { Extension } from "../extension.js";
import type { ExtensionFieldSource } from "./extensions.js";
/**
 * A facade that provides serialization and other internal functionality.
 */
export interface ProtoRuntime {
    readonly syntax: string;
    readonly json: JsonFormat;
    readonly bin: BinaryFormat;
    readonly util: Util;
    /**
     * Create a message type at runtime, without generating code.
     */
    makeMessageType<T extends Message<T> = AnyMessage>(typeName: string, fields: FieldListSource, opt?: {
        localName?: string;
    }): MessageType<T>;
    /**
     * Create an enum object at runtime, without generating code.
     *
     * The object conforms to TypeScript enums, and comes with
     * mapping from name to value, and from value to name.
     *
     * The type name and other reflection information is accessible
     * via getEnumType().
     */
    makeEnum(typeName: string, values: (EnumValueInfo | Omit<EnumValueInfo, "localName">)[], opt?: {}): EnumObject;
    /**
     * Create an enum type at runtime, without generating code.
     * Note that this only creates the reflection information, not an
     * actual enum object.
     */
    makeEnumType(typeName: string, values: (EnumValueInfo | Omit<EnumValueInfo, "localName">)[], opt?: {}): EnumType;
    /**
     * Get reflection information - the EnumType - from an enum object.
     * If this function is called on something other than a generated
     * enum, or an enum constructed with makeEnum(), it raises an error.
     */
    getEnumType(enumObject: EnumObject): EnumType;
    /**
     * Create an extension at runtime, without generating code.
     */
    makeExtension<E extends Message<E> = AnyMessage, V = unknown>(typeName: string, extendee: MessageType<E>, field: ExtensionFieldSource): Extension<E, V>;
}
export declare function makeProtoRuntime(syntax: string, newFieldList: Util["newFieldList"], initFields: Util["initFields"]): ProtoRuntime;
