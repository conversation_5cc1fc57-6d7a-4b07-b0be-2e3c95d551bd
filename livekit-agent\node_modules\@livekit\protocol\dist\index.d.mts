import { Message, Timestamp, PartialMessage, proto3, FieldList, BinaryReadOptions, JsonValue, JsonReadOptions, PlainMessage, Duration } from '@bufbuild/protobuf';
export { protoInt64 } from '@bufbuild/protobuf';

// @generated by protoc-gen-es v1.10.1 with parameter "target=dts+js"
// @generated from file livekit_metrics.proto (package livekit, syntax proto3)
/* eslint-disable */
// @ts-nocheck



/**
 * index from [0: MAX_LABEL_PREDEFINED_MAX_VALUE) are for predefined labels (`MetricLabel`)
 *
 * @generated from enum livekit.MetricLabel
 */
declare enum MetricLabel {
  /**
   * time to first token from LLM
   *
   * @generated from enum value: AGENTS_LLM_TTFT = 0;
   */
  AGENTS_LLM_TTFT = 0,

  /**
   * time to final transcription
   *
   * @generated from enum value: AGENTS_STT_TTFT = 1;
   */
  AGENTS_STT_TTFT = 1,

  /**
   * time to first byte
   *
   * @generated from enum value: AGENTS_TTS_TTFB = 2;
   */
  AGENTS_TTS_TTFB = 2,

  /**
   * Number of video freezes
   *
   * @generated from enum value: CLIENT_VIDEO_SUBSCRIBER_FREEZE_COUNT = 3;
   */
  CLIENT_VIDEO_SUBSCRIBER_FREEZE_COUNT = 3,

  /**
   * total duration of freezes
   *
   * @generated from enum value: CLIENT_VIDEO_SUBSCRIBER_TOTAL_FREEZE_DURATION = 4;
   */
  CLIENT_VIDEO_SUBSCRIBER_TOTAL_FREEZE_DURATION = 4,

  /**
   * number of video pauses
   *
   * @generated from enum value: CLIENT_VIDEO_SUBSCRIBER_PAUSE_COUNT = 5;
   */
  CLIENT_VIDEO_SUBSCRIBER_PAUSE_COUNT = 5,

  /**
   * total duration of pauses
   *
   * @generated from enum value: CLIENT_VIDEO_SUBSCRIBER_TOTAL_PAUSES_DURATION = 6;
   */
  CLIENT_VIDEO_SUBSCRIBER_TOTAL_PAUSES_DURATION = 6,

  /**
   * number of concealed (synthesized) audio samples
   *
   * @generated from enum value: CLIENT_AUDIO_SUBSCRIBER_CONCEALED_SAMPLES = 7;
   */
  CLIENT_AUDIO_SUBSCRIBER_CONCEALED_SAMPLES = 7,

  /**
   * number of silent concealed samples
   *
   * @generated from enum value: CLIENT_AUDIO_SUBSCRIBER_SILENT_CONCEALED_SAMPLES = 8;
   */
  CLIENT_AUDIO_SUBSCRIBER_SILENT_CONCEALED_SAMPLES = 8,

  /**
   * number of concealment events
   *
   * @generated from enum value: CLIENT_AUDIO_SUBSCRIBER_CONCEALMENT_EVENTS = 9;
   */
  CLIENT_AUDIO_SUBSCRIBER_CONCEALMENT_EVENTS = 9,

  /**
   * number of interruptions
   *
   * @generated from enum value: CLIENT_AUDIO_SUBSCRIBER_INTERRUPTION_COUNT = 10;
   */
  CLIENT_AUDIO_SUBSCRIBER_INTERRUPTION_COUNT = 10,

  /**
   * total duration of interruptions
   *
   * @generated from enum value: CLIENT_AUDIO_SUBSCRIBER_TOTAL_INTERRUPTION_DURATION = 11;
   */
  CLIENT_AUDIO_SUBSCRIBER_TOTAL_INTERRUPTION_DURATION = 11,

  /**
   * total time spent in jitter buffer
   *
   * @generated from enum value: CLIENT_SUBSCRIBER_JITTER_BUFFER_DELAY = 12;
   */
  CLIENT_SUBSCRIBER_JITTER_BUFFER_DELAY = 12,

  /**
   * total time spent in jitter buffer
   *
   * @generated from enum value: CLIENT_SUBSCRIBER_JITTER_BUFFER_EMITTED_COUNT = 13;
   */
  CLIENT_SUBSCRIBER_JITTER_BUFFER_EMITTED_COUNT = 13,

  /**
   * total duration spent in bandwidth quality limitation
   *
   * @generated from enum value: CLIENT_VIDEO_PUBLISHER_QUALITY_LIMITATION_DURATION_BANDWIDTH = 14;
   */
  CLIENT_VIDEO_PUBLISHER_QUALITY_LIMITATION_DURATION_BANDWIDTH = 14,

  /**
   * total duration spent in cpu quality limitation
   *
   * @generated from enum value: CLIENT_VIDEO_PUBLISHER_QUALITY_LIMITATION_DURATION_CPU = 15;
   */
  CLIENT_VIDEO_PUBLISHER_QUALITY_LIMITATION_DURATION_CPU = 15,

  /**
   * total duration spent in other quality limitation
   *
   * @generated from enum value: CLIENT_VIDEO_PUBLISHER_QUALITY_LIMITATION_DURATION_OTHER = 16;
   */
  CLIENT_VIDEO_PUBLISHER_QUALITY_LIMITATION_DURATION_OTHER = 16,

  /**
   * Publisher RTT (participant -> server)
   *
   * @generated from enum value: PUBLISHER_RTT = 17;
   */
  PUBLISHER_RTT = 17,

  /**
   * RTT between publisher node and subscriber node (could involve intermedia node(s))
   *
   * @generated from enum value: SERVER_MESH_RTT = 18;
   */
  SERVER_MESH_RTT = 18,

  /**
   * Subscribe RTT (server -> participant)
   *
   * @generated from enum value: SUBSCRIBER_RTT = 19;
   */
  SUBSCRIBER_RTT = 19,

  /**
   * @generated from enum value: METRIC_LABEL_PREDEFINED_MAX_VALUE = 4096;
   */
  METRIC_LABEL_PREDEFINED_MAX_VALUE = 4096,
}

/**
 * @generated from message livekit.MetricsBatch
 */
declare class MetricsBatch extends Message<MetricsBatch> {
  /**
   * time at which this batch is sent based on a monotonic clock (millisecond resolution)
   *
   * @generated from field: int64 timestamp_ms = 1;
   */
  timestampMs: bigint;

  /**
   * @generated from field: google.protobuf.Timestamp normalized_timestamp = 2;
   */
  normalizedTimestamp?: Timestamp;

  /**
   * To avoid repeating string values, we store them in a separate list and reference them by index
   * This is useful for storing participant identities, track names, etc.
   * There is also a predefined list of labels that can be used to reference common metrics.
   * They have reserved indices from 0 to (METRIC_LABEL_PREDEFINED_MAX_VALUE - 1).
   * Indexes pointing at str_data should start from METRIC_LABEL_PREDEFINED_MAX_VALUE, 
   * such that str_data[0] == index of METRIC_LABEL_PREDEFINED_MAX_VALUE.
   *
   * @generated from field: repeated string str_data = 3;
   */
  strData: string[];

  /**
   * @generated from field: repeated livekit.TimeSeriesMetric time_series = 4;
   */
  timeSeries: TimeSeriesMetric[];

  /**
   * @generated from field: repeated livekit.EventMetric events = 5;
   */
  events: EventMetric[];

  constructor(data?: PartialMessage<MetricsBatch>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.MetricsBatch";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): MetricsBatch;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): MetricsBatch;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): MetricsBatch;

  static equals(a: MetricsBatch | PlainMessage<MetricsBatch> | undefined, b: MetricsBatch | PlainMessage<MetricsBatch> | undefined): boolean;
}

/**
 * @generated from message livekit.TimeSeriesMetric
 */
declare class TimeSeriesMetric extends Message<TimeSeriesMetric> {
  /**
   * Metric name e.g "speech_probablity". The string value is not directly stored in the message, but referenced by index
   * in the `str_data` field of `MetricsBatch`
   *
   * @generated from field: uint32 label = 1;
   */
  label: number;

  /**
   * index into `str_data`
   *
   * @generated from field: uint32 participant_identity = 2;
   */
  participantIdentity: number;

  /**
   * index into `str_data`
   *
   * @generated from field: uint32 track_sid = 3;
   */
  trackSid: number;

  /**
   * @generated from field: repeated livekit.MetricSample samples = 4;
   */
  samples: MetricSample[];

  /**
   * index into 'str_data'
   *
   * @generated from field: uint32 rid = 5;
   */
  rid: number;

  constructor(data?: PartialMessage<TimeSeriesMetric>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.TimeSeriesMetric";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): TimeSeriesMetric;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): TimeSeriesMetric;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): TimeSeriesMetric;

  static equals(a: TimeSeriesMetric | PlainMessage<TimeSeriesMetric> | undefined, b: TimeSeriesMetric | PlainMessage<TimeSeriesMetric> | undefined): boolean;
}

/**
 * @generated from message livekit.MetricSample
 */
declare class MetricSample extends Message<MetricSample> {
  /**
   * time of metric based on a monotonic clock (in milliseconds)
   *
   * @generated from field: int64 timestamp_ms = 1;
   */
  timestampMs: bigint;

  /**
   * @generated from field: google.protobuf.Timestamp normalized_timestamp = 2;
   */
  normalizedTimestamp?: Timestamp;

  /**
   * @generated from field: float value = 3;
   */
  value: number;

  constructor(data?: PartialMessage<MetricSample>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.MetricSample";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): MetricSample;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): MetricSample;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): MetricSample;

  static equals(a: MetricSample | PlainMessage<MetricSample> | undefined, b: MetricSample | PlainMessage<MetricSample> | undefined): boolean;
}

/**
 * @generated from message livekit.EventMetric
 */
declare class EventMetric extends Message<EventMetric> {
  /**
   * @generated from field: uint32 label = 1;
   */
  label: number;

  /**
   * index into `str_data`
   *
   * @generated from field: uint32 participant_identity = 2;
   */
  participantIdentity: number;

  /**
   * index into `str_data`
   *
   * @generated from field: uint32 track_sid = 3;
   */
  trackSid: number;

  /**
   * start time of event based on a monotonic clock (in milliseconds)
   *
   * @generated from field: int64 start_timestamp_ms = 4;
   */
  startTimestampMs: bigint;

  /**
   * end time of event based on a monotonic clock (in milliseconds), if needed
   *
   * @generated from field: optional int64 end_timestamp_ms = 5;
   */
  endTimestampMs?: bigint;

  /**
   * @generated from field: google.protobuf.Timestamp normalized_start_timestamp = 6;
   */
  normalizedStartTimestamp?: Timestamp;

  /**
   * @generated from field: optional google.protobuf.Timestamp normalized_end_timestamp = 7;
   */
  normalizedEndTimestamp?: Timestamp;

  /**
   * @generated from field: string metadata = 8;
   */
  metadata: string;

  /**
   * index into 'str_data'
   *
   * @generated from field: uint32 rid = 9;
   */
  rid: number;

  constructor(data?: PartialMessage<EventMetric>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.EventMetric";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): EventMetric;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): EventMetric;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): EventMetric;

  static equals(a: EventMetric | PlainMessage<EventMetric> | undefined, b: EventMetric | PlainMessage<EventMetric> | undefined): boolean;
}

// Copyright 2023 LiveKit, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.



/**
 * @generated from enum livekit.AudioCodec
 */
declare enum AudioCodec {
  /**
   * @generated from enum value: DEFAULT_AC = 0;
   */
  DEFAULT_AC = 0,

  /**
   * @generated from enum value: OPUS = 1;
   */
  OPUS = 1,

  /**
   * @generated from enum value: AAC = 2;
   */
  AAC = 2,
}

/**
 * @generated from enum livekit.VideoCodec
 */
declare enum VideoCodec {
  /**
   * @generated from enum value: DEFAULT_VC = 0;
   */
  DEFAULT_VC = 0,

  /**
   * @generated from enum value: H264_BASELINE = 1;
   */
  H264_BASELINE = 1,

  /**
   * @generated from enum value: H264_MAIN = 2;
   */
  H264_MAIN = 2,

  /**
   * @generated from enum value: H264_HIGH = 3;
   */
  H264_HIGH = 3,

  /**
   * @generated from enum value: VP8 = 4;
   */
  VP8 = 4,
}

/**
 * @generated from enum livekit.ImageCodec
 */
declare enum ImageCodec {
  /**
   * @generated from enum value: IC_DEFAULT = 0;
   */
  IC_DEFAULT = 0,

  /**
   * @generated from enum value: IC_JPEG = 1;
   */
  IC_JPEG = 1,
}

/**
 * Policy for publisher to handle subscribers that are unable to support the primary codec of a track
 *
 * @generated from enum livekit.BackupCodecPolicy
 */
declare enum BackupCodecPolicy {
  /**
   * default behavior, the track prefer to regress to backup codec and all subscribers will receive the backup codec,
   * the sfu will try to regress codec if possible but not assured.
   *
   * @generated from enum value: PREFER_REGRESSION = 0;
   */
  PREFER_REGRESSION = 0,

  /**
   * encoding/send the primary and backup codec simultaneously
   *
   * @generated from enum value: SIMULCAST = 1;
   */
  SIMULCAST = 1,

  /**
   * force the track to regress to backup codec, this option can be used in video conference or the publisher has limited bandwidth/encoding power
   *
   * @generated from enum value: REGRESSION = 2;
   */
  REGRESSION = 2,
}

/**
 * @generated from enum livekit.TrackType
 */
declare enum TrackType {
  /**
   * @generated from enum value: AUDIO = 0;
   */
  AUDIO = 0,

  /**
   * @generated from enum value: VIDEO = 1;
   */
  VIDEO = 1,

  /**
   * @generated from enum value: DATA = 2;
   */
  DATA = 2,
}

/**
 * @generated from enum livekit.TrackSource
 */
declare enum TrackSource {
  /**
   * @generated from enum value: UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * @generated from enum value: CAMERA = 1;
   */
  CAMERA = 1,

  /**
   * @generated from enum value: MICROPHONE = 2;
   */
  MICROPHONE = 2,

  /**
   * @generated from enum value: SCREEN_SHARE = 3;
   */
  SCREEN_SHARE = 3,

  /**
   * @generated from enum value: SCREEN_SHARE_AUDIO = 4;
   */
  SCREEN_SHARE_AUDIO = 4,
}

/**
 * @generated from enum livekit.VideoQuality
 */
declare enum VideoQuality {
  /**
   * @generated from enum value: LOW = 0;
   */
  LOW = 0,

  /**
   * @generated from enum value: MEDIUM = 1;
   */
  MEDIUM = 1,

  /**
   * @generated from enum value: HIGH = 2;
   */
  HIGH = 2,

  /**
   * @generated from enum value: OFF = 3;
   */
  OFF = 3,
}

/**
 * @generated from enum livekit.ConnectionQuality
 */
declare enum ConnectionQuality {
  /**
   * @generated from enum value: POOR = 0;
   */
  POOR = 0,

  /**
   * @generated from enum value: GOOD = 1;
   */
  GOOD = 1,

  /**
   * @generated from enum value: EXCELLENT = 2;
   */
  EXCELLENT = 2,

  /**
   * @generated from enum value: LOST = 3;
   */
  LOST = 3,
}

/**
 * @generated from enum livekit.ClientConfigSetting
 */
declare enum ClientConfigSetting {
  /**
   * @generated from enum value: UNSET = 0;
   */
  UNSET = 0,

  /**
   * @generated from enum value: DISABLED = 1;
   */
  DISABLED = 1,

  /**
   * @generated from enum value: ENABLED = 2;
   */
  ENABLED = 2,
}

/**
 * @generated from enum livekit.DisconnectReason
 */
declare enum DisconnectReason {
  /**
   * @generated from enum value: UNKNOWN_REASON = 0;
   */
  UNKNOWN_REASON = 0,

  /**
   * the client initiated the disconnect
   *
   * @generated from enum value: CLIENT_INITIATED = 1;
   */
  CLIENT_INITIATED = 1,

  /**
   * another participant with the same identity has joined the room
   *
   * @generated from enum value: DUPLICATE_IDENTITY = 2;
   */
  DUPLICATE_IDENTITY = 2,

  /**
   * the server instance is shutting down
   *
   * @generated from enum value: SERVER_SHUTDOWN = 3;
   */
  SERVER_SHUTDOWN = 3,

  /**
   * RoomService.RemoveParticipant was called
   *
   * @generated from enum value: PARTICIPANT_REMOVED = 4;
   */
  PARTICIPANT_REMOVED = 4,

  /**
   * RoomService.DeleteRoom was called
   *
   * @generated from enum value: ROOM_DELETED = 5;
   */
  ROOM_DELETED = 5,

  /**
   * the client is attempting to resume a session, but server is not aware of it
   *
   * @generated from enum value: STATE_MISMATCH = 6;
   */
  STATE_MISMATCH = 6,

  /**
   * client was unable to connect fully
   *
   * @generated from enum value: JOIN_FAILURE = 7;
   */
  JOIN_FAILURE = 7,

  /**
   * Cloud-only, the server requested Participant to migrate the connection elsewhere
   *
   * @generated from enum value: MIGRATION = 8;
   */
  MIGRATION = 8,

  /**
   * the signal websocket was closed unexpectedly
   *
   * @generated from enum value: SIGNAL_CLOSE = 9;
   */
  SIGNAL_CLOSE = 9,

  /**
   * the room was closed, due to all Standard and Ingress participants having left
   *
   * @generated from enum value: ROOM_CLOSED = 10;
   */
  ROOM_CLOSED = 10,

  /**
   * SIP callee did not respond in time
   *
   * @generated from enum value: USER_UNAVAILABLE = 11;
   */
  USER_UNAVAILABLE = 11,

  /**
   * SIP callee rejected the call (busy)
   *
   * @generated from enum value: USER_REJECTED = 12;
   */
  USER_REJECTED = 12,

  /**
   * SIP protocol failure or unexpected response
   *
   * @generated from enum value: SIP_TRUNK_FAILURE = 13;
   */
  SIP_TRUNK_FAILURE = 13,

  /**
   * server timed out a participant session
   *
   * @generated from enum value: CONNECTION_TIMEOUT = 14;
   */
  CONNECTION_TIMEOUT = 14,

  /**
   * media stream failure or media timeout
   *
   * @generated from enum value: MEDIA_FAILURE = 15;
   */
  MEDIA_FAILURE = 15,
}

/**
 * @generated from enum livekit.ReconnectReason
 */
declare enum ReconnectReason {
  /**
   * @generated from enum value: RR_UNKNOWN = 0;
   */
  RR_UNKNOWN = 0,

  /**
   * @generated from enum value: RR_SIGNAL_DISCONNECTED = 1;
   */
  RR_SIGNAL_DISCONNECTED = 1,

  /**
   * @generated from enum value: RR_PUBLISHER_FAILED = 2;
   */
  RR_PUBLISHER_FAILED = 2,

  /**
   * @generated from enum value: RR_SUBSCRIBER_FAILED = 3;
   */
  RR_SUBSCRIBER_FAILED = 3,

  /**
   * @generated from enum value: RR_SWITCH_CANDIDATE = 4;
   */
  RR_SWITCH_CANDIDATE = 4,
}

/**
 * @generated from enum livekit.SubscriptionError
 */
declare enum SubscriptionError {
  /**
   * @generated from enum value: SE_UNKNOWN = 0;
   */
  SE_UNKNOWN = 0,

  /**
   * @generated from enum value: SE_CODEC_UNSUPPORTED = 1;
   */
  SE_CODEC_UNSUPPORTED = 1,

  /**
   * @generated from enum value: SE_TRACK_NOTFOUND = 2;
   */
  SE_TRACK_NOTFOUND = 2,
}

/**
 * @generated from enum livekit.AudioTrackFeature
 */
declare enum AudioTrackFeature {
  /**
   * @generated from enum value: TF_STEREO = 0;
   */
  TF_STEREO = 0,

  /**
   * @generated from enum value: TF_NO_DTX = 1;
   */
  TF_NO_DTX = 1,

  /**
   * @generated from enum value: TF_AUTO_GAIN_CONTROL = 2;
   */
  TF_AUTO_GAIN_CONTROL = 2,

  /**
   * @generated from enum value: TF_ECHO_CANCELLATION = 3;
   */
  TF_ECHO_CANCELLATION = 3,

  /**
   * @generated from enum value: TF_NOISE_SUPPRESSION = 4;
   */
  TF_NOISE_SUPPRESSION = 4,

  /**
   * @generated from enum value: TF_ENHANCED_NOISE_CANCELLATION = 5;
   */
  TF_ENHANCED_NOISE_CANCELLATION = 5,

  /**
   * client will buffer audio once available and send it to the server via bytes stream once connected
   *
   * @generated from enum value: TF_PRECONNECT_BUFFER = 6;
   */
  TF_PRECONNECT_BUFFER = 6,
}

/**
 * @generated from message livekit.Pagination
 */
declare class Pagination extends Message<Pagination> {
  /**
   * list entities which IDs are greater
   *
   * @generated from field: string after_id = 1;
   */
  afterId: string;

  /**
   * @generated from field: int32 limit = 2;
   */
  limit: number;

  constructor(data?: PartialMessage<Pagination>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.Pagination";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Pagination;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Pagination;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Pagination;

  static equals(a: Pagination | PlainMessage<Pagination> | undefined, b: Pagination | PlainMessage<Pagination> | undefined): boolean;
}

/**
 * ListUpdate is used for updated APIs where 'repeated string' field is modified.
 *
 * @generated from message livekit.ListUpdate
 */
declare class ListUpdate extends Message<ListUpdate> {
  /**
   * set the field to a new list
   *
   * @generated from field: repeated string set = 1;
   */
  set: string[];

  constructor(data?: PartialMessage<ListUpdate>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.ListUpdate";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListUpdate;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListUpdate;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListUpdate;

  static equals(a: ListUpdate | PlainMessage<ListUpdate> | undefined, b: ListUpdate | PlainMessage<ListUpdate> | undefined): boolean;
}

/**
 * @generated from message livekit.Room
 */
declare class Room extends Message<Room> {
  /**
   * @generated from field: string sid = 1;
   */
  sid: string;

  /**
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * @generated from field: uint32 empty_timeout = 3;
   */
  emptyTimeout: number;

  /**
   * @generated from field: uint32 departure_timeout = 14;
   */
  departureTimeout: number;

  /**
   * @generated from field: uint32 max_participants = 4;
   */
  maxParticipants: number;

  /**
   * @generated from field: int64 creation_time = 5;
   */
  creationTime: bigint;

  /**
   * @generated from field: int64 creation_time_ms = 15;
   */
  creationTimeMs: bigint;

  /**
   * @generated from field: string turn_password = 6;
   */
  turnPassword: string;

  /**
   * @generated from field: repeated livekit.Codec enabled_codecs = 7;
   */
  enabledCodecs: Codec[];

  /**
   * @generated from field: string metadata = 8;
   */
  metadata: string;

  /**
   * @generated from field: uint32 num_participants = 9;
   */
  numParticipants: number;

  /**
   * @generated from field: uint32 num_publishers = 11;
   */
  numPublishers: number;

  /**
   * @generated from field: bool active_recording = 10;
   */
  activeRecording: boolean;

  /**
   * @generated from field: livekit.TimedVersion version = 13;
   */
  version?: TimedVersion;

  constructor(data?: PartialMessage<Room>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.Room";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Room;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Room;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Room;

  static equals(a: Room | PlainMessage<Room> | undefined, b: Room | PlainMessage<Room> | undefined): boolean;
}

/**
 * @generated from message livekit.Codec
 */
declare class Codec extends Message<Codec> {
  /**
   * @generated from field: string mime = 1;
   */
  mime: string;

  /**
   * @generated from field: string fmtp_line = 2;
   */
  fmtpLine: string;

  constructor(data?: PartialMessage<Codec>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.Codec";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Codec;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Codec;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Codec;

  static equals(a: Codec | PlainMessage<Codec> | undefined, b: Codec | PlainMessage<Codec> | undefined): boolean;
}

/**
 * @generated from message livekit.PlayoutDelay
 */
declare class PlayoutDelay extends Message<PlayoutDelay> {
  /**
   * @generated from field: bool enabled = 1;
   */
  enabled: boolean;

  /**
   * @generated from field: uint32 min = 2;
   */
  min: number;

  /**
   * @generated from field: uint32 max = 3;
   */
  max: number;

  constructor(data?: PartialMessage<PlayoutDelay>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.PlayoutDelay";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PlayoutDelay;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PlayoutDelay;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PlayoutDelay;

  static equals(a: PlayoutDelay | PlainMessage<PlayoutDelay> | undefined, b: PlayoutDelay | PlainMessage<PlayoutDelay> | undefined): boolean;
}

/**
 * @generated from message livekit.ParticipantPermission
 */
declare class ParticipantPermission extends Message<ParticipantPermission> {
  /**
   * allow participant to subscribe to other tracks in the room
   *
   * @generated from field: bool can_subscribe = 1;
   */
  canSubscribe: boolean;

  /**
   * allow participant to publish new tracks to room
   *
   * @generated from field: bool can_publish = 2;
   */
  canPublish: boolean;

  /**
   * allow participant to publish data
   *
   * @generated from field: bool can_publish_data = 3;
   */
  canPublishData: boolean;

  /**
   * sources that are allowed to be published
   *
   * @generated from field: repeated livekit.TrackSource can_publish_sources = 9;
   */
  canPublishSources: TrackSource[];

  /**
   * indicates that it's hidden to others
   *
   * @generated from field: bool hidden = 7;
   */
  hidden: boolean;

  /**
   * indicates it's a recorder instance
   * deprecated: use ParticipantInfo.kind instead
   *
   * @generated from field: bool recorder = 8 [deprecated = true];
   * @deprecated
   */
  recorder: boolean;

  /**
   * indicates that participant can update own metadata and attributes
   *
   * @generated from field: bool can_update_metadata = 10;
   */
  canUpdateMetadata: boolean;

  /**
   * indicates that participant is an agent
   * deprecated: use ParticipantInfo.kind instead
   *
   * @generated from field: bool agent = 11 [deprecated = true];
   * @deprecated
   */
  agent: boolean;

  /**
   * if a participant can subscribe to metrics
   *
   * @generated from field: bool can_subscribe_metrics = 12;
   */
  canSubscribeMetrics: boolean;

  constructor(data?: PartialMessage<ParticipantPermission>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.ParticipantPermission";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ParticipantPermission;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ParticipantPermission;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ParticipantPermission;

  static equals(a: ParticipantPermission | PlainMessage<ParticipantPermission> | undefined, b: ParticipantPermission | PlainMessage<ParticipantPermission> | undefined): boolean;
}

/**
 * @generated from message livekit.ParticipantInfo
 */
declare class ParticipantInfo extends Message<ParticipantInfo> {
  /**
   * @generated from field: string sid = 1;
   */
  sid: string;

  /**
   * @generated from field: string identity = 2;
   */
  identity: string;

  /**
   * @generated from field: livekit.ParticipantInfo.State state = 3;
   */
  state: ParticipantInfo_State;

  /**
   * @generated from field: repeated livekit.TrackInfo tracks = 4;
   */
  tracks: TrackInfo[];

  /**
   * @generated from field: string metadata = 5;
   */
  metadata: string;

  /**
   * timestamp when participant joined room, in seconds
   *
   * @generated from field: int64 joined_at = 6;
   */
  joinedAt: bigint;

  /**
   * timestamp when participant joined room, in milliseconds
   *
   * @generated from field: int64 joined_at_ms = 17;
   */
  joinedAtMs: bigint;

  /**
   * @generated from field: string name = 9;
   */
  name: string;

  /**
   * @generated from field: uint32 version = 10;
   */
  version: number;

  /**
   * @generated from field: livekit.ParticipantPermission permission = 11;
   */
  permission?: ParticipantPermission;

  /**
   * @generated from field: string region = 12;
   */
  region: string;

  /**
   * indicates the participant has an active publisher connection
   * and can publish to the server
   *
   * @generated from field: bool is_publisher = 13;
   */
  isPublisher: boolean;

  /**
   * @generated from field: livekit.ParticipantInfo.Kind kind = 14;
   */
  kind: ParticipantInfo_Kind;

  /**
   * @generated from field: map<string, string> attributes = 15;
   */
  attributes: { [key: string]: string };

  /**
   * @generated from field: livekit.DisconnectReason disconnect_reason = 16;
   */
  disconnectReason: DisconnectReason;

  /**
   * @generated from field: repeated livekit.ParticipantInfo.KindDetail kind_details = 18;
   */
  kindDetails: ParticipantInfo_KindDetail[];

  constructor(data?: PartialMessage<ParticipantInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.ParticipantInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ParticipantInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ParticipantInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ParticipantInfo;

  static equals(a: ParticipantInfo | PlainMessage<ParticipantInfo> | undefined, b: ParticipantInfo | PlainMessage<ParticipantInfo> | undefined): boolean;
}

/**
 * @generated from enum livekit.ParticipantInfo.State
 */
declare enum ParticipantInfo_State {
  /**
   * websocket' connected, but not offered yet
   *
   * @generated from enum value: JOINING = 0;
   */
  JOINING = 0,

  /**
   * server received client offer
   *
   * @generated from enum value: JOINED = 1;
   */
  JOINED = 1,

  /**
   * ICE connectivity established
   *
   * @generated from enum value: ACTIVE = 2;
   */
  ACTIVE = 2,

  /**
   * WS disconnected
   *
   * @generated from enum value: DISCONNECTED = 3;
   */
  DISCONNECTED = 3,
}

/**
 * @generated from enum livekit.ParticipantInfo.Kind
 */
declare enum ParticipantInfo_Kind {
  /**
   * standard participants, e.g. web clients
   *
   * @generated from enum value: STANDARD = 0;
   */
  STANDARD = 0,

  /**
   * only ingests streams
   *
   * @generated from enum value: INGRESS = 1;
   */
  INGRESS = 1,

  /**
   * only consumes streams
   *
   * @generated from enum value: EGRESS = 2;
   */
  EGRESS = 2,

  /**
   * SIP participants
   *
   * @generated from enum value: SIP = 3;
   */
  SIP = 3,

  /**
   * LiveKit agents
   *
   * @generated from enum value: AGENT = 4;
   */
  AGENT = 4,
}

/**
 * @generated from enum livekit.ParticipantInfo.KindDetail
 */
declare enum ParticipantInfo_KindDetail {
  /**
   * @generated from enum value: CLOUD_AGENT = 0;
   */
  CLOUD_AGENT = 0,

  /**
   * @generated from enum value: FORWARDED = 1;
   */
  FORWARDED = 1,
}

/**
 * @generated from message livekit.Encryption
 */
declare class Encryption extends Message<Encryption> {
  constructor(data?: PartialMessage<Encryption>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.Encryption";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Encryption;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Encryption;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Encryption;

  static equals(a: Encryption | PlainMessage<Encryption> | undefined, b: Encryption | PlainMessage<Encryption> | undefined): boolean;
}

/**
 * @generated from enum livekit.Encryption.Type
 */
declare enum Encryption_Type {
  /**
   * @generated from enum value: NONE = 0;
   */
  NONE = 0,

  /**
   * @generated from enum value: GCM = 1;
   */
  GCM = 1,

  /**
   * @generated from enum value: CUSTOM = 2;
   */
  CUSTOM = 2,
}

/**
 * @generated from message livekit.SimulcastCodecInfo
 */
declare class SimulcastCodecInfo extends Message<SimulcastCodecInfo> {
  /**
   * @generated from field: string mime_type = 1;
   */
  mimeType: string;

  /**
   * @generated from field: string mid = 2;
   */
  mid: string;

  /**
   * @generated from field: string cid = 3;
   */
  cid: string;

  /**
   * @generated from field: repeated livekit.VideoLayer layers = 4;
   */
  layers: VideoLayer[];

  constructor(data?: PartialMessage<SimulcastCodecInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.SimulcastCodecInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SimulcastCodecInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SimulcastCodecInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SimulcastCodecInfo;

  static equals(a: SimulcastCodecInfo | PlainMessage<SimulcastCodecInfo> | undefined, b: SimulcastCodecInfo | PlainMessage<SimulcastCodecInfo> | undefined): boolean;
}

/**
 * @generated from message livekit.TrackInfo
 */
declare class TrackInfo extends Message<TrackInfo> {
  /**
   * @generated from field: string sid = 1;
   */
  sid: string;

  /**
   * @generated from field: livekit.TrackType type = 2;
   */
  type: TrackType;

  /**
   * @generated from field: string name = 3;
   */
  name: string;

  /**
   * @generated from field: bool muted = 4;
   */
  muted: boolean;

  /**
   * original width of video (unset for audio)
   * clients may receive a lower resolution version with simulcast
   *
   * @generated from field: uint32 width = 5;
   */
  width: number;

  /**
   * original height of video (unset for audio)
   *
   * @generated from field: uint32 height = 6;
   */
  height: number;

  /**
   * true if track is simulcasted
   *
   * @generated from field: bool simulcast = 7;
   */
  simulcast: boolean;

  /**
   * true if DTX (Discontinuous Transmission) is disabled for audio
   *
   * @generated from field: bool disable_dtx = 8;
   */
  disableDtx: boolean;

  /**
   * source of media
   *
   * @generated from field: livekit.TrackSource source = 9;
   */
  source: TrackSource;

  /**
   * @generated from field: repeated livekit.VideoLayer layers = 10;
   */
  layers: VideoLayer[];

  /**
   * mime type of codec
   *
   * @generated from field: string mime_type = 11;
   */
  mimeType: string;

  /**
   * @generated from field: string mid = 12;
   */
  mid: string;

  /**
   * @generated from field: repeated livekit.SimulcastCodecInfo codecs = 13;
   */
  codecs: SimulcastCodecInfo[];

  /**
   * @generated from field: bool stereo = 14;
   */
  stereo: boolean;

  /**
   * true if RED (Redundant Encoding) is disabled for audio
   *
   * @generated from field: bool disable_red = 15;
   */
  disableRed: boolean;

  /**
   * @generated from field: livekit.Encryption.Type encryption = 16;
   */
  encryption: Encryption_Type;

  /**
   * @generated from field: string stream = 17;
   */
  stream: string;

  /**
   * @generated from field: livekit.TimedVersion version = 18;
   */
  version?: TimedVersion;

  /**
   * @generated from field: repeated livekit.AudioTrackFeature audio_features = 19;
   */
  audioFeatures: AudioTrackFeature[];

  /**
   * @generated from field: livekit.BackupCodecPolicy backup_codec_policy = 20;
   */
  backupCodecPolicy: BackupCodecPolicy;

  constructor(data?: PartialMessage<TrackInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.TrackInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): TrackInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): TrackInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): TrackInfo;

  static equals(a: TrackInfo | PlainMessage<TrackInfo> | undefined, b: TrackInfo | PlainMessage<TrackInfo> | undefined): boolean;
}

/**
 * provide information about available spatial layers
 *
 * @generated from message livekit.VideoLayer
 */
declare class VideoLayer extends Message<VideoLayer> {
  /**
   * for tracks with a single layer, this should be HIGH
   *
   * @generated from field: livekit.VideoQuality quality = 1;
   */
  quality: VideoQuality;

  /**
   * @generated from field: uint32 width = 2;
   */
  width: number;

  /**
   * @generated from field: uint32 height = 3;
   */
  height: number;

  /**
   * target bitrate in bit per second (bps), server will measure actual
   *
   * @generated from field: uint32 bitrate = 4;
   */
  bitrate: number;

  /**
   * @generated from field: uint32 ssrc = 5;
   */
  ssrc: number;

  /**
   * @generated from field: int32 spatial_layer = 6;
   */
  spatialLayer: number;

  /**
   * @generated from field: string rid = 7;
   */
  rid: string;

  constructor(data?: PartialMessage<VideoLayer>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.VideoLayer";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): VideoLayer;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): VideoLayer;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): VideoLayer;

  static equals(a: VideoLayer | PlainMessage<VideoLayer> | undefined, b: VideoLayer | PlainMessage<VideoLayer> | undefined): boolean;
}

/**
 * new DataPacket API
 *
 * @generated from message livekit.DataPacket
 */
declare class DataPacket extends Message<DataPacket> {
  /**
   * @generated from field: livekit.DataPacket.Kind kind = 1 [deprecated = true];
   * @deprecated
   */
  kind: DataPacket_Kind;

  /**
   * participant identity of user that sent the message
   *
   * @generated from field: string participant_identity = 4;
   */
  participantIdentity: string;

  /**
   * identities of participants who will receive the message (sent to all by default)
   *
   * @generated from field: repeated string destination_identities = 5;
   */
  destinationIdentities: string[];

  /**
   * @generated from oneof livekit.DataPacket.value
   */
  value: {
    /**
     * @generated from field: livekit.UserPacket user = 2;
     */
    value: UserPacket;
    case: "user";
  } | {
    /**
     * @generated from field: livekit.ActiveSpeakerUpdate speaker = 3 [deprecated = true];
     * @deprecated
     */
    value: ActiveSpeakerUpdate;
    case: "speaker";
  } | {
    /**
     * @generated from field: livekit.SipDTMF sip_dtmf = 6;
     */
    value: SipDTMF;
    case: "sipDtmf";
  } | {
    /**
     * @generated from field: livekit.Transcription transcription = 7;
     */
    value: Transcription;
    case: "transcription";
  } | {
    /**
     * @generated from field: livekit.MetricsBatch metrics = 8;
     */
    value: MetricsBatch;
    case: "metrics";
  } | {
    /**
     * @generated from field: livekit.ChatMessage chat_message = 9;
     */
    value: ChatMessage;
    case: "chatMessage";
  } | {
    /**
     * @generated from field: livekit.RpcRequest rpc_request = 10;
     */
    value: RpcRequest;
    case: "rpcRequest";
  } | {
    /**
     * @generated from field: livekit.RpcAck rpc_ack = 11;
     */
    value: RpcAck;
    case: "rpcAck";
  } | {
    /**
     * @generated from field: livekit.RpcResponse rpc_response = 12;
     */
    value: RpcResponse;
    case: "rpcResponse";
  } | {
    /**
     * @generated from field: livekit.DataStream.Header stream_header = 13;
     */
    value: DataStream_Header;
    case: "streamHeader";
  } | {
    /**
     * @generated from field: livekit.DataStream.Chunk stream_chunk = 14;
     */
    value: DataStream_Chunk;
    case: "streamChunk";
  } | {
    /**
     * @generated from field: livekit.DataStream.Trailer stream_trailer = 15;
     */
    value: DataStream_Trailer;
    case: "streamTrailer";
  } | { case: undefined; value?: undefined };

  /**
   * sequence number of reliable packet
   *
   * @generated from field: uint32 sequence = 16;
   */
  sequence: number;

  /**
   * sid of the user that sent the message
   *
   * @generated from field: string participant_sid = 17;
   */
  participantSid: string;

  constructor(data?: PartialMessage<DataPacket>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.DataPacket";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DataPacket;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DataPacket;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DataPacket;

  static equals(a: DataPacket | PlainMessage<DataPacket> | undefined, b: DataPacket | PlainMessage<DataPacket> | undefined): boolean;
}

/**
 * @generated from enum livekit.DataPacket.Kind
 */
declare enum DataPacket_Kind {
  /**
   * @generated from enum value: RELIABLE = 0;
   */
  RELIABLE = 0,

  /**
   * @generated from enum value: LOSSY = 1;
   */
  LOSSY = 1,
}

/**
 * @generated from message livekit.ActiveSpeakerUpdate
 */
declare class ActiveSpeakerUpdate extends Message<ActiveSpeakerUpdate> {
  /**
   * @generated from field: repeated livekit.SpeakerInfo speakers = 1;
   */
  speakers: SpeakerInfo[];

  constructor(data?: PartialMessage<ActiveSpeakerUpdate>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.ActiveSpeakerUpdate";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ActiveSpeakerUpdate;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ActiveSpeakerUpdate;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ActiveSpeakerUpdate;

  static equals(a: ActiveSpeakerUpdate | PlainMessage<ActiveSpeakerUpdate> | undefined, b: ActiveSpeakerUpdate | PlainMessage<ActiveSpeakerUpdate> | undefined): boolean;
}

/**
 * @generated from message livekit.SpeakerInfo
 */
declare class SpeakerInfo extends Message<SpeakerInfo> {
  /**
   * @generated from field: string sid = 1;
   */
  sid: string;

  /**
   * audio level, 0-1.0, 1 is loudest
   *
   * @generated from field: float level = 2;
   */
  level: number;

  /**
   * true if speaker is currently active
   *
   * @generated from field: bool active = 3;
   */
  active: boolean;

  constructor(data?: PartialMessage<SpeakerInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.SpeakerInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SpeakerInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SpeakerInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SpeakerInfo;

  static equals(a: SpeakerInfo | PlainMessage<SpeakerInfo> | undefined, b: SpeakerInfo | PlainMessage<SpeakerInfo> | undefined): boolean;
}

/**
 * @generated from message livekit.UserPacket
 */
declare class UserPacket extends Message<UserPacket> {
  /**
   * participant ID of user that sent the message
   *
   * @generated from field: string participant_sid = 1 [deprecated = true];
   * @deprecated
   */
  participantSid: string;

  /**
   * @generated from field: string participant_identity = 5 [deprecated = true];
   * @deprecated
   */
  participantIdentity: string;

  /**
   * user defined payload
   *
   * @generated from field: bytes payload = 2;
   */
  payload: Uint8Array;

  /**
   * the ID of the participants who will receive the message (sent to all by default)
   *
   * @generated from field: repeated string destination_sids = 3 [deprecated = true];
   * @deprecated
   */
  destinationSids: string[];

  /**
   * identities of participants who will receive the message (sent to all by default)
   *
   * @generated from field: repeated string destination_identities = 6 [deprecated = true];
   * @deprecated
   */
  destinationIdentities: string[];

  /**
   * topic under which the message was published
   *
   * @generated from field: optional string topic = 4;
   */
  topic?: string;

  /**
   * Unique ID to indentify the message
   *
   * @generated from field: optional string id = 8;
   */
  id?: string;

  /**
   * start and end time allow relating the message to specific media time
   *
   * @generated from field: optional uint64 start_time = 9;
   */
  startTime?: bigint;

  /**
   * @generated from field: optional uint64 end_time = 10;
   */
  endTime?: bigint;

  /**
   * added by SDK to enable de-duping of messages, for INTERNAL USE ONLY
   *
   * @generated from field: bytes nonce = 11;
   */
  nonce: Uint8Array;

  constructor(data?: PartialMessage<UserPacket>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.UserPacket";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UserPacket;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UserPacket;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UserPacket;

  static equals(a: UserPacket | PlainMessage<UserPacket> | undefined, b: UserPacket | PlainMessage<UserPacket> | undefined): boolean;
}

/**
 * @generated from message livekit.SipDTMF
 */
declare class SipDTMF extends Message<SipDTMF> {
  /**
   * @generated from field: uint32 code = 3;
   */
  code: number;

  /**
   * @generated from field: string digit = 4;
   */
  digit: string;

  constructor(data?: PartialMessage<SipDTMF>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.SipDTMF";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SipDTMF;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SipDTMF;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SipDTMF;

  static equals(a: SipDTMF | PlainMessage<SipDTMF> | undefined, b: SipDTMF | PlainMessage<SipDTMF> | undefined): boolean;
}

/**
 * @generated from message livekit.Transcription
 */
declare class Transcription extends Message<Transcription> {
  /**
   * Participant that got its speech transcribed
   *
   * @generated from field: string transcribed_participant_identity = 2;
   */
  transcribedParticipantIdentity: string;

  /**
   * @generated from field: string track_id = 3;
   */
  trackId: string;

  /**
   * @generated from field: repeated livekit.TranscriptionSegment segments = 4;
   */
  segments: TranscriptionSegment[];

  constructor(data?: PartialMessage<Transcription>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.Transcription";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Transcription;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Transcription;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Transcription;

  static equals(a: Transcription | PlainMessage<Transcription> | undefined, b: Transcription | PlainMessage<Transcription> | undefined): boolean;
}

/**
 * @generated from message livekit.TranscriptionSegment
 */
declare class TranscriptionSegment extends Message<TranscriptionSegment> {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: string text = 2;
   */
  text: string;

  /**
   * @generated from field: uint64 start_time = 3;
   */
  startTime: bigint;

  /**
   * @generated from field: uint64 end_time = 4;
   */
  endTime: bigint;

  /**
   * @generated from field: bool final = 5;
   */
  final: boolean;

  /**
   * @generated from field: string language = 6;
   */
  language: string;

  constructor(data?: PartialMessage<TranscriptionSegment>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.TranscriptionSegment";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): TranscriptionSegment;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): TranscriptionSegment;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): TranscriptionSegment;

  static equals(a: TranscriptionSegment | PlainMessage<TranscriptionSegment> | undefined, b: TranscriptionSegment | PlainMessage<TranscriptionSegment> | undefined): boolean;
}

/**
 * @generated from message livekit.ChatMessage
 */
declare class ChatMessage extends Message<ChatMessage> {
  /**
   * uuid
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: int64 timestamp = 2;
   */
  timestamp: bigint;

  /**
   * populated only if the intent is to edit/update an existing message
   *
   * @generated from field: optional int64 edit_timestamp = 3;
   */
  editTimestamp?: bigint;

  /**
   * @generated from field: string message = 4;
   */
  message: string;

  /**
   * true to remove message
   *
   * @generated from field: bool deleted = 5;
   */
  deleted: boolean;

  /**
   * true if the chat message has been generated by an agent from a participant's audio transcription
   *
   * @generated from field: bool generated = 6;
   */
  generated: boolean;

  constructor(data?: PartialMessage<ChatMessage>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.ChatMessage";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ChatMessage;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ChatMessage;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ChatMessage;

  static equals(a: ChatMessage | PlainMessage<ChatMessage> | undefined, b: ChatMessage | PlainMessage<ChatMessage> | undefined): boolean;
}

/**
 * @generated from message livekit.RpcRequest
 */
declare class RpcRequest extends Message<RpcRequest> {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: string method = 2;
   */
  method: string;

  /**
   * @generated from field: string payload = 3;
   */
  payload: string;

  /**
   * @generated from field: uint32 response_timeout_ms = 4;
   */
  responseTimeoutMs: number;

  /**
   * @generated from field: uint32 version = 5;
   */
  version: number;

  constructor(data?: PartialMessage<RpcRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.RpcRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RpcRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RpcRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RpcRequest;

  static equals(a: RpcRequest | PlainMessage<RpcRequest> | undefined, b: RpcRequest | PlainMessage<RpcRequest> | undefined): boolean;
}

/**
 * @generated from message livekit.RpcAck
 */
declare class RpcAck extends Message<RpcAck> {
  /**
   * @generated from field: string request_id = 1;
   */
  requestId: string;

  constructor(data?: PartialMessage<RpcAck>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.RpcAck";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RpcAck;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RpcAck;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RpcAck;

  static equals(a: RpcAck | PlainMessage<RpcAck> | undefined, b: RpcAck | PlainMessage<RpcAck> | undefined): boolean;
}

/**
 * @generated from message livekit.RpcResponse
 */
declare class RpcResponse extends Message<RpcResponse> {
  /**
   * @generated from field: string request_id = 1;
   */
  requestId: string;

  /**
   * @generated from oneof livekit.RpcResponse.value
   */
  value: {
    /**
     * @generated from field: string payload = 2;
     */
    value: string;
    case: "payload";
  } | {
    /**
     * @generated from field: livekit.RpcError error = 3;
     */
    value: RpcError;
    case: "error";
  } | { case: undefined; value?: undefined };

  constructor(data?: PartialMessage<RpcResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.RpcResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RpcResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RpcResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RpcResponse;

  static equals(a: RpcResponse | PlainMessage<RpcResponse> | undefined, b: RpcResponse | PlainMessage<RpcResponse> | undefined): boolean;
}

/**
 * @generated from message livekit.RpcError
 */
declare class RpcError extends Message<RpcError> {
  /**
   * @generated from field: uint32 code = 1;
   */
  code: number;

  /**
   * @generated from field: string message = 2;
   */
  message: string;

  /**
   * @generated from field: string data = 3;
   */
  data: string;

  constructor(data?: PartialMessage<RpcError>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.RpcError";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RpcError;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RpcError;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RpcError;

  static equals(a: RpcError | PlainMessage<RpcError> | undefined, b: RpcError | PlainMessage<RpcError> | undefined): boolean;
}

/**
 * @generated from message livekit.ParticipantTracks
 */
declare class ParticipantTracks extends Message<ParticipantTracks> {
  /**
   * participant ID of participant to whom the tracks belong
   *
   * @generated from field: string participant_sid = 1;
   */
  participantSid: string;

  /**
   * @generated from field: repeated string track_sids = 2;
   */
  trackSids: string[];

  constructor(data?: PartialMessage<ParticipantTracks>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.ParticipantTracks";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ParticipantTracks;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ParticipantTracks;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ParticipantTracks;

  static equals(a: ParticipantTracks | PlainMessage<ParticipantTracks> | undefined, b: ParticipantTracks | PlainMessage<ParticipantTracks> | undefined): boolean;
}

/**
 * details about the server
 *
 * @generated from message livekit.ServerInfo
 */
declare class ServerInfo extends Message<ServerInfo> {
  /**
   * @generated from field: livekit.ServerInfo.Edition edition = 1;
   */
  edition: ServerInfo_Edition;

  /**
   * @generated from field: string version = 2;
   */
  version: string;

  /**
   * @generated from field: int32 protocol = 3;
   */
  protocol: number;

  /**
   * @generated from field: string region = 4;
   */
  region: string;

  /**
   * @generated from field: string node_id = 5;
   */
  nodeId: string;

  /**
   * additional debugging information. sent only if server is in development mode
   *
   * @generated from field: string debug_info = 6;
   */
  debugInfo: string;

  /**
   * @generated from field: int32 agent_protocol = 7;
   */
  agentProtocol: number;

  constructor(data?: PartialMessage<ServerInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.ServerInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ServerInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ServerInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ServerInfo;

  static equals(a: ServerInfo | PlainMessage<ServerInfo> | undefined, b: ServerInfo | PlainMessage<ServerInfo> | undefined): boolean;
}

/**
 * @generated from enum livekit.ServerInfo.Edition
 */
declare enum ServerInfo_Edition {
  /**
   * @generated from enum value: Standard = 0;
   */
  Standard = 0,

  /**
   * @generated from enum value: Cloud = 1;
   */
  Cloud = 1,
}

/**
 * details about the client
 *
 * @generated from message livekit.ClientInfo
 */
declare class ClientInfo extends Message<ClientInfo> {
  /**
   * @generated from field: livekit.ClientInfo.SDK sdk = 1;
   */
  sdk: ClientInfo_SDK;

  /**
   * @generated from field: string version = 2;
   */
  version: string;

  /**
   * @generated from field: int32 protocol = 3;
   */
  protocol: number;

  /**
   * @generated from field: string os = 4;
   */
  os: string;

  /**
   * @generated from field: string os_version = 5;
   */
  osVersion: string;

  /**
   * @generated from field: string device_model = 6;
   */
  deviceModel: string;

  /**
   * @generated from field: string browser = 7;
   */
  browser: string;

  /**
   * @generated from field: string browser_version = 8;
   */
  browserVersion: string;

  /**
   * @generated from field: string address = 9;
   */
  address: string;

  /**
   * wifi, wired, cellular, vpn, empty if not known
   *
   * @generated from field: string network = 10;
   */
  network: string;

  /**
   * comma separated list of additional LiveKit SDKs in use of this client, with versions
   * e.g. "components-js:1.2.3,track-processors-js:1.2.3"
   *
   * @generated from field: string other_sdks = 11;
   */
  otherSdks: string;

  constructor(data?: PartialMessage<ClientInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.ClientInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ClientInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ClientInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ClientInfo;

  static equals(a: ClientInfo | PlainMessage<ClientInfo> | undefined, b: ClientInfo | PlainMessage<ClientInfo> | undefined): boolean;
}

/**
 * @generated from enum livekit.ClientInfo.SDK
 */
declare enum ClientInfo_SDK {
  /**
   * @generated from enum value: UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * @generated from enum value: JS = 1;
   */
  JS = 1,

  /**
   * @generated from enum value: SWIFT = 2;
   */
  SWIFT = 2,

  /**
   * @generated from enum value: ANDROID = 3;
   */
  ANDROID = 3,

  /**
   * @generated from enum value: FLUTTER = 4;
   */
  FLUTTER = 4,

  /**
   * @generated from enum value: GO = 5;
   */
  GO = 5,

  /**
   * @generated from enum value: UNITY = 6;
   */
  UNITY = 6,

  /**
   * @generated from enum value: REACT_NATIVE = 7;
   */
  REACT_NATIVE = 7,

  /**
   * @generated from enum value: RUST = 8;
   */
  RUST = 8,

  /**
   * @generated from enum value: PYTHON = 9;
   */
  PYTHON = 9,

  /**
   * @generated from enum value: CPP = 10;
   */
  CPP = 10,

  /**
   * @generated from enum value: UNITY_WEB = 11;
   */
  UNITY_WEB = 11,

  /**
   * @generated from enum value: NODE = 12;
   */
  NODE = 12,

  /**
   * @generated from enum value: UNREAL = 13;
   */
  UNREAL = 13,

  /**
   * @generated from enum value: ESP32 = 14;
   */
  ESP32 = 14,
}

/**
 * server provided client configuration
 *
 * @generated from message livekit.ClientConfiguration
 */
declare class ClientConfiguration extends Message<ClientConfiguration> {
  /**
   * @generated from field: livekit.VideoConfiguration video = 1;
   */
  video?: VideoConfiguration;

  /**
   * @generated from field: livekit.VideoConfiguration screen = 2;
   */
  screen?: VideoConfiguration;

  /**
   * @generated from field: livekit.ClientConfigSetting resume_connection = 3;
   */
  resumeConnection: ClientConfigSetting;

  /**
   * @generated from field: livekit.DisabledCodecs disabled_codecs = 4;
   */
  disabledCodecs?: DisabledCodecs;

  /**
   * @generated from field: livekit.ClientConfigSetting force_relay = 5;
   */
  forceRelay: ClientConfigSetting;

  constructor(data?: PartialMessage<ClientConfiguration>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.ClientConfiguration";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ClientConfiguration;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ClientConfiguration;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ClientConfiguration;

  static equals(a: ClientConfiguration | PlainMessage<ClientConfiguration> | undefined, b: ClientConfiguration | PlainMessage<ClientConfiguration> | undefined): boolean;
}

/**
 * @generated from message livekit.VideoConfiguration
 */
declare class VideoConfiguration extends Message<VideoConfiguration> {
  /**
   * @generated from field: livekit.ClientConfigSetting hardware_encoder = 1;
   */
  hardwareEncoder: ClientConfigSetting;

  constructor(data?: PartialMessage<VideoConfiguration>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.VideoConfiguration";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): VideoConfiguration;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): VideoConfiguration;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): VideoConfiguration;

  static equals(a: VideoConfiguration | PlainMessage<VideoConfiguration> | undefined, b: VideoConfiguration | PlainMessage<VideoConfiguration> | undefined): boolean;
}

/**
 * @generated from message livekit.DisabledCodecs
 */
declare class DisabledCodecs extends Message<DisabledCodecs> {
  /**
   * disabled for both publish and subscribe
   *
   * @generated from field: repeated livekit.Codec codecs = 1;
   */
  codecs: Codec[];

  /**
   * only disable for publish
   *
   * @generated from field: repeated livekit.Codec publish = 2;
   */
  publish: Codec[];

  constructor(data?: PartialMessage<DisabledCodecs>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.DisabledCodecs";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DisabledCodecs;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DisabledCodecs;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DisabledCodecs;

  static equals(a: DisabledCodecs | PlainMessage<DisabledCodecs> | undefined, b: DisabledCodecs | PlainMessage<DisabledCodecs> | undefined): boolean;
}

/**
 * @generated from message livekit.RTPDrift
 */
declare class RTPDrift extends Message<RTPDrift> {
  /**
   * @generated from field: google.protobuf.Timestamp start_time = 1;
   */
  startTime?: Timestamp;

  /**
   * @generated from field: google.protobuf.Timestamp end_time = 2;
   */
  endTime?: Timestamp;

  /**
   * @generated from field: double duration = 3;
   */
  duration: number;

  /**
   * @generated from field: uint64 start_timestamp = 4;
   */
  startTimestamp: bigint;

  /**
   * @generated from field: uint64 end_timestamp = 5;
   */
  endTimestamp: bigint;

  /**
   * @generated from field: uint64 rtp_clock_ticks = 6;
   */
  rtpClockTicks: bigint;

  /**
   * @generated from field: int64 drift_samples = 7;
   */
  driftSamples: bigint;

  /**
   * @generated from field: double drift_ms = 8;
   */
  driftMs: number;

  /**
   * @generated from field: double clock_rate = 9;
   */
  clockRate: number;

  constructor(data?: PartialMessage<RTPDrift>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.RTPDrift";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RTPDrift;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RTPDrift;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RTPDrift;

  static equals(a: RTPDrift | PlainMessage<RTPDrift> | undefined, b: RTPDrift | PlainMessage<RTPDrift> | undefined): boolean;
}

/**
 * @generated from message livekit.RTPStats
 */
declare class RTPStats extends Message<RTPStats> {
  /**
   * @generated from field: google.protobuf.Timestamp start_time = 1;
   */
  startTime?: Timestamp;

  /**
   * @generated from field: google.protobuf.Timestamp end_time = 2;
   */
  endTime?: Timestamp;

  /**
   * @generated from field: double duration = 3;
   */
  duration: number;

  /**
   * @generated from field: uint32 packets = 4;
   */
  packets: number;

  /**
   * @generated from field: double packet_rate = 5;
   */
  packetRate: number;

  /**
   * @generated from field: uint64 bytes = 6;
   */
  bytes: bigint;

  /**
   * @generated from field: uint64 header_bytes = 39;
   */
  headerBytes: bigint;

  /**
   * @generated from field: double bitrate = 7;
   */
  bitrate: number;

  /**
   * @generated from field: uint32 packets_lost = 8;
   */
  packetsLost: number;

  /**
   * @generated from field: double packet_loss_rate = 9;
   */
  packetLossRate: number;

  /**
   * @generated from field: float packet_loss_percentage = 10;
   */
  packetLossPercentage: number;

  /**
   * @generated from field: uint32 packets_duplicate = 11;
   */
  packetsDuplicate: number;

  /**
   * @generated from field: double packet_duplicate_rate = 12;
   */
  packetDuplicateRate: number;

  /**
   * @generated from field: uint64 bytes_duplicate = 13;
   */
  bytesDuplicate: bigint;

  /**
   * @generated from field: uint64 header_bytes_duplicate = 40;
   */
  headerBytesDuplicate: bigint;

  /**
   * @generated from field: double bitrate_duplicate = 14;
   */
  bitrateDuplicate: number;

  /**
   * @generated from field: uint32 packets_padding = 15;
   */
  packetsPadding: number;

  /**
   * @generated from field: double packet_padding_rate = 16;
   */
  packetPaddingRate: number;

  /**
   * @generated from field: uint64 bytes_padding = 17;
   */
  bytesPadding: bigint;

  /**
   * @generated from field: uint64 header_bytes_padding = 41;
   */
  headerBytesPadding: bigint;

  /**
   * @generated from field: double bitrate_padding = 18;
   */
  bitratePadding: number;

  /**
   * @generated from field: uint32 packets_out_of_order = 19;
   */
  packetsOutOfOrder: number;

  /**
   * @generated from field: uint32 frames = 20;
   */
  frames: number;

  /**
   * @generated from field: double frame_rate = 21;
   */
  frameRate: number;

  /**
   * @generated from field: double jitter_current = 22;
   */
  jitterCurrent: number;

  /**
   * @generated from field: double jitter_max = 23;
   */
  jitterMax: number;

  /**
   * @generated from field: map<int32, uint32> gap_histogram = 24;
   */
  gapHistogram: { [key: number]: number };

  /**
   * @generated from field: uint32 nacks = 25;
   */
  nacks: number;

  /**
   * @generated from field: uint32 nack_acks = 37;
   */
  nackAcks: number;

  /**
   * @generated from field: uint32 nack_misses = 26;
   */
  nackMisses: number;

  /**
   * @generated from field: uint32 nack_repeated = 38;
   */
  nackRepeated: number;

  /**
   * @generated from field: uint32 plis = 27;
   */
  plis: number;

  /**
   * @generated from field: google.protobuf.Timestamp last_pli = 28;
   */
  lastPli?: Timestamp;

  /**
   * @generated from field: uint32 firs = 29;
   */
  firs: number;

  /**
   * @generated from field: google.protobuf.Timestamp last_fir = 30;
   */
  lastFir?: Timestamp;

  /**
   * @generated from field: uint32 rtt_current = 31;
   */
  rttCurrent: number;

  /**
   * @generated from field: uint32 rtt_max = 32;
   */
  rttMax: number;

  /**
   * @generated from field: uint32 key_frames = 33;
   */
  keyFrames: number;

  /**
   * @generated from field: google.protobuf.Timestamp last_key_frame = 34;
   */
  lastKeyFrame?: Timestamp;

  /**
   * @generated from field: uint32 layer_lock_plis = 35;
   */
  layerLockPlis: number;

  /**
   * @generated from field: google.protobuf.Timestamp last_layer_lock_pli = 36;
   */
  lastLayerLockPli?: Timestamp;

  /**
   * @generated from field: livekit.RTPDrift packet_drift = 44;
   */
  packetDrift?: RTPDrift;

  /**
   * @generated from field: livekit.RTPDrift ntp_report_drift = 45;
   */
  ntpReportDrift?: RTPDrift;

  /**
   * @generated from field: livekit.RTPDrift rebased_report_drift = 46;
   */
  rebasedReportDrift?: RTPDrift;

  /**
   * NEXT_ID: 48
   *
   * @generated from field: livekit.RTPDrift received_report_drift = 47;
   */
  receivedReportDrift?: RTPDrift;

  constructor(data?: PartialMessage<RTPStats>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.RTPStats";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RTPStats;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RTPStats;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RTPStats;

  static equals(a: RTPStats | PlainMessage<RTPStats> | undefined, b: RTPStats | PlainMessage<RTPStats> | undefined): boolean;
}

/**
 * @generated from message livekit.RTCPSenderReportState
 */
declare class RTCPSenderReportState extends Message<RTCPSenderReportState> {
  /**
   * @generated from field: uint32 rtp_timestamp = 1;
   */
  rtpTimestamp: number;

  /**
   * @generated from field: uint64 rtp_timestamp_ext = 2;
   */
  rtpTimestampExt: bigint;

  /**
   * @generated from field: uint64 ntp_timestamp = 3;
   */
  ntpTimestamp: bigint;

  /**
   * time at which this happened
   *
   * @generated from field: int64 at = 4;
   */
  at: bigint;

  /**
   * @generated from field: int64 at_adjusted = 5;
   */
  atAdjusted: bigint;

  /**
   * @generated from field: uint32 packets = 6;
   */
  packets: number;

  /**
   * @generated from field: uint64 octets = 7;
   */
  octets: bigint;

  constructor(data?: PartialMessage<RTCPSenderReportState>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.RTCPSenderReportState";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RTCPSenderReportState;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RTCPSenderReportState;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RTCPSenderReportState;

  static equals(a: RTCPSenderReportState | PlainMessage<RTCPSenderReportState> | undefined, b: RTCPSenderReportState | PlainMessage<RTCPSenderReportState> | undefined): boolean;
}

/**
 * @generated from message livekit.RTPForwarderState
 */
declare class RTPForwarderState extends Message<RTPForwarderState> {
  /**
   * @generated from field: bool started = 1;
   */
  started: boolean;

  /**
   * @generated from field: int32 reference_layer_spatial = 2;
   */
  referenceLayerSpatial: number;

  /**
   * @generated from field: int64 pre_start_time = 3;
   */
  preStartTime: bigint;

  /**
   * @generated from field: uint64 ext_first_timestamp = 4;
   */
  extFirstTimestamp: bigint;

  /**
   * @generated from field: uint64 dummy_start_timestamp_offset = 5;
   */
  dummyStartTimestampOffset: bigint;

  /**
   * @generated from field: livekit.RTPMungerState rtp_munger = 6;
   */
  rtpMunger?: RTPMungerState;

  /**
   * @generated from oneof livekit.RTPForwarderState.codec_munger
   */
  codecMunger: {
    /**
     * @generated from field: livekit.VP8MungerState vp8_munger = 7;
     */
    value: VP8MungerState;
    case: "vp8Munger";
  } | { case: undefined; value?: undefined };

  /**
   * @generated from field: repeated livekit.RTCPSenderReportState sender_report_state = 8;
   */
  senderReportState: RTCPSenderReportState[];

  constructor(data?: PartialMessage<RTPForwarderState>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.RTPForwarderState";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RTPForwarderState;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RTPForwarderState;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RTPForwarderState;

  static equals(a: RTPForwarderState | PlainMessage<RTPForwarderState> | undefined, b: RTPForwarderState | PlainMessage<RTPForwarderState> | undefined): boolean;
}

/**
 * @generated from message livekit.RTPMungerState
 */
declare class RTPMungerState extends Message<RTPMungerState> {
  /**
   * @generated from field: uint64 ext_last_sequence_number = 1;
   */
  extLastSequenceNumber: bigint;

  /**
   * @generated from field: uint64 ext_second_last_sequence_number = 2;
   */
  extSecondLastSequenceNumber: bigint;

  /**
   * @generated from field: uint64 ext_last_timestamp = 3;
   */
  extLastTimestamp: bigint;

  /**
   * @generated from field: uint64 ext_second_last_timestamp = 4;
   */
  extSecondLastTimestamp: bigint;

  /**
   * @generated from field: bool last_marker = 5;
   */
  lastMarker: boolean;

  /**
   * @generated from field: bool second_last_marker = 6;
   */
  secondLastMarker: boolean;

  constructor(data?: PartialMessage<RTPMungerState>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.RTPMungerState";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RTPMungerState;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RTPMungerState;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RTPMungerState;

  static equals(a: RTPMungerState | PlainMessage<RTPMungerState> | undefined, b: RTPMungerState | PlainMessage<RTPMungerState> | undefined): boolean;
}

/**
 * @generated from message livekit.VP8MungerState
 */
declare class VP8MungerState extends Message<VP8MungerState> {
  /**
   * @generated from field: int32 ext_last_picture_id = 1;
   */
  extLastPictureId: number;

  /**
   * @generated from field: bool picture_id_used = 2;
   */
  pictureIdUsed: boolean;

  /**
   * @generated from field: uint32 last_tl0_pic_idx = 3;
   */
  lastTl0PicIdx: number;

  /**
   * @generated from field: bool tl0_pic_idx_used = 4;
   */
  tl0PicIdxUsed: boolean;

  /**
   * @generated from field: bool tid_used = 5;
   */
  tidUsed: boolean;

  /**
   * @generated from field: uint32 last_key_idx = 6;
   */
  lastKeyIdx: number;

  /**
   * @generated from field: bool key_idx_used = 7;
   */
  keyIdxUsed: boolean;

  constructor(data?: PartialMessage<VP8MungerState>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.VP8MungerState";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): VP8MungerState;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): VP8MungerState;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): VP8MungerState;

  static equals(a: VP8MungerState | PlainMessage<VP8MungerState> | undefined, b: VP8MungerState | PlainMessage<VP8MungerState> | undefined): boolean;
}

/**
 * @generated from message livekit.TimedVersion
 */
declare class TimedVersion extends Message<TimedVersion> {
  /**
   * @generated from field: int64 unix_micro = 1;
   */
  unixMicro: bigint;

  /**
   * @generated from field: int32 ticks = 2;
   */
  ticks: number;

  constructor(data?: PartialMessage<TimedVersion>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.TimedVersion";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): TimedVersion;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): TimedVersion;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): TimedVersion;

  static equals(a: TimedVersion | PlainMessage<TimedVersion> | undefined, b: TimedVersion | PlainMessage<TimedVersion> | undefined): boolean;
}

/**
 * @generated from message livekit.DataStream
 */
declare class DataStream extends Message<DataStream> {
  constructor(data?: PartialMessage<DataStream>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.DataStream";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DataStream;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DataStream;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DataStream;

  static equals(a: DataStream | PlainMessage<DataStream> | undefined, b: DataStream | PlainMessage<DataStream> | undefined): boolean;
}

/**
 * enum for operation types (specific to TextHeader)
 *
 * @generated from enum livekit.DataStream.OperationType
 */
declare enum DataStream_OperationType {
  /**
   * @generated from enum value: CREATE = 0;
   */
  CREATE = 0,

  /**
   * @generated from enum value: UPDATE = 1;
   */
  UPDATE = 1,

  /**
   * @generated from enum value: DELETE = 2;
   */
  DELETE = 2,

  /**
   * @generated from enum value: REACTION = 3;
   */
  REACTION = 3,
}

/**
 * header properties specific to text streams
 *
 * @generated from message livekit.DataStream.TextHeader
 */
declare class DataStream_TextHeader extends Message<DataStream_TextHeader> {
  /**
   * @generated from field: livekit.DataStream.OperationType operation_type = 1;
   */
  operationType: DataStream_OperationType;

  /**
   * Optional: Version for updates/edits
   *
   * @generated from field: int32 version = 2;
   */
  version: number;

  /**
   * Optional: Reply to specific message
   *
   * @generated from field: string reply_to_stream_id = 3;
   */
  replyToStreamId: string;

  /**
   * file attachments for text streams
   *
   * @generated from field: repeated string attached_stream_ids = 4;
   */
  attachedStreamIds: string[];

  /**
   * true if the text has been generated by an agent from a participant's audio transcription
   *
   * @generated from field: bool generated = 5;
   */
  generated: boolean;

  constructor(data?: PartialMessage<DataStream_TextHeader>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.DataStream.TextHeader";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DataStream_TextHeader;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DataStream_TextHeader;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DataStream_TextHeader;

  static equals(a: DataStream_TextHeader | PlainMessage<DataStream_TextHeader> | undefined, b: DataStream_TextHeader | PlainMessage<DataStream_TextHeader> | undefined): boolean;
}

/**
 * header properties specific to byte or file streams
 *
 * @generated from message livekit.DataStream.ByteHeader
 */
declare class DataStream_ByteHeader extends Message<DataStream_ByteHeader> {
  /**
   * @generated from field: string name = 1;
   */
  name: string;

  constructor(data?: PartialMessage<DataStream_ByteHeader>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.DataStream.ByteHeader";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DataStream_ByteHeader;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DataStream_ByteHeader;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DataStream_ByteHeader;

  static equals(a: DataStream_ByteHeader | PlainMessage<DataStream_ByteHeader> | undefined, b: DataStream_ByteHeader | PlainMessage<DataStream_ByteHeader> | undefined): boolean;
}

/**
 * main DataStream.Header that contains a oneof for specific headers
 *
 * @generated from message livekit.DataStream.Header
 */
declare class DataStream_Header extends Message<DataStream_Header> {
  /**
   * unique identifier for this data stream
   *
   * @generated from field: string stream_id = 1;
   */
  streamId: string;

  /**
   * using int64 for Unix timestamp
   *
   * @generated from field: int64 timestamp = 2;
   */
  timestamp: bigint;

  /**
   * @generated from field: string topic = 3;
   */
  topic: string;

  /**
   * @generated from field: string mime_type = 4;
   */
  mimeType: string;

  /**
   * only populated for finite streams, if it's a stream of unknown size this stays empty
   *
   * @generated from field: optional uint64 total_length = 5;
   */
  totalLength?: bigint;

  /**
   * defaults to NONE
   *
   * @generated from field: livekit.Encryption.Type encryption_type = 7;
   */
  encryptionType: Encryption_Type;

  /**
   * user defined attributes map that can carry additional info
   *
   * @generated from field: map<string, string> attributes = 8;
   */
  attributes: { [key: string]: string };

  /**
   * oneof to choose between specific header types
   *
   * @generated from oneof livekit.DataStream.Header.content_header
   */
  contentHeader: {
    /**
     * @generated from field: livekit.DataStream.TextHeader text_header = 9;
     */
    value: DataStream_TextHeader;
    case: "textHeader";
  } | {
    /**
     * @generated from field: livekit.DataStream.ByteHeader byte_header = 10;
     */
    value: DataStream_ByteHeader;
    case: "byteHeader";
  } | { case: undefined; value?: undefined };

  constructor(data?: PartialMessage<DataStream_Header>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.DataStream.Header";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DataStream_Header;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DataStream_Header;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DataStream_Header;

  static equals(a: DataStream_Header | PlainMessage<DataStream_Header> | undefined, b: DataStream_Header | PlainMessage<DataStream_Header> | undefined): boolean;
}

/**
 * @generated from message livekit.DataStream.Chunk
 */
declare class DataStream_Chunk extends Message<DataStream_Chunk> {
  /**
   * unique identifier for this data stream to map it to the correct header
   *
   * @generated from field: string stream_id = 1;
   */
  streamId: string;

  /**
   * @generated from field: uint64 chunk_index = 2;
   */
  chunkIndex: bigint;

  /**
   * content as binary (bytes)
   *
   * @generated from field: bytes content = 3;
   */
  content: Uint8Array;

  /**
   * a version indicating that this chunk_index has been retroactively modified and the original one needs to be replaced
   *
   * @generated from field: int32 version = 4;
   */
  version: number;

  /**
   * optional, initialization vector for AES-GCM encryption
   *
   * @generated from field: optional bytes iv = 5;
   */
  iv?: Uint8Array;

  constructor(data?: PartialMessage<DataStream_Chunk>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.DataStream.Chunk";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DataStream_Chunk;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DataStream_Chunk;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DataStream_Chunk;

  static equals(a: DataStream_Chunk | PlainMessage<DataStream_Chunk> | undefined, b: DataStream_Chunk | PlainMessage<DataStream_Chunk> | undefined): boolean;
}

/**
 * @generated from message livekit.DataStream.Trailer
 */
declare class DataStream_Trailer extends Message<DataStream_Trailer> {
  /**
   * unique identifier for this data stream
   *
   * @generated from field: string stream_id = 1;
   */
  streamId: string;

  /**
   * reason why the stream was closed (could contain "error" / "interrupted" / empty for expected end)
   *
   * @generated from field: string reason = 2;
   */
  reason: string;

  /**
   * finalizing updates for the stream, can also include additional insights for errors or endTime for transcription
   *
   * @generated from field: map<string, string> attributes = 3;
   */
  attributes: { [key: string]: string };

  constructor(data?: PartialMessage<DataStream_Trailer>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.DataStream.Trailer";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DataStream_Trailer;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DataStream_Trailer;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DataStream_Trailer;

  static equals(a: DataStream_Trailer | PlainMessage<DataStream_Trailer> | undefined, b: DataStream_Trailer | PlainMessage<DataStream_Trailer> | undefined): boolean;
}

/**
 * @generated from message livekit.WebhookConfig
 */
declare class WebhookConfig extends Message<WebhookConfig> {
  /**
   * @generated from field: string url = 1;
   */
  url: string;

  /**
   * @generated from field: string signing_key = 2;
   */
  signingKey: string;

  constructor(data?: PartialMessage<WebhookConfig>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.WebhookConfig";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): WebhookConfig;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): WebhookConfig;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): WebhookConfig;

  static equals(a: WebhookConfig | PlainMessage<WebhookConfig> | undefined, b: WebhookConfig | PlainMessage<WebhookConfig> | undefined): boolean;
}

// Copyright 2023 LiveKit, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//   http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.



/**
 * @generated from enum livekit.JobType
 */
declare enum JobType {
  /**
   * @generated from enum value: JT_ROOM = 0;
   */
  JT_ROOM = 0,

  /**
   * @generated from enum value: JT_PUBLISHER = 1;
   */
  JT_PUBLISHER = 1,

  /**
   * @generated from enum value: JT_PARTICIPANT = 2;
   */
  JT_PARTICIPANT = 2,
}

/**
 * @generated from enum livekit.WorkerStatus
 */
declare enum WorkerStatus {
  /**
   * @generated from enum value: WS_AVAILABLE = 0;
   */
  WS_AVAILABLE = 0,

  /**
   * @generated from enum value: WS_FULL = 1;
   */
  WS_FULL = 1,
}

/**
 * @generated from enum livekit.JobStatus
 */
declare enum JobStatus {
  /**
   * @generated from enum value: JS_PENDING = 0;
   */
  JS_PENDING = 0,

  /**
   * @generated from enum value: JS_RUNNING = 1;
   */
  JS_RUNNING = 1,

  /**
   * @generated from enum value: JS_SUCCESS = 2;
   */
  JS_SUCCESS = 2,

  /**
   * @generated from enum value: JS_FAILED = 3;
   */
  JS_FAILED = 3,
}

/**
 * @generated from message livekit.Job
 */
declare class Job extends Message<Job> {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: string dispatch_id = 9;
   */
  dispatchId: string;

  /**
   * @generated from field: livekit.JobType type = 2;
   */
  type: JobType;

  /**
   * @generated from field: livekit.Room room = 3;
   */
  room?: Room;

  /**
   * @generated from field: optional livekit.ParticipantInfo participant = 4;
   */
  participant?: ParticipantInfo;

  /**
   * @generated from field: string namespace = 5 [deprecated = true];
   * @deprecated
   */
  namespace: string;

  /**
   * @generated from field: string metadata = 6;
   */
  metadata: string;

  /**
   * @generated from field: string agent_name = 7;
   */
  agentName: string;

  /**
   * @generated from field: livekit.JobState state = 8;
   */
  state?: JobState;

  constructor(data?: PartialMessage<Job>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.Job";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Job;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Job;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Job;

  static equals(a: Job | PlainMessage<Job> | undefined, b: Job | PlainMessage<Job> | undefined): boolean;
}

/**
 * @generated from message livekit.JobState
 */
declare class JobState extends Message<JobState> {
  /**
   * @generated from field: livekit.JobStatus status = 1;
   */
  status: JobStatus;

  /**
   * @generated from field: string error = 2;
   */
  error: string;

  /**
   * @generated from field: int64 started_at = 3;
   */
  startedAt: bigint;

  /**
   * @generated from field: int64 ended_at = 4;
   */
  endedAt: bigint;

  /**
   * @generated from field: int64 updated_at = 5;
   */
  updatedAt: bigint;

  /**
   * @generated from field: string participant_identity = 6;
   */
  participantIdentity: string;

  /**
   * @generated from field: string worker_id = 7;
   */
  workerId: string;

  constructor(data?: PartialMessage<JobState>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.JobState";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): JobState;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): JobState;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): JobState;

  static equals(a: JobState | PlainMessage<JobState> | undefined, b: JobState | PlainMessage<JobState> | undefined): boolean;
}

/**
 * from Worker to Server
 *
 * @generated from message livekit.WorkerMessage
 */
declare class WorkerMessage extends Message<WorkerMessage> {
  /**
   * @generated from oneof livekit.WorkerMessage.message
   */
  message: {
    /**
     * agent workers need to register themselves with the server first
     *
     * @generated from field: livekit.RegisterWorkerRequest register = 1;
     */
    value: RegisterWorkerRequest;
    case: "register";
  } | {
    /**
     * worker confirms to server that it's available for a job, or declines it
     *
     * @generated from field: livekit.AvailabilityResponse availability = 2;
     */
    value: AvailabilityResponse;
    case: "availability";
  } | {
    /**
     * worker can update its status to the server, including taking itself out of the pool
     *
     * @generated from field: livekit.UpdateWorkerStatus update_worker = 3;
     */
    value: UpdateWorkerStatus;
    case: "updateWorker";
  } | {
    /**
     * job can send status updates to the server, useful for tracking progress
     *
     * @generated from field: livekit.UpdateJobStatus update_job = 4;
     */
    value: UpdateJobStatus;
    case: "updateJob";
  } | {
    /**
     * @generated from field: livekit.WorkerPing ping = 5;
     */
    value: WorkerPing;
    case: "ping";
  } | {
    /**
     * @generated from field: livekit.SimulateJobRequest simulate_job = 6;
     */
    value: SimulateJobRequest;
    case: "simulateJob";
  } | {
    /**
     * @generated from field: livekit.MigrateJobRequest migrate_job = 7;
     */
    value: MigrateJobRequest;
    case: "migrateJob";
  } | { case: undefined; value?: undefined };

  constructor(data?: PartialMessage<WorkerMessage>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.WorkerMessage";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): WorkerMessage;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): WorkerMessage;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): WorkerMessage;

  static equals(a: WorkerMessage | PlainMessage<WorkerMessage> | undefined, b: WorkerMessage | PlainMessage<WorkerMessage> | undefined): boolean;
}

/**
 * from Server to Worker
 *
 * @generated from message livekit.ServerMessage
 */
declare class ServerMessage extends Message<ServerMessage> {
  /**
   * @generated from oneof livekit.ServerMessage.message
   */
  message: {
    /**
     * server confirms the registration, from this moment on, the worker is considered active
     *
     * @generated from field: livekit.RegisterWorkerResponse register = 1;
     */
    value: RegisterWorkerResponse;
    case: "register";
  } | {
    /**
     * server asks worker to confirm availability for a job
     *
     * @generated from field: livekit.AvailabilityRequest availability = 2;
     */
    value: AvailabilityRequest;
    case: "availability";
  } | {
    /**
     * @generated from field: livekit.JobAssignment assignment = 3;
     */
    value: JobAssignment;
    case: "assignment";
  } | {
    /**
     * @generated from field: livekit.JobTermination termination = 5;
     */
    value: JobTermination;
    case: "termination";
  } | {
    /**
     * @generated from field: livekit.WorkerPong pong = 4;
     */
    value: WorkerPong;
    case: "pong";
  } | { case: undefined; value?: undefined };

  constructor(data?: PartialMessage<ServerMessage>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.ServerMessage";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ServerMessage;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ServerMessage;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ServerMessage;

  static equals(a: ServerMessage | PlainMessage<ServerMessage> | undefined, b: ServerMessage | PlainMessage<ServerMessage> | undefined): boolean;
}

/**
 * @generated from message livekit.SimulateJobRequest
 */
declare class SimulateJobRequest extends Message<SimulateJobRequest> {
  /**
   * @generated from field: livekit.JobType type = 1;
   */
  type: JobType;

  /**
   * @generated from field: livekit.Room room = 2;
   */
  room?: Room;

  /**
   * @generated from field: livekit.ParticipantInfo participant = 3;
   */
  participant?: ParticipantInfo;

  constructor(data?: PartialMessage<SimulateJobRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.SimulateJobRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SimulateJobRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SimulateJobRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SimulateJobRequest;

  static equals(a: SimulateJobRequest | PlainMessage<SimulateJobRequest> | undefined, b: SimulateJobRequest | PlainMessage<SimulateJobRequest> | undefined): boolean;
}

/**
 * @generated from message livekit.WorkerPing
 */
declare class WorkerPing extends Message<WorkerPing> {
  /**
   * @generated from field: int64 timestamp = 1;
   */
  timestamp: bigint;

  constructor(data?: PartialMessage<WorkerPing>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.WorkerPing";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): WorkerPing;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): WorkerPing;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): WorkerPing;

  static equals(a: WorkerPing | PlainMessage<WorkerPing> | undefined, b: WorkerPing | PlainMessage<WorkerPing> | undefined): boolean;
}

/**
 * @generated from message livekit.WorkerPong
 */
declare class WorkerPong extends Message<WorkerPong> {
  /**
   * @generated from field: int64 last_timestamp = 1;
   */
  lastTimestamp: bigint;

  /**
   * @generated from field: int64 timestamp = 2;
   */
  timestamp: bigint;

  constructor(data?: PartialMessage<WorkerPong>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.WorkerPong";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): WorkerPong;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): WorkerPong;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): WorkerPong;

  static equals(a: WorkerPong | PlainMessage<WorkerPong> | undefined, b: WorkerPong | PlainMessage<WorkerPong> | undefined): boolean;
}

/**
 * @generated from message livekit.RegisterWorkerRequest
 */
declare class RegisterWorkerRequest extends Message<RegisterWorkerRequest> {
  /**
   * @generated from field: livekit.JobType type = 1;
   */
  type: JobType;

  /**
   * @generated from field: string agent_name = 8;
   */
  agentName: string;

  /**
   * string worker_id = 2;
   *
   * @generated from field: string version = 3;
   */
  version: string;

  /**
   * string name = 4 [deprecated = true];
   *
   * @generated from field: uint32 ping_interval = 5;
   */
  pingInterval: number;

  /**
   * @generated from field: optional string namespace = 6;
   */
  namespace?: string;

  /**
   * @generated from field: livekit.ParticipantPermission allowed_permissions = 7;
   */
  allowedPermissions?: ParticipantPermission;

  constructor(data?: PartialMessage<RegisterWorkerRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.RegisterWorkerRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RegisterWorkerRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RegisterWorkerRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RegisterWorkerRequest;

  static equals(a: RegisterWorkerRequest | PlainMessage<RegisterWorkerRequest> | undefined, b: RegisterWorkerRequest | PlainMessage<RegisterWorkerRequest> | undefined): boolean;
}

/**
 * @generated from message livekit.RegisterWorkerResponse
 */
declare class RegisterWorkerResponse extends Message<RegisterWorkerResponse> {
  /**
   * @generated from field: string worker_id = 1;
   */
  workerId: string;

  /**
   * @generated from field: livekit.ServerInfo server_info = 3;
   */
  serverInfo?: ServerInfo;

  constructor(data?: PartialMessage<RegisterWorkerResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.RegisterWorkerResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RegisterWorkerResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RegisterWorkerResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RegisterWorkerResponse;

  static equals(a: RegisterWorkerResponse | PlainMessage<RegisterWorkerResponse> | undefined, b: RegisterWorkerResponse | PlainMessage<RegisterWorkerResponse> | undefined): boolean;
}

/**
 * @generated from message livekit.MigrateJobRequest
 */
declare class MigrateJobRequest extends Message<MigrateJobRequest> {
  /**
   * string job_id = 1 [deprecated = true];
   *
   * @generated from field: repeated string job_ids = 2;
   */
  jobIds: string[];

  constructor(data?: PartialMessage<MigrateJobRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.MigrateJobRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): MigrateJobRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): MigrateJobRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): MigrateJobRequest;

  static equals(a: MigrateJobRequest | PlainMessage<MigrateJobRequest> | undefined, b: MigrateJobRequest | PlainMessage<MigrateJobRequest> | undefined): boolean;
}

/**
 * @generated from message livekit.AvailabilityRequest
 */
declare class AvailabilityRequest extends Message<AvailabilityRequest> {
  /**
   * @generated from field: livekit.Job job = 1;
   */
  job?: Job;

  /**
   * True when the job was previously assigned to another worker but has been
   * migrated due to different reasons (e.g. worker failure, job migration)
   *
   * @generated from field: bool resuming = 2;
   */
  resuming: boolean;

  constructor(data?: PartialMessage<AvailabilityRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.AvailabilityRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AvailabilityRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AvailabilityRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AvailabilityRequest;

  static equals(a: AvailabilityRequest | PlainMessage<AvailabilityRequest> | undefined, b: AvailabilityRequest | PlainMessage<AvailabilityRequest> | undefined): boolean;
}

/**
 * @generated from message livekit.AvailabilityResponse
 */
declare class AvailabilityResponse extends Message<AvailabilityResponse> {
  /**
   * @generated from field: string job_id = 1;
   */
  jobId: string;

  /**
   * @generated from field: bool available = 2;
   */
  available: boolean;

  /**
   * @generated from field: bool supports_resume = 3;
   */
  supportsResume: boolean;

  /**
   * @generated from field: bool terminate = 8;
   */
  terminate: boolean;

  /**
   * @generated from field: string participant_name = 4;
   */
  participantName: string;

  /**
   * @generated from field: string participant_identity = 5;
   */
  participantIdentity: string;

  /**
   * @generated from field: string participant_metadata = 6;
   */
  participantMetadata: string;

  /**
   * NEXT_ID: 9
   *
   * @generated from field: map<string, string> participant_attributes = 7;
   */
  participantAttributes: { [key: string]: string };

  constructor(data?: PartialMessage<AvailabilityResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.AvailabilityResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AvailabilityResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AvailabilityResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AvailabilityResponse;

  static equals(a: AvailabilityResponse | PlainMessage<AvailabilityResponse> | undefined, b: AvailabilityResponse | PlainMessage<AvailabilityResponse> | undefined): boolean;
}

/**
 * @generated from message livekit.UpdateJobStatus
 */
declare class UpdateJobStatus extends Message<UpdateJobStatus> {
  /**
   * @generated from field: string job_id = 1;
   */
  jobId: string;

  /**
   * The worker can indicate the job end by either specifying SUCCESS or FAILED
   *
   * @generated from field: livekit.JobStatus status = 2;
   */
  status: JobStatus;

  /**
   * metadata shown on the dashboard, useful for debugging
   *
   * @generated from field: string error = 3;
   */
  error: string;

  constructor(data?: PartialMessage<UpdateJobStatus>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.UpdateJobStatus";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateJobStatus;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateJobStatus;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateJobStatus;

  static equals(a: UpdateJobStatus | PlainMessage<UpdateJobStatus> | undefined, b: UpdateJobStatus | PlainMessage<UpdateJobStatus> | undefined): boolean;
}

/**
 * @generated from message livekit.UpdateWorkerStatus
 */
declare class UpdateWorkerStatus extends Message<UpdateWorkerStatus> {
  /**
   * @generated from field: optional livekit.WorkerStatus status = 1;
   */
  status?: WorkerStatus;

  /**
   * optional string metadata = 2 [deprecated=true];
   *
   * @generated from field: float load = 3;
   */
  load: number;

  /**
   * @generated from field: uint32 job_count = 4;
   */
  jobCount: number;

  constructor(data?: PartialMessage<UpdateWorkerStatus>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.UpdateWorkerStatus";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateWorkerStatus;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateWorkerStatus;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateWorkerStatus;

  static equals(a: UpdateWorkerStatus | PlainMessage<UpdateWorkerStatus> | undefined, b: UpdateWorkerStatus | PlainMessage<UpdateWorkerStatus> | undefined): boolean;
}

/**
 * @generated from message livekit.JobAssignment
 */
declare class JobAssignment extends Message<JobAssignment> {
  /**
   * @generated from field: livekit.Job job = 1;
   */
  job?: Job;

  /**
   * @generated from field: optional string url = 2;
   */
  url?: string;

  /**
   * @generated from field: string token = 3;
   */
  token: string;

  constructor(data?: PartialMessage<JobAssignment>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.JobAssignment";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): JobAssignment;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): JobAssignment;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): JobAssignment;

  static equals(a: JobAssignment | PlainMessage<JobAssignment> | undefined, b: JobAssignment | PlainMessage<JobAssignment> | undefined): boolean;
}

/**
 * @generated from message livekit.JobTermination
 */
declare class JobTermination extends Message<JobTermination> {
  /**
   * @generated from field: string job_id = 1;
   */
  jobId: string;

  constructor(data?: PartialMessage<JobTermination>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.JobTermination";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): JobTermination;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): JobTermination;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): JobTermination;

  static equals(a: JobTermination | PlainMessage<JobTermination> | undefined, b: JobTermination | PlainMessage<JobTermination> | undefined): boolean;
}

// Copyright 2023 LiveKit, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//   http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.



/**
 * @generated from message livekit.CreateAgentDispatchRequest
 */
declare class CreateAgentDispatchRequest extends Message<CreateAgentDispatchRequest> {
  /**
   * @generated from field: string agent_name = 1;
   */
  agentName: string;

  /**
   * @generated from field: string room = 2;
   */
  room: string;

  /**
   * @generated from field: string metadata = 3;
   */
  metadata: string;

  constructor(data?: PartialMessage<CreateAgentDispatchRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.CreateAgentDispatchRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreateAgentDispatchRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreateAgentDispatchRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreateAgentDispatchRequest;

  static equals(a: CreateAgentDispatchRequest | PlainMessage<CreateAgentDispatchRequest> | undefined, b: CreateAgentDispatchRequest | PlainMessage<CreateAgentDispatchRequest> | undefined): boolean;
}

/**
 * @generated from message livekit.RoomAgentDispatch
 */
declare class RoomAgentDispatch extends Message<RoomAgentDispatch> {
  /**
   * @generated from field: string agent_name = 1;
   */
  agentName: string;

  /**
   * @generated from field: string metadata = 2;
   */
  metadata: string;

  constructor(data?: PartialMessage<RoomAgentDispatch>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.RoomAgentDispatch";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RoomAgentDispatch;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RoomAgentDispatch;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RoomAgentDispatch;

  static equals(a: RoomAgentDispatch | PlainMessage<RoomAgentDispatch> | undefined, b: RoomAgentDispatch | PlainMessage<RoomAgentDispatch> | undefined): boolean;
}

/**
 * @generated from message livekit.DeleteAgentDispatchRequest
 */
declare class DeleteAgentDispatchRequest extends Message<DeleteAgentDispatchRequest> {
  /**
   * @generated from field: string dispatch_id = 1;
   */
  dispatchId: string;

  /**
   * @generated from field: string room = 2;
   */
  room: string;

  constructor(data?: PartialMessage<DeleteAgentDispatchRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.DeleteAgentDispatchRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeleteAgentDispatchRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeleteAgentDispatchRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeleteAgentDispatchRequest;

  static equals(a: DeleteAgentDispatchRequest | PlainMessage<DeleteAgentDispatchRequest> | undefined, b: DeleteAgentDispatchRequest | PlainMessage<DeleteAgentDispatchRequest> | undefined): boolean;
}

/**
 * @generated from message livekit.ListAgentDispatchRequest
 */
declare class ListAgentDispatchRequest extends Message<ListAgentDispatchRequest> {
  /**
   * if set, only the dispatch whose id is given will be returned
   *
   * @generated from field: string dispatch_id = 1;
   */
  dispatchId: string;

  /**
   * name of the room to list agents for. Must be set.
   *
   * @generated from field: string room = 2;
   */
  room: string;

  constructor(data?: PartialMessage<ListAgentDispatchRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.ListAgentDispatchRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListAgentDispatchRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListAgentDispatchRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListAgentDispatchRequest;

  static equals(a: ListAgentDispatchRequest | PlainMessage<ListAgentDispatchRequest> | undefined, b: ListAgentDispatchRequest | PlainMessage<ListAgentDispatchRequest> | undefined): boolean;
}

/**
 * @generated from message livekit.ListAgentDispatchResponse
 */
declare class ListAgentDispatchResponse extends Message<ListAgentDispatchResponse> {
  /**
   * @generated from field: repeated livekit.AgentDispatch agent_dispatches = 1;
   */
  agentDispatches: AgentDispatch[];

  constructor(data?: PartialMessage<ListAgentDispatchResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.ListAgentDispatchResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListAgentDispatchResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListAgentDispatchResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListAgentDispatchResponse;

  static equals(a: ListAgentDispatchResponse | PlainMessage<ListAgentDispatchResponse> | undefined, b: ListAgentDispatchResponse | PlainMessage<ListAgentDispatchResponse> | undefined): boolean;
}

/**
 * @generated from message livekit.AgentDispatch
 */
declare class AgentDispatch extends Message<AgentDispatch> {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: string agent_name = 2;
   */
  agentName: string;

  /**
   * @generated from field: string room = 3;
   */
  room: string;

  /**
   * @generated from field: string metadata = 4;
   */
  metadata: string;

  /**
   * @generated from field: livekit.AgentDispatchState state = 5;
   */
  state?: AgentDispatchState;

  constructor(data?: PartialMessage<AgentDispatch>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.AgentDispatch";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AgentDispatch;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AgentDispatch;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AgentDispatch;

  static equals(a: AgentDispatch | PlainMessage<AgentDispatch> | undefined, b: AgentDispatch | PlainMessage<AgentDispatch> | undefined): boolean;
}

/**
 * @generated from message livekit.AgentDispatchState
 */
declare class AgentDispatchState extends Message<AgentDispatchState> {
  /**
   * For dispatches of tyoe JT_ROOM, there will be at most 1 job. 
   * For dispatches of type JT_PUBLISHER, there will be 1 per publisher.
   *
   * @generated from field: repeated livekit.Job jobs = 1;
   */
  jobs: Job[];

  /**
   * @generated from field: int64 created_at = 2;
   */
  createdAt: bigint;

  /**
   * @generated from field: int64 deleted_at = 3;
   */
  deletedAt: bigint;

  constructor(data?: PartialMessage<AgentDispatchState>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.AgentDispatchState";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AgentDispatchState;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AgentDispatchState;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AgentDispatchState;

  static equals(a: AgentDispatchState | PlainMessage<AgentDispatchState> | undefined, b: AgentDispatchState | PlainMessage<AgentDispatchState> | undefined): boolean;
}

// Copyright 2023 LiveKit, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.



/**
 * @generated from enum livekit.EncodedFileType
 */
declare enum EncodedFileType {
  /**
   * file type chosen based on codecs
   *
   * @generated from enum value: DEFAULT_FILETYPE = 0;
   */
  DEFAULT_FILETYPE = 0,

  /**
   * @generated from enum value: MP4 = 1;
   */
  MP4 = 1,

  /**
   * @generated from enum value: OGG = 2;
   */
  OGG = 2,
}

/**
 * @generated from enum livekit.SegmentedFileProtocol
 */
declare enum SegmentedFileProtocol {
  /**
   * @generated from enum value: DEFAULT_SEGMENTED_FILE_PROTOCOL = 0;
   */
  DEFAULT_SEGMENTED_FILE_PROTOCOL = 0,

  /**
   * @generated from enum value: HLS_PROTOCOL = 1;
   */
  HLS_PROTOCOL = 1,
}

/**
 * @generated from enum livekit.SegmentedFileSuffix
 */
declare enum SegmentedFileSuffix {
  /**
   * @generated from enum value: INDEX = 0;
   */
  INDEX = 0,

  /**
   * @generated from enum value: TIMESTAMP = 1;
   */
  TIMESTAMP = 1,
}

/**
 * @generated from enum livekit.ImageFileSuffix
 */
declare enum ImageFileSuffix {
  /**
   * @generated from enum value: IMAGE_SUFFIX_INDEX = 0;
   */
  IMAGE_SUFFIX_INDEX = 0,

  /**
   * @generated from enum value: IMAGE_SUFFIX_TIMESTAMP = 1;
   */
  IMAGE_SUFFIX_TIMESTAMP = 1,

  /**
   * Do not append any suffix and overwrite the existing image with the latest
   *
   * @generated from enum value: IMAGE_SUFFIX_NONE_OVERWRITE = 2;
   */
  IMAGE_SUFFIX_NONE_OVERWRITE = 2,
}

/**
 * @generated from enum livekit.StreamProtocol
 */
declare enum StreamProtocol {
  /**
   * protocol chosen based on urls
   *
   * @generated from enum value: DEFAULT_PROTOCOL = 0;
   */
  DEFAULT_PROTOCOL = 0,

  /**
   * @generated from enum value: RTMP = 1;
   */
  RTMP = 1,

  /**
   * @generated from enum value: SRT = 2;
   */
  SRT = 2,
}

/**
 * @generated from enum livekit.AudioMixing
 */
declare enum AudioMixing {
  /**
   * all users are mixed together
   *
   * @generated from enum value: DEFAULT_MIXING = 0;
   */
  DEFAULT_MIXING = 0,

  /**
   * agent audio in the left channel, all other audio in the right channel
   *
   * @generated from enum value: DUAL_CHANNEL_AGENT = 1;
   */
  DUAL_CHANNEL_AGENT = 1,

  /**
   * each new audio track alternates between left and right channels
   *
   * @generated from enum value: DUAL_CHANNEL_ALTERNATE = 2;
   */
  DUAL_CHANNEL_ALTERNATE = 2,
}

/**
 * @generated from enum livekit.EncodingOptionsPreset
 */
declare enum EncodingOptionsPreset {
  /**
   *  1280x720, 30fps, 3000kpbs, H.264_MAIN / OPUS
   *
   * @generated from enum value: H264_720P_30 = 0;
   */
  H264_720P_30 = 0,

  /**
   *  1280x720, 60fps, 4500kbps, H.264_MAIN / OPUS
   *
   * @generated from enum value: H264_720P_60 = 1;
   */
  H264_720P_60 = 1,

  /**
   * 1920x1080, 30fps, 4500kbps, H.264_MAIN / OPUS
   *
   * @generated from enum value: H264_1080P_30 = 2;
   */
  H264_1080P_30 = 2,

  /**
   * 1920x1080, 60fps, 6000kbps, H.264_MAIN / OPUS
   *
   * @generated from enum value: H264_1080P_60 = 3;
   */
  H264_1080P_60 = 3,

  /**
   *  720x1280, 30fps, 3000kpbs, H.264_MAIN / OPUS
   *
   * @generated from enum value: PORTRAIT_H264_720P_30 = 4;
   */
  PORTRAIT_H264_720P_30 = 4,

  /**
   *  720x1280, 60fps, 4500kbps, H.264_MAIN / OPUS
   *
   * @generated from enum value: PORTRAIT_H264_720P_60 = 5;
   */
  PORTRAIT_H264_720P_60 = 5,

  /**
   * 1080x1920, 30fps, 4500kbps, H.264_MAIN / OPUS
   *
   * @generated from enum value: PORTRAIT_H264_1080P_30 = 6;
   */
  PORTRAIT_H264_1080P_30 = 6,

  /**
   * 1080x1920, 60fps, 6000kbps, H.264_MAIN / OPUS
   *
   * @generated from enum value: PORTRAIT_H264_1080P_60 = 7;
   */
  PORTRAIT_H264_1080P_60 = 7,
}

/**
 * @generated from enum livekit.EgressStatus
 */
declare enum EgressStatus {
  /**
   * @generated from enum value: EGRESS_STARTING = 0;
   */
  EGRESS_STARTING = 0,

  /**
   * @generated from enum value: EGRESS_ACTIVE = 1;
   */
  EGRESS_ACTIVE = 1,

  /**
   * @generated from enum value: EGRESS_ENDING = 2;
   */
  EGRESS_ENDING = 2,

  /**
   * @generated from enum value: EGRESS_COMPLETE = 3;
   */
  EGRESS_COMPLETE = 3,

  /**
   * @generated from enum value: EGRESS_FAILED = 4;
   */
  EGRESS_FAILED = 4,

  /**
   * @generated from enum value: EGRESS_ABORTED = 5;
   */
  EGRESS_ABORTED = 5,

  /**
   * @generated from enum value: EGRESS_LIMIT_REACHED = 6;
   */
  EGRESS_LIMIT_REACHED = 6,
}

/**
 * @generated from enum livekit.EgressSourceType
 */
declare enum EgressSourceType {
  /**
   * @generated from enum value: EGRESS_SOURCE_TYPE_WEB = 0;
   */
  WEB = 0,

  /**
   * @generated from enum value: EGRESS_SOURCE_TYPE_SDK = 1;
   */
  SDK = 1,
}

/**
 * composite using a web browser
 *
 * @generated from message livekit.RoomCompositeEgressRequest
 */
declare class RoomCompositeEgressRequest extends Message<RoomCompositeEgressRequest> {
  /**
   * required
   *
   * @generated from field: string room_name = 1;
   */
  roomName: string;

  /**
   * (optional)
   *
   * @generated from field: string layout = 2;
   */
  layout: string;

  /**
   * (default false)
   *
   * @generated from field: bool audio_only = 3;
   */
  audioOnly: boolean;

  /**
   * only applies to audio_only egress (default DEFAULT_MIXING)
   *
   * @generated from field: livekit.AudioMixing audio_mixing = 15;
   */
  audioMixing: AudioMixing;

  /**
   * (default false)
   *
   * @generated from field: bool video_only = 4;
   */
  videoOnly: boolean;

  /**
   * template base url (default https://recorder.livekit.io)
   *
   * @generated from field: string custom_base_url = 5;
   */
  customBaseUrl: string;

  /**
   * deprecated (use _output fields)
   *
   * @generated from oneof livekit.RoomCompositeEgressRequest.output
   */
  output: {
    /**
     * @generated from field: livekit.EncodedFileOutput file = 6 [deprecated = true];
     * @deprecated
     */
    value: EncodedFileOutput;
    case: "file";
  } | {
    /**
     * @generated from field: livekit.StreamOutput stream = 7 [deprecated = true];
     * @deprecated
     */
    value: StreamOutput;
    case: "stream";
  } | {
    /**
     * @generated from field: livekit.SegmentedFileOutput segments = 10 [deprecated = true];
     * @deprecated
     */
    value: SegmentedFileOutput;
    case: "segments";
  } | { case: undefined; value?: undefined };

  /**
   * @generated from oneof livekit.RoomCompositeEgressRequest.options
   */
  options: {
    /**
     * (default H264_720P_30)
     *
     * @generated from field: livekit.EncodingOptionsPreset preset = 8;
     */
    value: EncodingOptionsPreset;
    case: "preset";
  } | {
    /**
     * (optional)
     *
     * @generated from field: livekit.EncodingOptions advanced = 9;
     */
    value: EncodingOptions;
    case: "advanced";
  } | { case: undefined; value?: undefined };

  /**
   * @generated from field: repeated livekit.EncodedFileOutput file_outputs = 11;
   */
  fileOutputs: EncodedFileOutput[];

  /**
   * @generated from field: repeated livekit.StreamOutput stream_outputs = 12;
   */
  streamOutputs: StreamOutput[];

  /**
   * @generated from field: repeated livekit.SegmentedFileOutput segment_outputs = 13;
   */
  segmentOutputs: SegmentedFileOutput[];

  /**
   * @generated from field: repeated livekit.ImageOutput image_outputs = 14;
   */
  imageOutputs: ImageOutput[];

  /**
   * extra webhooks to call for this request
   *
   * @generated from field: repeated livekit.WebhookConfig webhooks = 16;
   */
  webhooks: WebhookConfig[];

  constructor(data?: PartialMessage<RoomCompositeEgressRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.RoomCompositeEgressRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RoomCompositeEgressRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RoomCompositeEgressRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RoomCompositeEgressRequest;

  static equals(a: RoomCompositeEgressRequest | PlainMessage<RoomCompositeEgressRequest> | undefined, b: RoomCompositeEgressRequest | PlainMessage<RoomCompositeEgressRequest> | undefined): boolean;
}

/**
 * record any website
 *
 * @generated from message livekit.WebEgressRequest
 */
declare class WebEgressRequest extends Message<WebEgressRequest> {
  /**
   * @generated from field: string url = 1;
   */
  url: string;

  /**
   * @generated from field: bool audio_only = 2;
   */
  audioOnly: boolean;

  /**
   * @generated from field: bool video_only = 3;
   */
  videoOnly: boolean;

  /**
   * @generated from field: bool await_start_signal = 12;
   */
  awaitStartSignal: boolean;

  /**
   * deprecated (use _output fields)
   *
   * @generated from oneof livekit.WebEgressRequest.output
   */
  output: {
    /**
     * @generated from field: livekit.EncodedFileOutput file = 4 [deprecated = true];
     * @deprecated
     */
    value: EncodedFileOutput;
    case: "file";
  } | {
    /**
     * @generated from field: livekit.StreamOutput stream = 5 [deprecated = true];
     * @deprecated
     */
    value: StreamOutput;
    case: "stream";
  } | {
    /**
     * @generated from field: livekit.SegmentedFileOutput segments = 6 [deprecated = true];
     * @deprecated
     */
    value: SegmentedFileOutput;
    case: "segments";
  } | { case: undefined; value?: undefined };

  /**
   * @generated from oneof livekit.WebEgressRequest.options
   */
  options: {
    /**
     * @generated from field: livekit.EncodingOptionsPreset preset = 7;
     */
    value: EncodingOptionsPreset;
    case: "preset";
  } | {
    /**
     * @generated from field: livekit.EncodingOptions advanced = 8;
     */
    value: EncodingOptions;
    case: "advanced";
  } | { case: undefined; value?: undefined };

  /**
   * @generated from field: repeated livekit.EncodedFileOutput file_outputs = 9;
   */
  fileOutputs: EncodedFileOutput[];

  /**
   * @generated from field: repeated livekit.StreamOutput stream_outputs = 10;
   */
  streamOutputs: StreamOutput[];

  /**
   * @generated from field: repeated livekit.SegmentedFileOutput segment_outputs = 11;
   */
  segmentOutputs: SegmentedFileOutput[];

  /**
   * @generated from field: repeated livekit.ImageOutput image_outputs = 13;
   */
  imageOutputs: ImageOutput[];

  /**
   * extra webhooks to call for this request
   *
   * @generated from field: repeated livekit.WebhookConfig webhooks = 14;
   */
  webhooks: WebhookConfig[];

  constructor(data?: PartialMessage<WebEgressRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.WebEgressRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): WebEgressRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): WebEgressRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): WebEgressRequest;

  static equals(a: WebEgressRequest | PlainMessage<WebEgressRequest> | undefined, b: WebEgressRequest | PlainMessage<WebEgressRequest> | undefined): boolean;
}

/**
 * record audio and video from a single participant
 *
 * @generated from message livekit.ParticipantEgressRequest
 */
declare class ParticipantEgressRequest extends Message<ParticipantEgressRequest> {
  /**
   * required
   *
   * @generated from field: string room_name = 1;
   */
  roomName: string;

  /**
   * required
   *
   * @generated from field: string identity = 2;
   */
  identity: string;

  /**
   * (default false)
   *
   * @generated from field: bool screen_share = 3;
   */
  screenShare: boolean;

  /**
   * @generated from oneof livekit.ParticipantEgressRequest.options
   */
  options: {
    /**
     * (default H264_720P_30)
     *
     * @generated from field: livekit.EncodingOptionsPreset preset = 4;
     */
    value: EncodingOptionsPreset;
    case: "preset";
  } | {
    /**
     * (optional)
     *
     * @generated from field: livekit.EncodingOptions advanced = 5;
     */
    value: EncodingOptions;
    case: "advanced";
  } | { case: undefined; value?: undefined };

  /**
   * @generated from field: repeated livekit.EncodedFileOutput file_outputs = 6;
   */
  fileOutputs: EncodedFileOutput[];

  /**
   * @generated from field: repeated livekit.StreamOutput stream_outputs = 7;
   */
  streamOutputs: StreamOutput[];

  /**
   * @generated from field: repeated livekit.SegmentedFileOutput segment_outputs = 8;
   */
  segmentOutputs: SegmentedFileOutput[];

  /**
   * @generated from field: repeated livekit.ImageOutput image_outputs = 9;
   */
  imageOutputs: ImageOutput[];

  /**
   * extra webhooks to call for this request
   *
   * @generated from field: repeated livekit.WebhookConfig webhooks = 10;
   */
  webhooks: WebhookConfig[];

  constructor(data?: PartialMessage<ParticipantEgressRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.ParticipantEgressRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ParticipantEgressRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ParticipantEgressRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ParticipantEgressRequest;

  static equals(a: ParticipantEgressRequest | PlainMessage<ParticipantEgressRequest> | undefined, b: ParticipantEgressRequest | PlainMessage<ParticipantEgressRequest> | undefined): boolean;
}

/**
 * containerize up to one audio and one video track
 *
 * @generated from message livekit.TrackCompositeEgressRequest
 */
declare class TrackCompositeEgressRequest extends Message<TrackCompositeEgressRequest> {
  /**
   * required
   *
   * @generated from field: string room_name = 1;
   */
  roomName: string;

  /**
   * (optional)
   *
   * @generated from field: string audio_track_id = 2;
   */
  audioTrackId: string;

  /**
   * (optional)
   *
   * @generated from field: string video_track_id = 3;
   */
  videoTrackId: string;

  /**
   * deprecated (use _output fields)
   *
   * @generated from oneof livekit.TrackCompositeEgressRequest.output
   */
  output: {
    /**
     * @generated from field: livekit.EncodedFileOutput file = 4 [deprecated = true];
     * @deprecated
     */
    value: EncodedFileOutput;
    case: "file";
  } | {
    /**
     * @generated from field: livekit.StreamOutput stream = 5 [deprecated = true];
     * @deprecated
     */
    value: StreamOutput;
    case: "stream";
  } | {
    /**
     * @generated from field: livekit.SegmentedFileOutput segments = 8 [deprecated = true];
     * @deprecated
     */
    value: SegmentedFileOutput;
    case: "segments";
  } | { case: undefined; value?: undefined };

  /**
   * @generated from oneof livekit.TrackCompositeEgressRequest.options
   */
  options: {
    /**
     * (default H264_720P_30)
     *
     * @generated from field: livekit.EncodingOptionsPreset preset = 6;
     */
    value: EncodingOptionsPreset;
    case: "preset";
  } | {
    /**
     * (optional)
     *
     * @generated from field: livekit.EncodingOptions advanced = 7;
     */
    value: EncodingOptions;
    case: "advanced";
  } | { case: undefined; value?: undefined };

  /**
   * @generated from field: repeated livekit.EncodedFileOutput file_outputs = 11;
   */
  fileOutputs: EncodedFileOutput[];

  /**
   * @generated from field: repeated livekit.StreamOutput stream_outputs = 12;
   */
  streamOutputs: StreamOutput[];

  /**
   * @generated from field: repeated livekit.SegmentedFileOutput segment_outputs = 13;
   */
  segmentOutputs: SegmentedFileOutput[];

  /**
   * @generated from field: repeated livekit.ImageOutput image_outputs = 14;
   */
  imageOutputs: ImageOutput[];

  /**
   * extra webhooks to call for this request
   *
   * @generated from field: repeated livekit.WebhookConfig webhooks = 15;
   */
  webhooks: WebhookConfig[];

  constructor(data?: PartialMessage<TrackCompositeEgressRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.TrackCompositeEgressRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): TrackCompositeEgressRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): TrackCompositeEgressRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): TrackCompositeEgressRequest;

  static equals(a: TrackCompositeEgressRequest | PlainMessage<TrackCompositeEgressRequest> | undefined, b: TrackCompositeEgressRequest | PlainMessage<TrackCompositeEgressRequest> | undefined): boolean;
}

/**
 * record tracks individually, without transcoding
 *
 * @generated from message livekit.TrackEgressRequest
 */
declare class TrackEgressRequest extends Message<TrackEgressRequest> {
  /**
   * required
   *
   * @generated from field: string room_name = 1;
   */
  roomName: string;

  /**
   * required
   *
   * @generated from field: string track_id = 2;
   */
  trackId: string;

  /**
   * required
   *
   * @generated from oneof livekit.TrackEgressRequest.output
   */
  output: {
    /**
     * @generated from field: livekit.DirectFileOutput file = 3;
     */
    value: DirectFileOutput;
    case: "file";
  } | {
    /**
     * @generated from field: string websocket_url = 4;
     */
    value: string;
    case: "websocketUrl";
  } | { case: undefined; value?: undefined };

  /**
   * extra webhooks to call for this request
   *
   * @generated from field: repeated livekit.WebhookConfig webhooks = 5;
   */
  webhooks: WebhookConfig[];

  constructor(data?: PartialMessage<TrackEgressRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.TrackEgressRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): TrackEgressRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): TrackEgressRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): TrackEgressRequest;

  static equals(a: TrackEgressRequest | PlainMessage<TrackEgressRequest> | undefined, b: TrackEgressRequest | PlainMessage<TrackEgressRequest> | undefined): boolean;
}

/**
 * @generated from message livekit.EncodedFileOutput
 */
declare class EncodedFileOutput extends Message<EncodedFileOutput> {
  /**
   * (optional)
   *
   * @generated from field: livekit.EncodedFileType file_type = 1;
   */
  fileType: EncodedFileType;

  /**
   * see egress docs for templating (default {room_name}-{time})
   *
   * @generated from field: string filepath = 2;
   */
  filepath: string;

  /**
   * disable upload of manifest file (default false)
   *
   * @generated from field: bool disable_manifest = 6;
   */
  disableManifest: boolean;

  /**
   * @generated from oneof livekit.EncodedFileOutput.output
   */
  output: {
    /**
     * @generated from field: livekit.S3Upload s3 = 3;
     */
    value: S3Upload;
    case: "s3";
  } | {
    /**
     * @generated from field: livekit.GCPUpload gcp = 4;
     */
    value: GCPUpload;
    case: "gcp";
  } | {
    /**
     * @generated from field: livekit.AzureBlobUpload azure = 5;
     */
    value: AzureBlobUpload;
    case: "azure";
  } | {
    /**
     * @generated from field: livekit.AliOSSUpload aliOSS = 7;
     */
    value: AliOSSUpload;
    case: "aliOSS";
  } | { case: undefined; value?: undefined };

  constructor(data?: PartialMessage<EncodedFileOutput>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.EncodedFileOutput";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): EncodedFileOutput;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): EncodedFileOutput;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): EncodedFileOutput;

  static equals(a: EncodedFileOutput | PlainMessage<EncodedFileOutput> | undefined, b: EncodedFileOutput | PlainMessage<EncodedFileOutput> | undefined): boolean;
}

/**
 * Used to generate HLS segments or other kind of segmented output
 *
 * @generated from message livekit.SegmentedFileOutput
 */
declare class SegmentedFileOutput extends Message<SegmentedFileOutput> {
  /**
   * (optional)
   *
   * @generated from field: livekit.SegmentedFileProtocol protocol = 1;
   */
  protocol: SegmentedFileProtocol;

  /**
   * (optional)
   *
   * @generated from field: string filename_prefix = 2;
   */
  filenamePrefix: string;

  /**
   * (optional)
   *
   * @generated from field: string playlist_name = 3;
   */
  playlistName: string;

  /**
   * (optional, disabled if not provided). Path of a live playlist
   *
   * @generated from field: string live_playlist_name = 11;
   */
  livePlaylistName: string;

  /**
   * in seconds (optional)
   *
   * @generated from field: uint32 segment_duration = 4;
   */
  segmentDuration: number;

  /**
   * (optional, default INDEX)
   *
   * @generated from field: livekit.SegmentedFileSuffix filename_suffix = 10;
   */
  filenameSuffix: SegmentedFileSuffix;

  /**
   * disable upload of manifest file (default false)
   *
   * @generated from field: bool disable_manifest = 8;
   */
  disableManifest: boolean;

  /**
   * required
   *
   * @generated from oneof livekit.SegmentedFileOutput.output
   */
  output: {
    /**
     * @generated from field: livekit.S3Upload s3 = 5;
     */
    value: S3Upload;
    case: "s3";
  } | {
    /**
     * @generated from field: livekit.GCPUpload gcp = 6;
     */
    value: GCPUpload;
    case: "gcp";
  } | {
    /**
     * @generated from field: livekit.AzureBlobUpload azure = 7;
     */
    value: AzureBlobUpload;
    case: "azure";
  } | {
    /**
     * @generated from field: livekit.AliOSSUpload aliOSS = 9;
     */
    value: AliOSSUpload;
    case: "aliOSS";
  } | { case: undefined; value?: undefined };

  constructor(data?: PartialMessage<SegmentedFileOutput>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.SegmentedFileOutput";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SegmentedFileOutput;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SegmentedFileOutput;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SegmentedFileOutput;

  static equals(a: SegmentedFileOutput | PlainMessage<SegmentedFileOutput> | undefined, b: SegmentedFileOutput | PlainMessage<SegmentedFileOutput> | undefined): boolean;
}

/**
 * @generated from message livekit.DirectFileOutput
 */
declare class DirectFileOutput extends Message<DirectFileOutput> {
  /**
   * see egress docs for templating (default {track_id}-{time})
   *
   * @generated from field: string filepath = 1;
   */
  filepath: string;

  /**
   * disable upload of manifest file (default false)
   *
   * @generated from field: bool disable_manifest = 5;
   */
  disableManifest: boolean;

  /**
   * @generated from oneof livekit.DirectFileOutput.output
   */
  output: {
    /**
     * @generated from field: livekit.S3Upload s3 = 2;
     */
    value: S3Upload;
    case: "s3";
  } | {
    /**
     * @generated from field: livekit.GCPUpload gcp = 3;
     */
    value: GCPUpload;
    case: "gcp";
  } | {
    /**
     * @generated from field: livekit.AzureBlobUpload azure = 4;
     */
    value: AzureBlobUpload;
    case: "azure";
  } | {
    /**
     * @generated from field: livekit.AliOSSUpload aliOSS = 6;
     */
    value: AliOSSUpload;
    case: "aliOSS";
  } | { case: undefined; value?: undefined };

  constructor(data?: PartialMessage<DirectFileOutput>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.DirectFileOutput";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DirectFileOutput;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DirectFileOutput;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DirectFileOutput;

  static equals(a: DirectFileOutput | PlainMessage<DirectFileOutput> | undefined, b: DirectFileOutput | PlainMessage<DirectFileOutput> | undefined): boolean;
}

/**
 * @generated from message livekit.ImageOutput
 */
declare class ImageOutput extends Message<ImageOutput> {
  /**
   * in seconds (required)
   *
   * @generated from field: uint32 capture_interval = 1;
   */
  captureInterval: number;

  /**
   * (optional, defaults to track width)
   *
   * @generated from field: int32 width = 2;
   */
  width: number;

  /**
   * (optional, defaults to track height)
   *
   * @generated from field: int32 height = 3;
   */
  height: number;

  /**
   * (optional)
   *
   * @generated from field: string filename_prefix = 4;
   */
  filenamePrefix: string;

  /**
   * (optional, default INDEX)
   *
   * @generated from field: livekit.ImageFileSuffix filename_suffix = 5;
   */
  filenameSuffix: ImageFileSuffix;

  /**
   * (optional)
   *
   * @generated from field: livekit.ImageCodec image_codec = 6;
   */
  imageCodec: ImageCodec;

  /**
   * disable upload of manifest file (default false)
   *
   * @generated from field: bool disable_manifest = 7;
   */
  disableManifest: boolean;

  /**
   * required
   *
   * @generated from oneof livekit.ImageOutput.output
   */
  output: {
    /**
     * @generated from field: livekit.S3Upload s3 = 8;
     */
    value: S3Upload;
    case: "s3";
  } | {
    /**
     * @generated from field: livekit.GCPUpload gcp = 9;
     */
    value: GCPUpload;
    case: "gcp";
  } | {
    /**
     * @generated from field: livekit.AzureBlobUpload azure = 10;
     */
    value: AzureBlobUpload;
    case: "azure";
  } | {
    /**
     * @generated from field: livekit.AliOSSUpload aliOSS = 11;
     */
    value: AliOSSUpload;
    case: "aliOSS";
  } | { case: undefined; value?: undefined };

  constructor(data?: PartialMessage<ImageOutput>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.ImageOutput";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ImageOutput;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ImageOutput;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ImageOutput;

  static equals(a: ImageOutput | PlainMessage<ImageOutput> | undefined, b: ImageOutput | PlainMessage<ImageOutput> | undefined): boolean;
}

/**
 * @generated from message livekit.S3Upload
 */
declare class S3Upload extends Message<S3Upload> {
  /**
   * @generated from field: string access_key = 1;
   */
  accessKey: string;

  /**
   * @generated from field: string secret = 2;
   */
  secret: string;

  /**
   * @generated from field: string session_token = 11;
   */
  sessionToken: string;

  /**
   * @generated from field: string region = 3;
   */
  region: string;

  /**
   * @generated from field: string endpoint = 4;
   */
  endpoint: string;

  /**
   * @generated from field: string bucket = 5;
   */
  bucket: string;

  /**
   * @generated from field: bool force_path_style = 6;
   */
  forcePathStyle: boolean;

  /**
   * @generated from field: map<string, string> metadata = 7;
   */
  metadata: { [key: string]: string };

  /**
   * @generated from field: string tagging = 8;
   */
  tagging: string;

  /**
   * Content-Disposition header
   *
   * @generated from field: string content_disposition = 9;
   */
  contentDisposition: string;

  /**
   * @generated from field: livekit.ProxyConfig proxy = 10;
   */
  proxy?: ProxyConfig;

  constructor(data?: PartialMessage<S3Upload>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.S3Upload";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): S3Upload;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): S3Upload;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): S3Upload;

  static equals(a: S3Upload | PlainMessage<S3Upload> | undefined, b: S3Upload | PlainMessage<S3Upload> | undefined): boolean;
}

/**
 * @generated from message livekit.GCPUpload
 */
declare class GCPUpload extends Message<GCPUpload> {
  /**
   * service account credentials serialized in JSON "credentials.json"
   *
   * @generated from field: string credentials = 1;
   */
  credentials: string;

  /**
   * @generated from field: string bucket = 2;
   */
  bucket: string;

  /**
   * @generated from field: livekit.ProxyConfig proxy = 3;
   */
  proxy?: ProxyConfig;

  constructor(data?: PartialMessage<GCPUpload>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.GCPUpload";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GCPUpload;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GCPUpload;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GCPUpload;

  static equals(a: GCPUpload | PlainMessage<GCPUpload> | undefined, b: GCPUpload | PlainMessage<GCPUpload> | undefined): boolean;
}

/**
 * @generated from message livekit.AzureBlobUpload
 */
declare class AzureBlobUpload extends Message<AzureBlobUpload> {
  /**
   * @generated from field: string account_name = 1;
   */
  accountName: string;

  /**
   * @generated from field: string account_key = 2;
   */
  accountKey: string;

  /**
   * @generated from field: string container_name = 3;
   */
  containerName: string;

  constructor(data?: PartialMessage<AzureBlobUpload>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.AzureBlobUpload";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AzureBlobUpload;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AzureBlobUpload;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AzureBlobUpload;

  static equals(a: AzureBlobUpload | PlainMessage<AzureBlobUpload> | undefined, b: AzureBlobUpload | PlainMessage<AzureBlobUpload> | undefined): boolean;
}

/**
 * @generated from message livekit.AliOSSUpload
 */
declare class AliOSSUpload extends Message<AliOSSUpload> {
  /**
   * @generated from field: string access_key = 1;
   */
  accessKey: string;

  /**
   * @generated from field: string secret = 2;
   */
  secret: string;

  /**
   * @generated from field: string region = 3;
   */
  region: string;

  /**
   * @generated from field: string endpoint = 4;
   */
  endpoint: string;

  /**
   * @generated from field: string bucket = 5;
   */
  bucket: string;

  constructor(data?: PartialMessage<AliOSSUpload>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.AliOSSUpload";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AliOSSUpload;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AliOSSUpload;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AliOSSUpload;

  static equals(a: AliOSSUpload | PlainMessage<AliOSSUpload> | undefined, b: AliOSSUpload | PlainMessage<AliOSSUpload> | undefined): boolean;
}

/**
 * @generated from message livekit.ProxyConfig
 */
declare class ProxyConfig extends Message<ProxyConfig> {
  /**
   * @generated from field: string url = 1;
   */
  url: string;

  /**
   * @generated from field: string username = 2;
   */
  username: string;

  /**
   * @generated from field: string password = 3;
   */
  password: string;

  constructor(data?: PartialMessage<ProxyConfig>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.ProxyConfig";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ProxyConfig;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ProxyConfig;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ProxyConfig;

  static equals(a: ProxyConfig | PlainMessage<ProxyConfig> | undefined, b: ProxyConfig | PlainMessage<ProxyConfig> | undefined): boolean;
}

/**
 * @generated from message livekit.StreamOutput
 */
declare class StreamOutput extends Message<StreamOutput> {
  /**
   * required
   *
   * @generated from field: livekit.StreamProtocol protocol = 1;
   */
  protocol: StreamProtocol;

  /**
   * required
   *
   * @generated from field: repeated string urls = 2;
   */
  urls: string[];

  constructor(data?: PartialMessage<StreamOutput>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.StreamOutput";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): StreamOutput;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): StreamOutput;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): StreamOutput;

  static equals(a: StreamOutput | PlainMessage<StreamOutput> | undefined, b: StreamOutput | PlainMessage<StreamOutput> | undefined): boolean;
}

/**
 * @generated from message livekit.EncodingOptions
 */
declare class EncodingOptions extends Message<EncodingOptions> {
  /**
   * (default 1920)
   *
   * @generated from field: int32 width = 1;
   */
  width: number;

  /**
   * (default 1080)
   *
   * @generated from field: int32 height = 2;
   */
  height: number;

  /**
   * (default 24)
   *
   * @generated from field: int32 depth = 3;
   */
  depth: number;

  /**
   * (default 30)
   *
   * @generated from field: int32 framerate = 4;
   */
  framerate: number;

  /**
   * (default OPUS)
   *
   * @generated from field: livekit.AudioCodec audio_codec = 5;
   */
  audioCodec: AudioCodec;

  /**
   * (default 128)
   *
   * @generated from field: int32 audio_bitrate = 6;
   */
  audioBitrate: number;

  /**
   * quality setting on audio encoder
   *
   * @generated from field: int32 audio_quality = 11;
   */
  audioQuality: number;

  /**
   * (default 44100)
   *
   * @generated from field: int32 audio_frequency = 7;
   */
  audioFrequency: number;

  /**
   * (default H264_MAIN)
   *
   * @generated from field: livekit.VideoCodec video_codec = 8;
   */
  videoCodec: VideoCodec;

  /**
   * (default 4500)
   *
   * @generated from field: int32 video_bitrate = 9;
   */
  videoBitrate: number;

  /**
   * quality setting on video encoder
   *
   * @generated from field: int32 video_quality = 12;
   */
  videoQuality: number;

  /**
   * in seconds (default 4s for streaming, segment duration for segmented output, encoder default for files)
   *
   * @generated from field: double key_frame_interval = 10;
   */
  keyFrameInterval: number;

  constructor(data?: PartialMessage<EncodingOptions>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.EncodingOptions";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): EncodingOptions;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): EncodingOptions;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): EncodingOptions;

  static equals(a: EncodingOptions | PlainMessage<EncodingOptions> | undefined, b: EncodingOptions | PlainMessage<EncodingOptions> | undefined): boolean;
}

/**
 * @generated from message livekit.UpdateLayoutRequest
 */
declare class UpdateLayoutRequest extends Message<UpdateLayoutRequest> {
  /**
   * @generated from field: string egress_id = 1;
   */
  egressId: string;

  /**
   * @generated from field: string layout = 2;
   */
  layout: string;

  constructor(data?: PartialMessage<UpdateLayoutRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.UpdateLayoutRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateLayoutRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateLayoutRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateLayoutRequest;

  static equals(a: UpdateLayoutRequest | PlainMessage<UpdateLayoutRequest> | undefined, b: UpdateLayoutRequest | PlainMessage<UpdateLayoutRequest> | undefined): boolean;
}

/**
 * @generated from message livekit.UpdateStreamRequest
 */
declare class UpdateStreamRequest extends Message<UpdateStreamRequest> {
  /**
   * @generated from field: string egress_id = 1;
   */
  egressId: string;

  /**
   * @generated from field: repeated string add_output_urls = 2;
   */
  addOutputUrls: string[];

  /**
   * @generated from field: repeated string remove_output_urls = 3;
   */
  removeOutputUrls: string[];

  constructor(data?: PartialMessage<UpdateStreamRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.UpdateStreamRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateStreamRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateStreamRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateStreamRequest;

  static equals(a: UpdateStreamRequest | PlainMessage<UpdateStreamRequest> | undefined, b: UpdateStreamRequest | PlainMessage<UpdateStreamRequest> | undefined): boolean;
}

/**
 * @generated from message livekit.ListEgressRequest
 */
declare class ListEgressRequest extends Message<ListEgressRequest> {
  /**
   * (optional, filter by room name)
   *
   * @generated from field: string room_name = 1;
   */
  roomName: string;

  /**
   * (optional, filter by egress ID)
   *
   * @generated from field: string egress_id = 2;
   */
  egressId: string;

  /**
   * (optional, list active egress only)
   *
   * @generated from field: bool active = 3;
   */
  active: boolean;

  constructor(data?: PartialMessage<ListEgressRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.ListEgressRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListEgressRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListEgressRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListEgressRequest;

  static equals(a: ListEgressRequest | PlainMessage<ListEgressRequest> | undefined, b: ListEgressRequest | PlainMessage<ListEgressRequest> | undefined): boolean;
}

/**
 * @generated from message livekit.ListEgressResponse
 */
declare class ListEgressResponse extends Message<ListEgressResponse> {
  /**
   * @generated from field: repeated livekit.EgressInfo items = 1;
   */
  items: EgressInfo[];

  constructor(data?: PartialMessage<ListEgressResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.ListEgressResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListEgressResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListEgressResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListEgressResponse;

  static equals(a: ListEgressResponse | PlainMessage<ListEgressResponse> | undefined, b: ListEgressResponse | PlainMessage<ListEgressResponse> | undefined): boolean;
}

/**
 * @generated from message livekit.StopEgressRequest
 */
declare class StopEgressRequest extends Message<StopEgressRequest> {
  /**
   * @generated from field: string egress_id = 1;
   */
  egressId: string;

  constructor(data?: PartialMessage<StopEgressRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.StopEgressRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): StopEgressRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): StopEgressRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): StopEgressRequest;

  static equals(a: StopEgressRequest | PlainMessage<StopEgressRequest> | undefined, b: StopEgressRequest | PlainMessage<StopEgressRequest> | undefined): boolean;
}

/**
 * @generated from message livekit.EgressInfo
 */
declare class EgressInfo extends Message<EgressInfo> {
  /**
   * @generated from field: string egress_id = 1;
   */
  egressId: string;

  /**
   * @generated from field: string room_id = 2;
   */
  roomId: string;

  /**
   * @generated from field: string room_name = 13;
   */
  roomName: string;

  /**
   * @generated from field: livekit.EgressSourceType source_type = 26;
   */
  sourceType: EgressSourceType;

  /**
   * @generated from field: livekit.EgressStatus status = 3;
   */
  status: EgressStatus;

  /**
   * @generated from field: int64 started_at = 10;
   */
  startedAt: bigint;

  /**
   * @generated from field: int64 ended_at = 11;
   */
  endedAt: bigint;

  /**
   * @generated from field: int64 updated_at = 18;
   */
  updatedAt: bigint;

  /**
   * @generated from field: string details = 21;
   */
  details: string;

  /**
   * @generated from field: string error = 9;
   */
  error: string;

  /**
   * @generated from field: int32 error_code = 22;
   */
  errorCode: number;

  /**
   * @generated from oneof livekit.EgressInfo.request
   */
  request: {
    /**
     * @generated from field: livekit.RoomCompositeEgressRequest room_composite = 4;
     */
    value: RoomCompositeEgressRequest;
    case: "roomComposite";
  } | {
    /**
     * @generated from field: livekit.WebEgressRequest web = 14;
     */
    value: WebEgressRequest;
    case: "web";
  } | {
    /**
     * @generated from field: livekit.ParticipantEgressRequest participant = 19;
     */
    value: ParticipantEgressRequest;
    case: "participant";
  } | {
    /**
     * @generated from field: livekit.TrackCompositeEgressRequest track_composite = 5;
     */
    value: TrackCompositeEgressRequest;
    case: "trackComposite";
  } | {
    /**
     * @generated from field: livekit.TrackEgressRequest track = 6;
     */
    value: TrackEgressRequest;
    case: "track";
  } | { case: undefined; value?: undefined };

  /**
   * deprecated (use _result fields)
   *
   * @generated from oneof livekit.EgressInfo.result
   */
  result: {
    /**
     * @generated from field: livekit.StreamInfoList stream = 7 [deprecated = true];
     * @deprecated
     */
    value: StreamInfoList;
    case: "stream";
  } | {
    /**
     * @generated from field: livekit.FileInfo file = 8 [deprecated = true];
     * @deprecated
     */
    value: FileInfo;
    case: "file";
  } | {
    /**
     * @generated from field: livekit.SegmentsInfo segments = 12 [deprecated = true];
     * @deprecated
     */
    value: SegmentsInfo;
    case: "segments";
  } | { case: undefined; value?: undefined };

  /**
   * @generated from field: repeated livekit.StreamInfo stream_results = 15;
   */
  streamResults: StreamInfo[];

  /**
   * @generated from field: repeated livekit.FileInfo file_results = 16;
   */
  fileResults: FileInfo[];

  /**
   * @generated from field: repeated livekit.SegmentsInfo segment_results = 17;
   */
  segmentResults: SegmentsInfo[];

  /**
   * @generated from field: repeated livekit.ImagesInfo image_results = 20;
   */
  imageResults: ImagesInfo[];

  /**
   * @generated from field: string manifest_location = 23;
   */
  manifestLocation: string;

  /**
   * next ID: 27
   *
   * @generated from field: bool backup_storage_used = 25;
   */
  backupStorageUsed: boolean;

  constructor(data?: PartialMessage<EgressInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.EgressInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): EgressInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): EgressInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): EgressInfo;

  static equals(a: EgressInfo | PlainMessage<EgressInfo> | undefined, b: EgressInfo | PlainMessage<EgressInfo> | undefined): boolean;
}

/**
 * @generated from message livekit.StreamInfoList
 * @deprecated
 */
declare class StreamInfoList extends Message<StreamInfoList> {
  /**
   * @generated from field: repeated livekit.StreamInfo info = 1;
   */
  info: StreamInfo[];

  constructor(data?: PartialMessage<StreamInfoList>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.StreamInfoList";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): StreamInfoList;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): StreamInfoList;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): StreamInfoList;

  static equals(a: StreamInfoList | PlainMessage<StreamInfoList> | undefined, b: StreamInfoList | PlainMessage<StreamInfoList> | undefined): boolean;
}

/**
 * @generated from message livekit.StreamInfo
 */
declare class StreamInfo extends Message<StreamInfo> {
  /**
   * @generated from field: string url = 1;
   */
  url: string;

  /**
   * @generated from field: int64 started_at = 2;
   */
  startedAt: bigint;

  /**
   * @generated from field: int64 ended_at = 3;
   */
  endedAt: bigint;

  /**
   * @generated from field: int64 duration = 4;
   */
  duration: bigint;

  /**
   * @generated from field: livekit.StreamInfo.Status status = 5;
   */
  status: StreamInfo_Status;

  /**
   * @generated from field: string error = 6;
   */
  error: string;

  constructor(data?: PartialMessage<StreamInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.StreamInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): StreamInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): StreamInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): StreamInfo;

  static equals(a: StreamInfo | PlainMessage<StreamInfo> | undefined, b: StreamInfo | PlainMessage<StreamInfo> | undefined): boolean;
}

/**
 * @generated from enum livekit.StreamInfo.Status
 */
declare enum StreamInfo_Status {
  /**
   * @generated from enum value: ACTIVE = 0;
   */
  ACTIVE = 0,

  /**
   * @generated from enum value: FINISHED = 1;
   */
  FINISHED = 1,

  /**
   * @generated from enum value: FAILED = 2;
   */
  FAILED = 2,
}

/**
 * @generated from message livekit.FileInfo
 */
declare class FileInfo extends Message<FileInfo> {
  /**
   * @generated from field: string filename = 1;
   */
  filename: string;

  /**
   * @generated from field: int64 started_at = 2;
   */
  startedAt: bigint;

  /**
   * @generated from field: int64 ended_at = 3;
   */
  endedAt: bigint;

  /**
   * @generated from field: int64 duration = 6;
   */
  duration: bigint;

  /**
   * @generated from field: int64 size = 4;
   */
  size: bigint;

  /**
   * @generated from field: string location = 5;
   */
  location: string;

  constructor(data?: PartialMessage<FileInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.FileInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): FileInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): FileInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): FileInfo;

  static equals(a: FileInfo | PlainMessage<FileInfo> | undefined, b: FileInfo | PlainMessage<FileInfo> | undefined): boolean;
}

/**
 * @generated from message livekit.SegmentsInfo
 */
declare class SegmentsInfo extends Message<SegmentsInfo> {
  /**
   * @generated from field: string playlist_name = 1;
   */
  playlistName: string;

  /**
   * @generated from field: string live_playlist_name = 8;
   */
  livePlaylistName: string;

  /**
   * @generated from field: int64 duration = 2;
   */
  duration: bigint;

  /**
   * @generated from field: int64 size = 3;
   */
  size: bigint;

  /**
   * @generated from field: string playlist_location = 4;
   */
  playlistLocation: string;

  /**
   * @generated from field: string live_playlist_location = 9;
   */
  livePlaylistLocation: string;

  /**
   * @generated from field: int64 segment_count = 5;
   */
  segmentCount: bigint;

  /**
   * @generated from field: int64 started_at = 6;
   */
  startedAt: bigint;

  /**
   * @generated from field: int64 ended_at = 7;
   */
  endedAt: bigint;

  constructor(data?: PartialMessage<SegmentsInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.SegmentsInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SegmentsInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SegmentsInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SegmentsInfo;

  static equals(a: SegmentsInfo | PlainMessage<SegmentsInfo> | undefined, b: SegmentsInfo | PlainMessage<SegmentsInfo> | undefined): boolean;
}

/**
 * @generated from message livekit.ImagesInfo
 */
declare class ImagesInfo extends Message<ImagesInfo> {
  /**
   * @generated from field: string filename_prefix = 4;
   */
  filenamePrefix: string;

  /**
   * @generated from field: int64 image_count = 1;
   */
  imageCount: bigint;

  /**
   * @generated from field: int64 started_at = 2;
   */
  startedAt: bigint;

  /**
   * @generated from field: int64 ended_at = 3;
   */
  endedAt: bigint;

  constructor(data?: PartialMessage<ImagesInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.ImagesInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ImagesInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ImagesInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ImagesInfo;

  static equals(a: ImagesInfo | PlainMessage<ImagesInfo> | undefined, b: ImagesInfo | PlainMessage<ImagesInfo> | undefined): boolean;
}

/**
 * @generated from message livekit.AutoParticipantEgress
 */
declare class AutoParticipantEgress extends Message<AutoParticipantEgress> {
  /**
   * @generated from oneof livekit.AutoParticipantEgress.options
   */
  options: {
    /**
     * (default H264_720P_30)
     *
     * @generated from field: livekit.EncodingOptionsPreset preset = 1;
     */
    value: EncodingOptionsPreset;
    case: "preset";
  } | {
    /**
     * (optional)
     *
     * @generated from field: livekit.EncodingOptions advanced = 2;
     */
    value: EncodingOptions;
    case: "advanced";
  } | { case: undefined; value?: undefined };

  /**
   * @generated from field: repeated livekit.EncodedFileOutput file_outputs = 3;
   */
  fileOutputs: EncodedFileOutput[];

  /**
   * @generated from field: repeated livekit.SegmentedFileOutput segment_outputs = 4;
   */
  segmentOutputs: SegmentedFileOutput[];

  constructor(data?: PartialMessage<AutoParticipantEgress>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.AutoParticipantEgress";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AutoParticipantEgress;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AutoParticipantEgress;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AutoParticipantEgress;

  static equals(a: AutoParticipantEgress | PlainMessage<AutoParticipantEgress> | undefined, b: AutoParticipantEgress | PlainMessage<AutoParticipantEgress> | undefined): boolean;
}

/**
 * @generated from message livekit.AutoTrackEgress
 */
declare class AutoTrackEgress extends Message<AutoTrackEgress> {
  /**
   * see docs for templating (default {track_id}-{time})
   *
   * @generated from field: string filepath = 1;
   */
  filepath: string;

  /**
   * disables upload of json manifest file (default false)
   *
   * @generated from field: bool disable_manifest = 5;
   */
  disableManifest: boolean;

  /**
   * @generated from oneof livekit.AutoTrackEgress.output
   */
  output: {
    /**
     * @generated from field: livekit.S3Upload s3 = 2;
     */
    value: S3Upload;
    case: "s3";
  } | {
    /**
     * @generated from field: livekit.GCPUpload gcp = 3;
     */
    value: GCPUpload;
    case: "gcp";
  } | {
    /**
     * @generated from field: livekit.AzureBlobUpload azure = 4;
     */
    value: AzureBlobUpload;
    case: "azure";
  } | {
    /**
     * @generated from field: livekit.AliOSSUpload aliOSS = 6;
     */
    value: AliOSSUpload;
    case: "aliOSS";
  } | { case: undefined; value?: undefined };

  constructor(data?: PartialMessage<AutoTrackEgress>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.AutoTrackEgress";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AutoTrackEgress;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AutoTrackEgress;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AutoTrackEgress;

  static equals(a: AutoTrackEgress | PlainMessage<AutoTrackEgress> | undefined, b: AutoTrackEgress | PlainMessage<AutoTrackEgress> | undefined): boolean;
}

// Copyright 2023 LiveKit, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.



/**
 * @generated from enum livekit.IngressInput
 */
declare enum IngressInput {
  /**
   * @generated from enum value: RTMP_INPUT = 0;
   */
  RTMP_INPUT = 0,

  /**
   * @generated from enum value: WHIP_INPUT = 1;
   */
  WHIP_INPUT = 1,

  /**
   * Pull from the provided URL. Only HTTP url are supported, serving either a single media file or a HLS stream
   *
   * @generated from enum value: URL_INPUT = 2;
   */
  URL_INPUT = 2,
}

/**
 * @generated from enum livekit.IngressAudioEncodingPreset
 */
declare enum IngressAudioEncodingPreset {
  /**
   * OPUS, 2 channels, 96kbps
   *
   * @generated from enum value: OPUS_STEREO_96KBPS = 0;
   */
  OPUS_STEREO_96KBPS = 0,

  /**
   * OPUS, 1 channel, 64kbps
   *
   * @generated from enum value: OPUS_MONO_64KBS = 1;
   */
  OPUS_MONO_64KBS = 1,
}

/**
 * @generated from enum livekit.IngressVideoEncodingPreset
 */
declare enum IngressVideoEncodingPreset {
  /**
   * 1280x720,  30fps, 1900kbps main layer, 3 layers total
   *
   * @generated from enum value: H264_720P_30FPS_3_LAYERS = 0;
   */
  H264_720P_30FPS_3_LAYERS = 0,

  /**
   * 1980x1080, 30fps, 3500kbps main layer, 3 layers total
   *
   * @generated from enum value: H264_1080P_30FPS_3_LAYERS = 1;
   */
  H264_1080P_30FPS_3_LAYERS = 1,

  /**
   *  960x540,  25fps, 1000kbps  main layer, 2 layers total
   *
   * @generated from enum value: H264_540P_25FPS_2_LAYERS = 2;
   */
  H264_540P_25FPS_2_LAYERS = 2,

  /**
   * 1280x720,  30fps, 1900kbps, no simulcast
   *
   * @generated from enum value: H264_720P_30FPS_1_LAYER = 3;
   */
  H264_720P_30FPS_1_LAYER = 3,

  /**
   * 1980x1080, 30fps, 3500kbps, no simulcast
   *
   * @generated from enum value: H264_1080P_30FPS_1_LAYER = 4;
   */
  H264_1080P_30FPS_1_LAYER = 4,

  /**
   * 1280x720,  30fps, 2500kbps main layer, 3 layers total, higher bitrate for high motion, harder to encode content
   *
   * @generated from enum value: H264_720P_30FPS_3_LAYERS_HIGH_MOTION = 5;
   */
  H264_720P_30FPS_3_LAYERS_HIGH_MOTION = 5,

  /**
   * 1980x1080, 30fps, 4500kbps main layer, 3 layers total, higher bitrate for high motion, harder to encode content
   *
   * @generated from enum value: H264_1080P_30FPS_3_LAYERS_HIGH_MOTION = 6;
   */
  H264_1080P_30FPS_3_LAYERS_HIGH_MOTION = 6,

  /**
   *  960x540,  25fps, 1300kbps  main layer, 2 layers total, higher bitrate for high motion, harder to encode content
   *
   * @generated from enum value: H264_540P_25FPS_2_LAYERS_HIGH_MOTION = 7;
   */
  H264_540P_25FPS_2_LAYERS_HIGH_MOTION = 7,

  /**
   * 1280x720,  30fps, 2500kbps, no simulcast, higher bitrate for high motion, harder to encode content
   *
   * @generated from enum value: H264_720P_30FPS_1_LAYER_HIGH_MOTION = 8;
   */
  H264_720P_30FPS_1_LAYER_HIGH_MOTION = 8,

  /**
   * 1980x1080, 30fps, 4500kbps, no simulcast, higher bitrate for high motion, harder to encode content
   *
   * @generated from enum value: H264_1080P_30FPS_1_LAYER_HIGH_MOTION = 9;
   */
  H264_1080P_30FPS_1_LAYER_HIGH_MOTION = 9,
}

/**
 * @generated from message livekit.CreateIngressRequest
 */
declare class CreateIngressRequest extends Message<CreateIngressRequest> {
  /**
   * @generated from field: livekit.IngressInput input_type = 1;
   */
  inputType: IngressInput;

  /**
   * Where to pull media from, only for URL input type
   *
   * @generated from field: string url = 9;
   */
  url: string;

  /**
   * User provided identifier for the ingress
   *
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * room to publish to
   *
   * @generated from field: string room_name = 3;
   */
  roomName: string;

  /**
   * publish as participant
   *
   * @generated from field: string participant_identity = 4;
   */
  participantIdentity: string;

  /**
   * name of publishing participant (used for display only)
   *
   * @generated from field: string participant_name = 5;
   */
  participantName: string;

  /**
   * metadata associated with the publishing participant
   *
   * @generated from field: string participant_metadata = 10;
   */
  participantMetadata: string;

  /**
   * [depreacted ] whether to pass through the incoming media without transcoding, only compatible with some input types. Use `enable_transcoding` instead.
   *
   * @generated from field: bool bypass_transcoding = 8 [deprecated = true];
   * @deprecated
   */
  bypassTranscoding: boolean;

  /**
   * Whether to transcode the ingested media. Only WHIP supports disabling transcoding currently. WHIP will default to transcoding disabled. Replaces `bypass_transcoding. 
   *
   * @generated from field: optional bool enable_transcoding = 11;
   */
  enableTranscoding?: boolean;

  /**
   * @generated from field: livekit.IngressAudioOptions audio = 6;
   */
  audio?: IngressAudioOptions;

  /**
   * @generated from field: livekit.IngressVideoOptions video = 7;
   */
  video?: IngressVideoOptions;

  /**
   * The default value is true and when set to false, the new connection attempts will be rejected
   *
   * @generated from field: optional bool enabled = 12;
   */
  enabled?: boolean;

  constructor(data?: PartialMessage<CreateIngressRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.CreateIngressRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreateIngressRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreateIngressRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreateIngressRequest;

  static equals(a: CreateIngressRequest | PlainMessage<CreateIngressRequest> | undefined, b: CreateIngressRequest | PlainMessage<CreateIngressRequest> | undefined): boolean;
}

/**
 * @generated from message livekit.IngressAudioOptions
 */
declare class IngressAudioOptions extends Message<IngressAudioOptions> {
  /**
   * @generated from field: string name = 1;
   */
  name: string;

  /**
   * @generated from field: livekit.TrackSource source = 2;
   */
  source: TrackSource;

  /**
   * @generated from oneof livekit.IngressAudioOptions.encoding_options
   */
  encodingOptions: {
    /**
     * @generated from field: livekit.IngressAudioEncodingPreset preset = 3;
     */
    value: IngressAudioEncodingPreset;
    case: "preset";
  } | {
    /**
     * @generated from field: livekit.IngressAudioEncodingOptions options = 4;
     */
    value: IngressAudioEncodingOptions;
    case: "options";
  } | { case: undefined; value?: undefined };

  constructor(data?: PartialMessage<IngressAudioOptions>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.IngressAudioOptions";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): IngressAudioOptions;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): IngressAudioOptions;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): IngressAudioOptions;

  static equals(a: IngressAudioOptions | PlainMessage<IngressAudioOptions> | undefined, b: IngressAudioOptions | PlainMessage<IngressAudioOptions> | undefined): boolean;
}

/**
 * @generated from message livekit.IngressVideoOptions
 */
declare class IngressVideoOptions extends Message<IngressVideoOptions> {
  /**
   * @generated from field: string name = 1;
   */
  name: string;

  /**
   * @generated from field: livekit.TrackSource source = 2;
   */
  source: TrackSource;

  /**
   * @generated from oneof livekit.IngressVideoOptions.encoding_options
   */
  encodingOptions: {
    /**
     * @generated from field: livekit.IngressVideoEncodingPreset preset = 3;
     */
    value: IngressVideoEncodingPreset;
    case: "preset";
  } | {
    /**
     * @generated from field: livekit.IngressVideoEncodingOptions options = 4;
     */
    value: IngressVideoEncodingOptions;
    case: "options";
  } | { case: undefined; value?: undefined };

  constructor(data?: PartialMessage<IngressVideoOptions>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.IngressVideoOptions";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): IngressVideoOptions;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): IngressVideoOptions;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): IngressVideoOptions;

  static equals(a: IngressVideoOptions | PlainMessage<IngressVideoOptions> | undefined, b: IngressVideoOptions | PlainMessage<IngressVideoOptions> | undefined): boolean;
}

/**
 * @generated from message livekit.IngressAudioEncodingOptions
 */
declare class IngressAudioEncodingOptions extends Message<IngressAudioEncodingOptions> {
  /**
   * desired audio codec to publish to room
   *
   * @generated from field: livekit.AudioCodec audio_codec = 1;
   */
  audioCodec: AudioCodec;

  /**
   * @generated from field: uint32 bitrate = 2;
   */
  bitrate: number;

  /**
   * @generated from field: bool disable_dtx = 3;
   */
  disableDtx: boolean;

  /**
   * @generated from field: uint32 channels = 4;
   */
  channels: number;

  constructor(data?: PartialMessage<IngressAudioEncodingOptions>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.IngressAudioEncodingOptions";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): IngressAudioEncodingOptions;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): IngressAudioEncodingOptions;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): IngressAudioEncodingOptions;

  static equals(a: IngressAudioEncodingOptions | PlainMessage<IngressAudioEncodingOptions> | undefined, b: IngressAudioEncodingOptions | PlainMessage<IngressAudioEncodingOptions> | undefined): boolean;
}

/**
 * @generated from message livekit.IngressVideoEncodingOptions
 */
declare class IngressVideoEncodingOptions extends Message<IngressVideoEncodingOptions> {
  /**
   * desired codec to publish to room
   *
   * @generated from field: livekit.VideoCodec video_codec = 1;
   */
  videoCodec: VideoCodec;

  /**
   * @generated from field: double frame_rate = 2;
   */
  frameRate: number;

  /**
   * simulcast layers to publish, when empty, should usually be set to layers at 1/2 and 1/4 of the dimensions
   *
   * @generated from field: repeated livekit.VideoLayer layers = 3;
   */
  layers: VideoLayer[];

  constructor(data?: PartialMessage<IngressVideoEncodingOptions>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.IngressVideoEncodingOptions";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): IngressVideoEncodingOptions;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): IngressVideoEncodingOptions;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): IngressVideoEncodingOptions;

  static equals(a: IngressVideoEncodingOptions | PlainMessage<IngressVideoEncodingOptions> | undefined, b: IngressVideoEncodingOptions | PlainMessage<IngressVideoEncodingOptions> | undefined): boolean;
}

/**
 * @generated from message livekit.IngressInfo
 */
declare class IngressInfo extends Message<IngressInfo> {
  /**
   * @generated from field: string ingress_id = 1;
   */
  ingressId: string;

  /**
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * @generated from field: string stream_key = 3;
   */
  streamKey: string;

  /**
   * URL to point the encoder to for push (RTMP, WHIP), or location to pull media from for pull (URL)
   *
   * @generated from field: string url = 4;
   */
  url: string;

  /**
   * for RTMP input, it'll be a rtmp:// URL
   * for FILE input, it'll be a http:// URL
   * for SRT input, it'll be a srt:// URL
   *
   * @generated from field: livekit.IngressInput input_type = 5;
   */
  inputType: IngressInput;

  /**
   * @generated from field: bool bypass_transcoding = 13 [deprecated = true];
   * @deprecated
   */
  bypassTranscoding: boolean;

  /**
   * @generated from field: optional bool enable_transcoding = 15;
   */
  enableTranscoding?: boolean;

  /**
   * @generated from field: livekit.IngressAudioOptions audio = 6;
   */
  audio?: IngressAudioOptions;

  /**
   * @generated from field: livekit.IngressVideoOptions video = 7;
   */
  video?: IngressVideoOptions;

  /**
   * @generated from field: string room_name = 8;
   */
  roomName: string;

  /**
   * @generated from field: string participant_identity = 9;
   */
  participantIdentity: string;

  /**
   * @generated from field: string participant_name = 10;
   */
  participantName: string;

  /**
   * @generated from field: string participant_metadata = 14;
   */
  participantMetadata: string;

  /**
   * @generated from field: bool reusable = 11;
   */
  reusable: boolean;

  /**
   * Description of error/stream non compliance and debug info for publisher otherwise (received bitrate, resolution, bandwidth)
   *
   * @generated from field: livekit.IngressState state = 12;
   */
  state?: IngressState;

  /**
   * The default value is true and when set to false, the new connection attempts will be rejected
   *
   * @generated from field: optional bool enabled = 16;
   */
  enabled?: boolean;

  constructor(data?: PartialMessage<IngressInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.IngressInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): IngressInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): IngressInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): IngressInfo;

  static equals(a: IngressInfo | PlainMessage<IngressInfo> | undefined, b: IngressInfo | PlainMessage<IngressInfo> | undefined): boolean;
}

/**
 * @generated from message livekit.IngressState
 */
declare class IngressState extends Message<IngressState> {
  /**
   * @generated from field: livekit.IngressState.Status status = 1;
   */
  status: IngressState_Status;

  /**
   * Error/non compliance description if any
   *
   * @generated from field: string error = 2;
   */
  error: string;

  /**
   * @generated from field: livekit.InputVideoState video = 3;
   */
  video?: InputVideoState;

  /**
   * @generated from field: livekit.InputAudioState audio = 4;
   */
  audio?: InputAudioState;

  /**
   * ID of the current/previous room published to
   *
   * @generated from field: string room_id = 5;
   */
  roomId: string;

  /**
   * @generated from field: int64 started_at = 7;
   */
  startedAt: bigint;

  /**
   * @generated from field: int64 ended_at = 8;
   */
  endedAt: bigint;

  /**
   * @generated from field: int64 updated_at = 10;
   */
  updatedAt: bigint;

  /**
   * @generated from field: string resource_id = 9;
   */
  resourceId: string;

  /**
   * @generated from field: repeated livekit.TrackInfo tracks = 6;
   */
  tracks: TrackInfo[];

  constructor(data?: PartialMessage<IngressState>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.IngressState";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): IngressState;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): IngressState;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): IngressState;

  static equals(a: IngressState | PlainMessage<IngressState> | undefined, b: IngressState | PlainMessage<IngressState> | undefined): boolean;
}

/**
 * @generated from enum livekit.IngressState.Status
 */
declare enum IngressState_Status {
  /**
   * @generated from enum value: ENDPOINT_INACTIVE = 0;
   */
  ENDPOINT_INACTIVE = 0,

  /**
   * @generated from enum value: ENDPOINT_BUFFERING = 1;
   */
  ENDPOINT_BUFFERING = 1,

  /**
   * @generated from enum value: ENDPOINT_PUBLISHING = 2;
   */
  ENDPOINT_PUBLISHING = 2,

  /**
   * @generated from enum value: ENDPOINT_ERROR = 3;
   */
  ENDPOINT_ERROR = 3,

  /**
   * @generated from enum value: ENDPOINT_COMPLETE = 4;
   */
  ENDPOINT_COMPLETE = 4,
}

/**
 * @generated from message livekit.InputVideoState
 */
declare class InputVideoState extends Message<InputVideoState> {
  /**
   * @generated from field: string mime_type = 1;
   */
  mimeType: string;

  /**
   * @generated from field: uint32 average_bitrate = 2;
   */
  averageBitrate: number;

  /**
   * @generated from field: uint32 width = 3;
   */
  width: number;

  /**
   * @generated from field: uint32 height = 4;
   */
  height: number;

  /**
   * @generated from field: double framerate = 5;
   */
  framerate: number;

  constructor(data?: PartialMessage<InputVideoState>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.InputVideoState";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): InputVideoState;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): InputVideoState;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): InputVideoState;

  static equals(a: InputVideoState | PlainMessage<InputVideoState> | undefined, b: InputVideoState | PlainMessage<InputVideoState> | undefined): boolean;
}

/**
 * @generated from message livekit.InputAudioState
 */
declare class InputAudioState extends Message<InputAudioState> {
  /**
   * @generated from field: string mime_type = 1;
   */
  mimeType: string;

  /**
   * @generated from field: uint32 average_bitrate = 2;
   */
  averageBitrate: number;

  /**
   * @generated from field: uint32 channels = 3;
   */
  channels: number;

  /**
   * @generated from field: uint32 sample_rate = 4;
   */
  sampleRate: number;

  constructor(data?: PartialMessage<InputAudioState>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.InputAudioState";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): InputAudioState;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): InputAudioState;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): InputAudioState;

  static equals(a: InputAudioState | PlainMessage<InputAudioState> | undefined, b: InputAudioState | PlainMessage<InputAudioState> | undefined): boolean;
}

/**
 * @generated from message livekit.UpdateIngressRequest
 */
declare class UpdateIngressRequest extends Message<UpdateIngressRequest> {
  /**
   * @generated from field: string ingress_id = 1;
   */
  ingressId: string;

  /**
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * @generated from field: string room_name = 3;
   */
  roomName: string;

  /**
   * @generated from field: string participant_identity = 4;
   */
  participantIdentity: string;

  /**
   * @generated from field: string participant_name = 5;
   */
  participantName: string;

  /**
   * @generated from field: string participant_metadata = 9;
   */
  participantMetadata: string;

  /**
   * @generated from field: optional bool bypass_transcoding = 8 [deprecated = true];
   * @deprecated
   */
  bypassTranscoding?: boolean;

  /**
   * @generated from field: optional bool enable_transcoding = 10;
   */
  enableTranscoding?: boolean;

  /**
   * @generated from field: livekit.IngressAudioOptions audio = 6;
   */
  audio?: IngressAudioOptions;

  /**
   * @generated from field: livekit.IngressVideoOptions video = 7;
   */
  video?: IngressVideoOptions;

  /**
   * The default value is true and when set to false, the new connection attempts will be rejected
   *
   * @generated from field: optional bool enabled = 11;
   */
  enabled?: boolean;

  constructor(data?: PartialMessage<UpdateIngressRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.UpdateIngressRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateIngressRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateIngressRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateIngressRequest;

  static equals(a: UpdateIngressRequest | PlainMessage<UpdateIngressRequest> | undefined, b: UpdateIngressRequest | PlainMessage<UpdateIngressRequest> | undefined): boolean;
}

/**
 * @generated from message livekit.ListIngressRequest
 */
declare class ListIngressRequest extends Message<ListIngressRequest> {
  /**
   * when blank, lists all ingress endpoints
   *
   * (optional, filter by room name)
   *
   * @generated from field: string room_name = 1;
   */
  roomName: string;

  /**
   * (optional, filter by ingress ID)
   *
   * @generated from field: string ingress_id = 2;
   */
  ingressId: string;

  constructor(data?: PartialMessage<ListIngressRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.ListIngressRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListIngressRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListIngressRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListIngressRequest;

  static equals(a: ListIngressRequest | PlainMessage<ListIngressRequest> | undefined, b: ListIngressRequest | PlainMessage<ListIngressRequest> | undefined): boolean;
}

/**
 * @generated from message livekit.ListIngressResponse
 */
declare class ListIngressResponse extends Message<ListIngressResponse> {
  /**
   * @generated from field: repeated livekit.IngressInfo items = 1;
   */
  items: IngressInfo[];

  constructor(data?: PartialMessage<ListIngressResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.ListIngressResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListIngressResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListIngressResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListIngressResponse;

  static equals(a: ListIngressResponse | PlainMessage<ListIngressResponse> | undefined, b: ListIngressResponse | PlainMessage<ListIngressResponse> | undefined): boolean;
}

/**
 * @generated from message livekit.DeleteIngressRequest
 */
declare class DeleteIngressRequest extends Message<DeleteIngressRequest> {
  /**
   * @generated from field: string ingress_id = 1;
   */
  ingressId: string;

  constructor(data?: PartialMessage<DeleteIngressRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.DeleteIngressRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeleteIngressRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeleteIngressRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeleteIngressRequest;

  static equals(a: DeleteIngressRequest | PlainMessage<DeleteIngressRequest> | undefined, b: DeleteIngressRequest | PlainMessage<DeleteIngressRequest> | undefined): boolean;
}

// Copyright 2023 LiveKit, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.



/**
 * @generated from message livekit.CreateRoomRequest
 */
declare class CreateRoomRequest extends Message<CreateRoomRequest> {
  /**
   * name of the room
   *
   * @generated from field: string name = 1;
   */
  name: string;

  /**
   * configuration to use for this room parameters. Setting parameters below override the config defaults.
   *
   * @generated from field: string room_preset = 12;
   */
  roomPreset: string;

  /**
   * number of seconds to keep the room open if no one joins
   *
   * @generated from field: uint32 empty_timeout = 2;
   */
  emptyTimeout: number;

  /**
   * number of seconds to keep the room open after everyone leaves
   *
   * @generated from field: uint32 departure_timeout = 10;
   */
  departureTimeout: number;

  /**
   * limit number of participants that can be in a room
   *
   * @generated from field: uint32 max_participants = 3;
   */
  maxParticipants: number;

  /**
   * override the node room is allocated to, for debugging
   *
   * @generated from field: string node_id = 4;
   */
  nodeId: string;

  /**
   * metadata of room
   *
   * @generated from field: string metadata = 5;
   */
  metadata: string;

  /**
   * auto-egress configurations
   *
   * @generated from field: livekit.RoomEgress egress = 6;
   */
  egress?: RoomEgress;

  /**
   * playout delay of subscriber
   *
   * @generated from field: uint32 min_playout_delay = 7;
   */
  minPlayoutDelay: number;

  /**
   * @generated from field: uint32 max_playout_delay = 8;
   */
  maxPlayoutDelay: number;

  /**
   * improves A/V sync when playout_delay set to a value larger than 200ms. It will disables transceiver re-use
   * so not recommended for rooms with frequent subscription changes
   *
   * @generated from field: bool sync_streams = 9;
   */
  syncStreams: boolean;

  /**
   * replay
   *
   * @generated from field: bool replay_enabled = 13;
   */
  replayEnabled: boolean;

  /**
   * Define agents that should be dispatched to this room
   *
   * NEXT-ID: 15
   *
   * @generated from field: repeated livekit.RoomAgentDispatch agents = 14;
   */
  agents: RoomAgentDispatch[];

  constructor(data?: PartialMessage<CreateRoomRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.CreateRoomRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreateRoomRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreateRoomRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreateRoomRequest;

  static equals(a: CreateRoomRequest | PlainMessage<CreateRoomRequest> | undefined, b: CreateRoomRequest | PlainMessage<CreateRoomRequest> | undefined): boolean;
}

/**
 * @generated from message livekit.RoomEgress
 */
declare class RoomEgress extends Message<RoomEgress> {
  /**
   * @generated from field: livekit.RoomCompositeEgressRequest room = 1;
   */
  room?: RoomCompositeEgressRequest;

  /**
   * @generated from field: livekit.AutoParticipantEgress participant = 3;
   */
  participant?: AutoParticipantEgress;

  /**
   * @generated from field: livekit.AutoTrackEgress tracks = 2;
   */
  tracks?: AutoTrackEgress;

  constructor(data?: PartialMessage<RoomEgress>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.RoomEgress";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RoomEgress;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RoomEgress;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RoomEgress;

  static equals(a: RoomEgress | PlainMessage<RoomEgress> | undefined, b: RoomEgress | PlainMessage<RoomEgress> | undefined): boolean;
}

/**
 * @generated from message livekit.RoomAgent
 */
declare class RoomAgent extends Message<RoomAgent> {
  /**
   * @generated from field: repeated livekit.RoomAgentDispatch dispatches = 1;
   */
  dispatches: RoomAgentDispatch[];

  constructor(data?: PartialMessage<RoomAgent>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.RoomAgent";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RoomAgent;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RoomAgent;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RoomAgent;

  static equals(a: RoomAgent | PlainMessage<RoomAgent> | undefined, b: RoomAgent | PlainMessage<RoomAgent> | undefined): boolean;
}

/**
 * @generated from message livekit.ListRoomsRequest
 */
declare class ListRoomsRequest extends Message<ListRoomsRequest> {
  /**
   * when set, will only return rooms with name match
   *
   * @generated from field: repeated string names = 1;
   */
  names: string[];

  constructor(data?: PartialMessage<ListRoomsRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.ListRoomsRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListRoomsRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListRoomsRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListRoomsRequest;

  static equals(a: ListRoomsRequest | PlainMessage<ListRoomsRequest> | undefined, b: ListRoomsRequest | PlainMessage<ListRoomsRequest> | undefined): boolean;
}

/**
 * @generated from message livekit.ListRoomsResponse
 */
declare class ListRoomsResponse extends Message<ListRoomsResponse> {
  /**
   * @generated from field: repeated livekit.Room rooms = 1;
   */
  rooms: Room[];

  constructor(data?: PartialMessage<ListRoomsResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.ListRoomsResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListRoomsResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListRoomsResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListRoomsResponse;

  static equals(a: ListRoomsResponse | PlainMessage<ListRoomsResponse> | undefined, b: ListRoomsResponse | PlainMessage<ListRoomsResponse> | undefined): boolean;
}

/**
 * @generated from message livekit.DeleteRoomRequest
 */
declare class DeleteRoomRequest extends Message<DeleteRoomRequest> {
  /**
   * name of the room
   *
   * @generated from field: string room = 1;
   */
  room: string;

  constructor(data?: PartialMessage<DeleteRoomRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.DeleteRoomRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeleteRoomRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeleteRoomRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeleteRoomRequest;

  static equals(a: DeleteRoomRequest | PlainMessage<DeleteRoomRequest> | undefined, b: DeleteRoomRequest | PlainMessage<DeleteRoomRequest> | undefined): boolean;
}

/**
 * @generated from message livekit.DeleteRoomResponse
 */
declare class DeleteRoomResponse extends Message<DeleteRoomResponse> {
  constructor(data?: PartialMessage<DeleteRoomResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.DeleteRoomResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeleteRoomResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeleteRoomResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeleteRoomResponse;

  static equals(a: DeleteRoomResponse | PlainMessage<DeleteRoomResponse> | undefined, b: DeleteRoomResponse | PlainMessage<DeleteRoomResponse> | undefined): boolean;
}

/**
 * @generated from message livekit.ListParticipantsRequest
 */
declare class ListParticipantsRequest extends Message<ListParticipantsRequest> {
  /**
   * name of the room
   *
   * @generated from field: string room = 1;
   */
  room: string;

  constructor(data?: PartialMessage<ListParticipantsRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.ListParticipantsRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListParticipantsRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListParticipantsRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListParticipantsRequest;

  static equals(a: ListParticipantsRequest | PlainMessage<ListParticipantsRequest> | undefined, b: ListParticipantsRequest | PlainMessage<ListParticipantsRequest> | undefined): boolean;
}

/**
 * @generated from message livekit.ListParticipantsResponse
 */
declare class ListParticipantsResponse extends Message<ListParticipantsResponse> {
  /**
   * @generated from field: repeated livekit.ParticipantInfo participants = 1;
   */
  participants: ParticipantInfo[];

  constructor(data?: PartialMessage<ListParticipantsResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.ListParticipantsResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListParticipantsResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListParticipantsResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListParticipantsResponse;

  static equals(a: ListParticipantsResponse | PlainMessage<ListParticipantsResponse> | undefined, b: ListParticipantsResponse | PlainMessage<ListParticipantsResponse> | undefined): boolean;
}

/**
 * @generated from message livekit.RoomParticipantIdentity
 */
declare class RoomParticipantIdentity extends Message<RoomParticipantIdentity> {
  /**
   * name of the room
   *
   * @generated from field: string room = 1;
   */
  room: string;

  /**
   * identity of the participant
   *
   * @generated from field: string identity = 2;
   */
  identity: string;

  constructor(data?: PartialMessage<RoomParticipantIdentity>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.RoomParticipantIdentity";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RoomParticipantIdentity;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RoomParticipantIdentity;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RoomParticipantIdentity;

  static equals(a: RoomParticipantIdentity | PlainMessage<RoomParticipantIdentity> | undefined, b: RoomParticipantIdentity | PlainMessage<RoomParticipantIdentity> | undefined): boolean;
}

/**
 * @generated from message livekit.RemoveParticipantResponse
 */
declare class RemoveParticipantResponse extends Message<RemoveParticipantResponse> {
  constructor(data?: PartialMessage<RemoveParticipantResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.RemoveParticipantResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RemoveParticipantResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RemoveParticipantResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RemoveParticipantResponse;

  static equals(a: RemoveParticipantResponse | PlainMessage<RemoveParticipantResponse> | undefined, b: RemoveParticipantResponse | PlainMessage<RemoveParticipantResponse> | undefined): boolean;
}

/**
 * @generated from message livekit.MuteRoomTrackRequest
 */
declare class MuteRoomTrackRequest extends Message<MuteRoomTrackRequest> {
  /**
   * name of the room
   *
   * @generated from field: string room = 1;
   */
  room: string;

  /**
   * @generated from field: string identity = 2;
   */
  identity: string;

  /**
   * sid of the track to mute
   *
   * @generated from field: string track_sid = 3;
   */
  trackSid: string;

  /**
   * set to true to mute, false to unmute
   *
   * @generated from field: bool muted = 4;
   */
  muted: boolean;

  constructor(data?: PartialMessage<MuteRoomTrackRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.MuteRoomTrackRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): MuteRoomTrackRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): MuteRoomTrackRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): MuteRoomTrackRequest;

  static equals(a: MuteRoomTrackRequest | PlainMessage<MuteRoomTrackRequest> | undefined, b: MuteRoomTrackRequest | PlainMessage<MuteRoomTrackRequest> | undefined): boolean;
}

/**
 * @generated from message livekit.MuteRoomTrackResponse
 */
declare class MuteRoomTrackResponse extends Message<MuteRoomTrackResponse> {
  /**
   * @generated from field: livekit.TrackInfo track = 1;
   */
  track?: TrackInfo;

  constructor(data?: PartialMessage<MuteRoomTrackResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.MuteRoomTrackResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): MuteRoomTrackResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): MuteRoomTrackResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): MuteRoomTrackResponse;

  static equals(a: MuteRoomTrackResponse | PlainMessage<MuteRoomTrackResponse> | undefined, b: MuteRoomTrackResponse | PlainMessage<MuteRoomTrackResponse> | undefined): boolean;
}

/**
 * @generated from message livekit.UpdateParticipantRequest
 */
declare class UpdateParticipantRequest extends Message<UpdateParticipantRequest> {
  /**
   * @generated from field: string room = 1;
   */
  room: string;

  /**
   * @generated from field: string identity = 2;
   */
  identity: string;

  /**
   * metadata to update. skipping updates if left empty
   *
   * @generated from field: string metadata = 3;
   */
  metadata: string;

  /**
   * set to update the participant's permissions
   *
   * @generated from field: livekit.ParticipantPermission permission = 4;
   */
  permission?: ParticipantPermission;

  /**
   * display name to update
   *
   * @generated from field: string name = 5;
   */
  name: string;

  /**
   * attributes to update. it only updates attributes that have been set
   * to delete attributes, set the value to an empty string
   *
   * @generated from field: map<string, string> attributes = 6;
   */
  attributes: { [key: string]: string };

  constructor(data?: PartialMessage<UpdateParticipantRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.UpdateParticipantRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateParticipantRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateParticipantRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateParticipantRequest;

  static equals(a: UpdateParticipantRequest | PlainMessage<UpdateParticipantRequest> | undefined, b: UpdateParticipantRequest | PlainMessage<UpdateParticipantRequest> | undefined): boolean;
}

/**
 * @generated from message livekit.UpdateSubscriptionsRequest
 */
declare class UpdateSubscriptionsRequest extends Message<UpdateSubscriptionsRequest> {
  /**
   * @generated from field: string room = 1;
   */
  room: string;

  /**
   * @generated from field: string identity = 2;
   */
  identity: string;

  /**
   * list of sids of tracks
   *
   * @generated from field: repeated string track_sids = 3;
   */
  trackSids: string[];

  /**
   * set to true to subscribe, false to unsubscribe from tracks
   *
   * @generated from field: bool subscribe = 4;
   */
  subscribe: boolean;

  /**
   * list of participants and their tracks
   *
   * @generated from field: repeated livekit.ParticipantTracks participant_tracks = 5;
   */
  participantTracks: ParticipantTracks[];

  constructor(data?: PartialMessage<UpdateSubscriptionsRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.UpdateSubscriptionsRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateSubscriptionsRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateSubscriptionsRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateSubscriptionsRequest;

  static equals(a: UpdateSubscriptionsRequest | PlainMessage<UpdateSubscriptionsRequest> | undefined, b: UpdateSubscriptionsRequest | PlainMessage<UpdateSubscriptionsRequest> | undefined): boolean;
}

/**
 * empty for now
 *
 * @generated from message livekit.UpdateSubscriptionsResponse
 */
declare class UpdateSubscriptionsResponse extends Message<UpdateSubscriptionsResponse> {
  constructor(data?: PartialMessage<UpdateSubscriptionsResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.UpdateSubscriptionsResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateSubscriptionsResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateSubscriptionsResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateSubscriptionsResponse;

  static equals(a: UpdateSubscriptionsResponse | PlainMessage<UpdateSubscriptionsResponse> | undefined, b: UpdateSubscriptionsResponse | PlainMessage<UpdateSubscriptionsResponse> | undefined): boolean;
}

/**
 * @generated from message livekit.SendDataRequest
 */
declare class SendDataRequest extends Message<SendDataRequest> {
  /**
   * @generated from field: string room = 1;
   */
  room: string;

  /**
   * @generated from field: bytes data = 2;
   */
  data: Uint8Array;

  /**
   * @generated from field: livekit.DataPacket.Kind kind = 3;
   */
  kind: DataPacket_Kind;

  /**
   * mark deprecated
   *
   * @generated from field: repeated string destination_sids = 4 [deprecated = true];
   * @deprecated
   */
  destinationSids: string[];

  /**
   * when set, only forward to these identities
   *
   * @generated from field: repeated string destination_identities = 6;
   */
  destinationIdentities: string[];

  /**
   * @generated from field: optional string topic = 5;
   */
  topic?: string;

  /**
   * added by SDK to enable de-duping of messages, for INTERNAL USE ONLY
   *
   * @generated from field: bytes nonce = 7;
   */
  nonce: Uint8Array;

  constructor(data?: PartialMessage<SendDataRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.SendDataRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SendDataRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SendDataRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SendDataRequest;

  static equals(a: SendDataRequest | PlainMessage<SendDataRequest> | undefined, b: SendDataRequest | PlainMessage<SendDataRequest> | undefined): boolean;
}

/**
 * @generated from message livekit.SendDataResponse
 */
declare class SendDataResponse extends Message<SendDataResponse> {
  constructor(data?: PartialMessage<SendDataResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.SendDataResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SendDataResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SendDataResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SendDataResponse;

  static equals(a: SendDataResponse | PlainMessage<SendDataResponse> | undefined, b: SendDataResponse | PlainMessage<SendDataResponse> | undefined): boolean;
}

/**
 * @generated from message livekit.UpdateRoomMetadataRequest
 */
declare class UpdateRoomMetadataRequest extends Message<UpdateRoomMetadataRequest> {
  /**
   * @generated from field: string room = 1;
   */
  room: string;

  /**
   * metadata to update. skipping updates if left empty
   *
   * @generated from field: string metadata = 2;
   */
  metadata: string;

  constructor(data?: PartialMessage<UpdateRoomMetadataRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.UpdateRoomMetadataRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateRoomMetadataRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateRoomMetadataRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateRoomMetadataRequest;

  static equals(a: UpdateRoomMetadataRequest | PlainMessage<UpdateRoomMetadataRequest> | undefined, b: UpdateRoomMetadataRequest | PlainMessage<UpdateRoomMetadataRequest> | undefined): boolean;
}

/**
 * @generated from message livekit.RoomConfiguration
 */
declare class RoomConfiguration extends Message<RoomConfiguration> {
  /**
   * Used as ID, must be unique
   *
   * @generated from field: string name = 1;
   */
  name: string;

  /**
   * number of seconds to keep the room open if no one joins
   *
   * @generated from field: uint32 empty_timeout = 2;
   */
  emptyTimeout: number;

  /**
   * number of seconds to keep the room open after everyone leaves
   *
   * @generated from field: uint32 departure_timeout = 3;
   */
  departureTimeout: number;

  /**
   * limit number of participants that can be in a room, excluding Egress and Ingress participants
   *
   * @generated from field: uint32 max_participants = 4;
   */
  maxParticipants: number;

  /**
   * egress
   *
   * @generated from field: livekit.RoomEgress egress = 5;
   */
  egress?: RoomEgress;

  /**
   * playout delay of subscriber
   *
   * @generated from field: uint32 min_playout_delay = 7;
   */
  minPlayoutDelay: number;

  /**
   * @generated from field: uint32 max_playout_delay = 8;
   */
  maxPlayoutDelay: number;

  /**
   * improves A/V sync when playout_delay set to a value larger than 200ms. It will disables transceiver re-use
   * so not recommended for rooms with frequent subscription changes
   *
   * @generated from field: bool sync_streams = 9;
   */
  syncStreams: boolean;

  /**
   * Define agents that should be dispatched to this room
   *
   * @generated from field: repeated livekit.RoomAgentDispatch agents = 10;
   */
  agents: RoomAgentDispatch[];

  constructor(data?: PartialMessage<RoomConfiguration>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.RoomConfiguration";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RoomConfiguration;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RoomConfiguration;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RoomConfiguration;

  static equals(a: RoomConfiguration | PlainMessage<RoomConfiguration> | undefined, b: RoomConfiguration | PlainMessage<RoomConfiguration> | undefined): boolean;
}

/**
 * @generated from message livekit.ForwardParticipantRequest
 */
declare class ForwardParticipantRequest extends Message<ForwardParticipantRequest> {
  /**
   * room to forward participant from
   *
   * @generated from field: string room = 1;
   */
  room: string;

  /**
   * identity of the participant to forward
   *
   * @generated from field: string identity = 2;
   */
  identity: string;

  /**
   * room to forward participant to
   *
   * @generated from field: string destination_room = 3;
   */
  destinationRoom: string;

  constructor(data?: PartialMessage<ForwardParticipantRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.ForwardParticipantRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ForwardParticipantRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ForwardParticipantRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ForwardParticipantRequest;

  static equals(a: ForwardParticipantRequest | PlainMessage<ForwardParticipantRequest> | undefined, b: ForwardParticipantRequest | PlainMessage<ForwardParticipantRequest> | undefined): boolean;
}

/**
 * @generated from message livekit.ForwardParticipantResponse
 */
declare class ForwardParticipantResponse extends Message<ForwardParticipantResponse> {
  constructor(data?: PartialMessage<ForwardParticipantResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.ForwardParticipantResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ForwardParticipantResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ForwardParticipantResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ForwardParticipantResponse;

  static equals(a: ForwardParticipantResponse | PlainMessage<ForwardParticipantResponse> | undefined, b: ForwardParticipantResponse | PlainMessage<ForwardParticipantResponse> | undefined): boolean;
}

/**
 * @generated from message livekit.MoveParticipantRequest
 */
declare class MoveParticipantRequest extends Message<MoveParticipantRequest> {
  /**
   * room to move participant from
   *
   * @generated from field: string room = 1;
   */
  room: string;

  /**
   * identity of the participant to move to
   *
   * @generated from field: string identity = 2;
   */
  identity: string;

  /**
   * room to move participant to
   *
   * @generated from field: string destination_room = 3;
   */
  destinationRoom: string;

  constructor(data?: PartialMessage<MoveParticipantRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.MoveParticipantRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): MoveParticipantRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): MoveParticipantRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): MoveParticipantRequest;

  static equals(a: MoveParticipantRequest | PlainMessage<MoveParticipantRequest> | undefined, b: MoveParticipantRequest | PlainMessage<MoveParticipantRequest> | undefined): boolean;
}

/**
 * @generated from message livekit.MoveParticipantResponse
 */
declare class MoveParticipantResponse extends Message<MoveParticipantResponse> {
  constructor(data?: PartialMessage<MoveParticipantResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.MoveParticipantResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): MoveParticipantResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): MoveParticipantResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): MoveParticipantResponse;

  static equals(a: MoveParticipantResponse | PlainMessage<MoveParticipantResponse> | undefined, b: MoveParticipantResponse | PlainMessage<MoveParticipantResponse> | undefined): boolean;
}

// Copyright 2023 LiveKit, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.



/**
 * @generated from enum livekit.SignalTarget
 */
declare enum SignalTarget {
  /**
   * @generated from enum value: PUBLISHER = 0;
   */
  PUBLISHER = 0,

  /**
   * @generated from enum value: SUBSCRIBER = 1;
   */
  SUBSCRIBER = 1,
}

/**
 * @generated from enum livekit.StreamState
 */
declare enum StreamState {
  /**
   * @generated from enum value: ACTIVE = 0;
   */
  ACTIVE = 0,

  /**
   * @generated from enum value: PAUSED = 1;
   */
  PAUSED = 1,
}

/**
 * @generated from enum livekit.CandidateProtocol
 */
declare enum CandidateProtocol {
  /**
   * @generated from enum value: UDP = 0;
   */
  UDP = 0,

  /**
   * @generated from enum value: TCP = 1;
   */
  TCP = 1,

  /**
   * @generated from enum value: TLS = 2;
   */
  TLS = 2,
}

/**
 * @generated from message livekit.SignalRequest
 */
declare class SignalRequest extends Message<SignalRequest> {
  /**
   * @generated from oneof livekit.SignalRequest.message
   */
  message: {
    /**
     * initial join exchange, for publisher
     *
     * @generated from field: livekit.SessionDescription offer = 1;
     */
    value: SessionDescription;
    case: "offer";
  } | {
    /**
     * participant answering publisher offer
     *
     * @generated from field: livekit.SessionDescription answer = 2;
     */
    value: SessionDescription;
    case: "answer";
  } | {
    /**
     * @generated from field: livekit.TrickleRequest trickle = 3;
     */
    value: TrickleRequest;
    case: "trickle";
  } | {
    /**
     * @generated from field: livekit.AddTrackRequest add_track = 4;
     */
    value: AddTrackRequest;
    case: "addTrack";
  } | {
    /**
     * mute the participant's published tracks
     *
     * @generated from field: livekit.MuteTrackRequest mute = 5;
     */
    value: MuteTrackRequest;
    case: "mute";
  } | {
    /**
     * Subscribe or unsubscribe from tracks
     *
     * @generated from field: livekit.UpdateSubscription subscription = 6;
     */
    value: UpdateSubscription;
    case: "subscription";
  } | {
    /**
     * Update settings of subscribed tracks
     *
     * @generated from field: livekit.UpdateTrackSettings track_setting = 7;
     */
    value: UpdateTrackSettings;
    case: "trackSetting";
  } | {
    /**
     * Immediately terminate session
     *
     * @generated from field: livekit.LeaveRequest leave = 8;
     */
    value: LeaveRequest;
    case: "leave";
  } | {
    /**
     * Update published video layers
     *
     * @generated from field: livekit.UpdateVideoLayers update_layers = 10 [deprecated = true];
     * @deprecated
     */
    value: UpdateVideoLayers;
    case: "updateLayers";
  } | {
    /**
     * Update subscriber permissions
     *
     * @generated from field: livekit.SubscriptionPermission subscription_permission = 11;
     */
    value: SubscriptionPermission;
    case: "subscriptionPermission";
  } | {
    /**
     * sync client's subscribe state to server during reconnect
     *
     * @generated from field: livekit.SyncState sync_state = 12;
     */
    value: SyncState;
    case: "syncState";
  } | {
    /**
     * Simulate conditions, for client validations
     *
     * @generated from field: livekit.SimulateScenario simulate = 13;
     */
    value: SimulateScenario;
    case: "simulate";
  } | {
    /**
     * client triggered ping to server
     *
     * deprecated by ping_req (message Ping)
     *
     * @generated from field: int64 ping = 14;
     */
    value: bigint;
    case: "ping";
  } | {
    /**
     * update a participant's own metadata, name, or attributes
     * requires canUpdateOwnParticipantMetadata permission
     *
     * @generated from field: livekit.UpdateParticipantMetadata update_metadata = 15;
     */
    value: UpdateParticipantMetadata;
    case: "updateMetadata";
  } | {
    /**
     * @generated from field: livekit.Ping ping_req = 16;
     */
    value: Ping;
    case: "pingReq";
  } | {
    /**
     * Update local audio track settings
     *
     * @generated from field: livekit.UpdateLocalAudioTrack update_audio_track = 17;
     */
    value: UpdateLocalAudioTrack;
    case: "updateAudioTrack";
  } | {
    /**
     * Update local video track settings
     *
     * @generated from field: livekit.UpdateLocalVideoTrack update_video_track = 18;
     */
    value: UpdateLocalVideoTrack;
    case: "updateVideoTrack";
  } | { case: undefined; value?: undefined };

  constructor(data?: PartialMessage<SignalRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.SignalRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SignalRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SignalRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SignalRequest;

  static equals(a: SignalRequest | PlainMessage<SignalRequest> | undefined, b: SignalRequest | PlainMessage<SignalRequest> | undefined): boolean;
}

/**
 * @generated from message livekit.SignalResponse
 */
declare class SignalResponse extends Message<SignalResponse> {
  /**
   * @generated from oneof livekit.SignalResponse.message
   */
  message: {
    /**
     * sent when join is accepted
     *
     * @generated from field: livekit.JoinResponse join = 1;
     */
    value: JoinResponse;
    case: "join";
  } | {
    /**
     * sent when server answers publisher
     *
     * @generated from field: livekit.SessionDescription answer = 2;
     */
    value: SessionDescription;
    case: "answer";
  } | {
    /**
     * sent when server is sending subscriber an offer
     *
     * @generated from field: livekit.SessionDescription offer = 3;
     */
    value: SessionDescription;
    case: "offer";
  } | {
    /**
     * sent when an ICE candidate is available
     *
     * @generated from field: livekit.TrickleRequest trickle = 4;
     */
    value: TrickleRequest;
    case: "trickle";
  } | {
    /**
     * sent when participants in the room has changed
     *
     * @generated from field: livekit.ParticipantUpdate update = 5;
     */
    value: ParticipantUpdate;
    case: "update";
  } | {
    /**
     * sent to the participant when their track has been published
     *
     * @generated from field: livekit.TrackPublishedResponse track_published = 6;
     */
    value: TrackPublishedResponse;
    case: "trackPublished";
  } | {
    /**
     * Immediately terminate session
     *
     * @generated from field: livekit.LeaveRequest leave = 8;
     */
    value: LeaveRequest;
    case: "leave";
  } | {
    /**
     * server initiated mute
     *
     * @generated from field: livekit.MuteTrackRequest mute = 9;
     */
    value: MuteTrackRequest;
    case: "mute";
  } | {
    /**
     * indicates changes to speaker status, including when they've gone to not speaking
     *
     * @generated from field: livekit.SpeakersChanged speakers_changed = 10;
     */
    value: SpeakersChanged;
    case: "speakersChanged";
  } | {
    /**
     * sent when metadata of the room has changed
     *
     * @generated from field: livekit.RoomUpdate room_update = 11;
     */
    value: RoomUpdate;
    case: "roomUpdate";
  } | {
    /**
     * when connection quality changed
     *
     * @generated from field: livekit.ConnectionQualityUpdate connection_quality = 12;
     */
    value: ConnectionQualityUpdate;
    case: "connectionQuality";
  } | {
    /**
     * when streamed tracks state changed, used to notify when any of the streams were paused due to
     * congestion
     *
     * @generated from field: livekit.StreamStateUpdate stream_state_update = 13;
     */
    value: StreamStateUpdate;
    case: "streamStateUpdate";
  } | {
    /**
     * when max subscribe quality changed, used by dynamic broadcasting to disable unused layers
     *
     * @generated from field: livekit.SubscribedQualityUpdate subscribed_quality_update = 14;
     */
    value: SubscribedQualityUpdate;
    case: "subscribedQualityUpdate";
  } | {
    /**
     * when subscription permission changed
     *
     * @generated from field: livekit.SubscriptionPermissionUpdate subscription_permission_update = 15;
     */
    value: SubscriptionPermissionUpdate;
    case: "subscriptionPermissionUpdate";
  } | {
    /**
     * update the token the client was using, to prevent an active client from using an expired token
     *
     * @generated from field: string refresh_token = 16;
     */
    value: string;
    case: "refreshToken";
  } | {
    /**
     * server initiated track unpublish
     *
     * @generated from field: livekit.TrackUnpublishedResponse track_unpublished = 17;
     */
    value: TrackUnpublishedResponse;
    case: "trackUnpublished";
  } | {
    /**
     * respond to ping
     *
     * deprecated by pong_resp (message Pong)
     *
     * @generated from field: int64 pong = 18;
     */
    value: bigint;
    case: "pong";
  } | {
    /**
     * sent when client reconnects
     *
     * @generated from field: livekit.ReconnectResponse reconnect = 19;
     */
    value: ReconnectResponse;
    case: "reconnect";
  } | {
    /**
     * respond to Ping
     *
     * @generated from field: livekit.Pong pong_resp = 20;
     */
    value: Pong;
    case: "pongResp";
  } | {
    /**
     * Subscription response, client should not expect any media from this subscription if it fails
     *
     * @generated from field: livekit.SubscriptionResponse subscription_response = 21;
     */
    value: SubscriptionResponse;
    case: "subscriptionResponse";
  } | {
    /**
     * Response relating to user inititated requests that carry a `request_id`
     *
     * @generated from field: livekit.RequestResponse request_response = 22;
     */
    value: RequestResponse;
    case: "requestResponse";
  } | {
    /**
     * notify to the publisher when a published track has been subscribed for the first time
     *
     * @generated from field: livekit.TrackSubscribed track_subscribed = 23;
     */
    value: TrackSubscribed;
    case: "trackSubscribed";
  } | {
    /**
     * notify to the participant when they have been moved to a new room
     *
     * @generated from field: livekit.RoomMovedResponse room_moved = 24;
     */
    value: RoomMovedResponse;
    case: "roomMoved";
  } | { case: undefined; value?: undefined };

  constructor(data?: PartialMessage<SignalResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.SignalResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SignalResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SignalResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SignalResponse;

  static equals(a: SignalResponse | PlainMessage<SignalResponse> | undefined, b: SignalResponse | PlainMessage<SignalResponse> | undefined): boolean;
}

/**
 * @generated from message livekit.SimulcastCodec
 */
declare class SimulcastCodec extends Message<SimulcastCodec> {
  /**
   * @generated from field: string codec = 1;
   */
  codec: string;

  /**
   * @generated from field: string cid = 2;
   */
  cid: string;

  constructor(data?: PartialMessage<SimulcastCodec>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.SimulcastCodec";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SimulcastCodec;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SimulcastCodec;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SimulcastCodec;

  static equals(a: SimulcastCodec | PlainMessage<SimulcastCodec> | undefined, b: SimulcastCodec | PlainMessage<SimulcastCodec> | undefined): boolean;
}

/**
 * @generated from message livekit.AddTrackRequest
 */
declare class AddTrackRequest extends Message<AddTrackRequest> {
  /**
   * client ID of track, to match it when RTC track is received
   *
   * @generated from field: string cid = 1;
   */
  cid: string;

  /**
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * @generated from field: livekit.TrackType type = 3;
   */
  type: TrackType;

  /**
   * to be deprecated in favor of layers
   *
   * @generated from field: uint32 width = 4;
   */
  width: number;

  /**
   * @generated from field: uint32 height = 5;
   */
  height: number;

  /**
   * true to add track and initialize to muted
   *
   * @generated from field: bool muted = 6;
   */
  muted: boolean;

  /**
   * true if DTX (Discontinuous Transmission) is disabled for audio
   *
   * deprecated in favor of audio_features
   *
   * @generated from field: bool disable_dtx = 7 [deprecated = true];
   * @deprecated
   */
  disableDtx: boolean;

  /**
   * @generated from field: livekit.TrackSource source = 8;
   */
  source: TrackSource;

  /**
   * @generated from field: repeated livekit.VideoLayer layers = 9;
   */
  layers: VideoLayer[];

  /**
   * @generated from field: repeated livekit.SimulcastCodec simulcast_codecs = 10;
   */
  simulcastCodecs: SimulcastCodec[];

  /**
   * server ID of track, publish new codec to exist track
   *
   * @generated from field: string sid = 11;
   */
  sid: string;

  /**
   * deprecated in favor of audio_features
   *
   * @generated from field: bool stereo = 12 [deprecated = true];
   * @deprecated
   */
  stereo: boolean;

  /**
   * true if RED (Redundant Encoding) is disabled for audio
   *
   * @generated from field: bool disable_red = 13;
   */
  disableRed: boolean;

  /**
   * @generated from field: livekit.Encryption.Type encryption = 14;
   */
  encryption: Encryption_Type;

  /**
   * which stream the track belongs to, used to group tracks together.
   * if not specified, server will infer it from track source to bundle camera/microphone, screenshare/audio together
   *
   * @generated from field: string stream = 15;
   */
  stream: string;

  /**
   * @generated from field: livekit.BackupCodecPolicy backup_codec_policy = 16;
   */
  backupCodecPolicy: BackupCodecPolicy;

  /**
   * @generated from field: repeated livekit.AudioTrackFeature audio_features = 17;
   */
  audioFeatures: AudioTrackFeature[];

  constructor(data?: PartialMessage<AddTrackRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.AddTrackRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AddTrackRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AddTrackRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AddTrackRequest;

  static equals(a: AddTrackRequest | PlainMessage<AddTrackRequest> | undefined, b: AddTrackRequest | PlainMessage<AddTrackRequest> | undefined): boolean;
}

/**
 * @generated from message livekit.TrickleRequest
 */
declare class TrickleRequest extends Message<TrickleRequest> {
  /**
   * @generated from field: string candidateInit = 1;
   */
  candidateInit: string;

  /**
   * @generated from field: livekit.SignalTarget target = 2;
   */
  target: SignalTarget;

  /**
   * @generated from field: bool final = 3;
   */
  final: boolean;

  constructor(data?: PartialMessage<TrickleRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.TrickleRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): TrickleRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): TrickleRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): TrickleRequest;

  static equals(a: TrickleRequest | PlainMessage<TrickleRequest> | undefined, b: TrickleRequest | PlainMessage<TrickleRequest> | undefined): boolean;
}

/**
 * @generated from message livekit.MuteTrackRequest
 */
declare class MuteTrackRequest extends Message<MuteTrackRequest> {
  /**
   * @generated from field: string sid = 1;
   */
  sid: string;

  /**
   * @generated from field: bool muted = 2;
   */
  muted: boolean;

  constructor(data?: PartialMessage<MuteTrackRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.MuteTrackRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): MuteTrackRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): MuteTrackRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): MuteTrackRequest;

  static equals(a: MuteTrackRequest | PlainMessage<MuteTrackRequest> | undefined, b: MuteTrackRequest | PlainMessage<MuteTrackRequest> | undefined): boolean;
}

/**
 * @generated from message livekit.JoinResponse
 */
declare class JoinResponse extends Message<JoinResponse> {
  /**
   * @generated from field: livekit.Room room = 1;
   */
  room?: Room;

  /**
   * @generated from field: livekit.ParticipantInfo participant = 2;
   */
  participant?: ParticipantInfo;

  /**
   * @generated from field: repeated livekit.ParticipantInfo other_participants = 3;
   */
  otherParticipants: ParticipantInfo[];

  /**
   * deprecated. use server_info.version instead.
   *
   * @generated from field: string server_version = 4;
   */
  serverVersion: string;

  /**
   * @generated from field: repeated livekit.ICEServer ice_servers = 5;
   */
  iceServers: ICEServer[];

  /**
   * use subscriber as the primary PeerConnection
   *
   * @generated from field: bool subscriber_primary = 6;
   */
  subscriberPrimary: boolean;

  /**
   * when the current server isn't available, return alternate url to retry connection
   * when this is set, the other fields will be largely empty
   *
   * @generated from field: string alternative_url = 7;
   */
  alternativeUrl: string;

  /**
   * @generated from field: livekit.ClientConfiguration client_configuration = 8;
   */
  clientConfiguration?: ClientConfiguration;

  /**
   * deprecated. use server_info.region instead.
   *
   * @generated from field: string server_region = 9;
   */
  serverRegion: string;

  /**
   * @generated from field: int32 ping_timeout = 10;
   */
  pingTimeout: number;

  /**
   * @generated from field: int32 ping_interval = 11;
   */
  pingInterval: number;

  /**
   * @generated from field: livekit.ServerInfo server_info = 12;
   */
  serverInfo?: ServerInfo;

  /**
   * Server-Injected-Frame byte trailer, used to identify unencrypted frames when e2ee is enabled
   *
   * @generated from field: bytes sif_trailer = 13;
   */
  sifTrailer: Uint8Array;

  /**
   * @generated from field: repeated livekit.Codec enabled_publish_codecs = 14;
   */
  enabledPublishCodecs: Codec[];

  /**
   * when set, client should attempt to establish publish peer connection when joining room to speed up publishing
   *
   * @generated from field: bool fast_publish = 15;
   */
  fastPublish: boolean;

  constructor(data?: PartialMessage<JoinResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.JoinResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): JoinResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): JoinResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): JoinResponse;

  static equals(a: JoinResponse | PlainMessage<JoinResponse> | undefined, b: JoinResponse | PlainMessage<JoinResponse> | undefined): boolean;
}

/**
 * @generated from message livekit.ReconnectResponse
 */
declare class ReconnectResponse extends Message<ReconnectResponse> {
  /**
   * @generated from field: repeated livekit.ICEServer ice_servers = 1;
   */
  iceServers: ICEServer[];

  /**
   * @generated from field: livekit.ClientConfiguration client_configuration = 2;
   */
  clientConfiguration?: ClientConfiguration;

  /**
   * @generated from field: livekit.ServerInfo server_info = 3;
   */
  serverInfo?: ServerInfo;

  /**
   * last sequence number of reliable message received before resuming
   *
   * @generated from field: uint32 last_message_seq = 4;
   */
  lastMessageSeq: number;

  constructor(data?: PartialMessage<ReconnectResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.ReconnectResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ReconnectResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ReconnectResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ReconnectResponse;

  static equals(a: ReconnectResponse | PlainMessage<ReconnectResponse> | undefined, b: ReconnectResponse | PlainMessage<ReconnectResponse> | undefined): boolean;
}

/**
 * @generated from message livekit.TrackPublishedResponse
 */
declare class TrackPublishedResponse extends Message<TrackPublishedResponse> {
  /**
   * @generated from field: string cid = 1;
   */
  cid: string;

  /**
   * @generated from field: livekit.TrackInfo track = 2;
   */
  track?: TrackInfo;

  constructor(data?: PartialMessage<TrackPublishedResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.TrackPublishedResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): TrackPublishedResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): TrackPublishedResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): TrackPublishedResponse;

  static equals(a: TrackPublishedResponse | PlainMessage<TrackPublishedResponse> | undefined, b: TrackPublishedResponse | PlainMessage<TrackPublishedResponse> | undefined): boolean;
}

/**
 * @generated from message livekit.TrackUnpublishedResponse
 */
declare class TrackUnpublishedResponse extends Message<TrackUnpublishedResponse> {
  /**
   * @generated from field: string track_sid = 1;
   */
  trackSid: string;

  constructor(data?: PartialMessage<TrackUnpublishedResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.TrackUnpublishedResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): TrackUnpublishedResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): TrackUnpublishedResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): TrackUnpublishedResponse;

  static equals(a: TrackUnpublishedResponse | PlainMessage<TrackUnpublishedResponse> | undefined, b: TrackUnpublishedResponse | PlainMessage<TrackUnpublishedResponse> | undefined): boolean;
}

/**
 * @generated from message livekit.SessionDescription
 */
declare class SessionDescription extends Message<SessionDescription> {
  /**
   * "answer" | "offer" | "pranswer" | "rollback"
   *
   * @generated from field: string type = 1;
   */
  type: string;

  /**
   * @generated from field: string sdp = 2;
   */
  sdp: string;

  /**
   * @generated from field: uint32 id = 3;
   */
  id: number;

  constructor(data?: PartialMessage<SessionDescription>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.SessionDescription";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SessionDescription;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SessionDescription;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SessionDescription;

  static equals(a: SessionDescription | PlainMessage<SessionDescription> | undefined, b: SessionDescription | PlainMessage<SessionDescription> | undefined): boolean;
}

/**
 * @generated from message livekit.ParticipantUpdate
 */
declare class ParticipantUpdate extends Message<ParticipantUpdate> {
  /**
   * @generated from field: repeated livekit.ParticipantInfo participants = 1;
   */
  participants: ParticipantInfo[];

  constructor(data?: PartialMessage<ParticipantUpdate>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.ParticipantUpdate";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ParticipantUpdate;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ParticipantUpdate;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ParticipantUpdate;

  static equals(a: ParticipantUpdate | PlainMessage<ParticipantUpdate> | undefined, b: ParticipantUpdate | PlainMessage<ParticipantUpdate> | undefined): boolean;
}

/**
 * @generated from message livekit.UpdateSubscription
 */
declare class UpdateSubscription extends Message<UpdateSubscription> {
  /**
   * @generated from field: repeated string track_sids = 1;
   */
  trackSids: string[];

  /**
   * @generated from field: bool subscribe = 2;
   */
  subscribe: boolean;

  /**
   * @generated from field: repeated livekit.ParticipantTracks participant_tracks = 3;
   */
  participantTracks: ParticipantTracks[];

  constructor(data?: PartialMessage<UpdateSubscription>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.UpdateSubscription";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateSubscription;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateSubscription;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateSubscription;

  static equals(a: UpdateSubscription | PlainMessage<UpdateSubscription> | undefined, b: UpdateSubscription | PlainMessage<UpdateSubscription> | undefined): boolean;
}

/**
 * @generated from message livekit.UpdateTrackSettings
 */
declare class UpdateTrackSettings extends Message<UpdateTrackSettings> {
  /**
   * @generated from field: repeated string track_sids = 1;
   */
  trackSids: string[];

  /**
   * when true, the track is placed in a paused state, with no new data returned
   *
   * @generated from field: bool disabled = 3;
   */
  disabled: boolean;

  /**
   * deprecated in favor of width & height
   *
   * @generated from field: livekit.VideoQuality quality = 4;
   */
  quality: VideoQuality;

  /**
   * for video, width to receive
   *
   * @generated from field: uint32 width = 5;
   */
  width: number;

  /**
   * for video, height to receive
   *
   * @generated from field: uint32 height = 6;
   */
  height: number;

  /**
   * @generated from field: uint32 fps = 7;
   */
  fps: number;

  /**
   * subscription priority. 1 being the highest (0 is unset)
   * when unset, server sill assign priority based on the order of subscription
   * server will use priority in the following ways:
   * 1. when subscribed tracks exceed per-participant subscription limit, server will
   *    pause the lowest priority tracks
   * 2. when the network is congested, server will assign available bandwidth to
   *    higher priority tracks first. lowest priority tracks can be paused
   *
   * @generated from field: uint32 priority = 8;
   */
  priority: number;

  constructor(data?: PartialMessage<UpdateTrackSettings>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.UpdateTrackSettings";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateTrackSettings;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateTrackSettings;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateTrackSettings;

  static equals(a: UpdateTrackSettings | PlainMessage<UpdateTrackSettings> | undefined, b: UpdateTrackSettings | PlainMessage<UpdateTrackSettings> | undefined): boolean;
}

/**
 * @generated from message livekit.UpdateLocalAudioTrack
 */
declare class UpdateLocalAudioTrack extends Message<UpdateLocalAudioTrack> {
  /**
   * @generated from field: string track_sid = 1;
   */
  trackSid: string;

  /**
   * @generated from field: repeated livekit.AudioTrackFeature features = 2;
   */
  features: AudioTrackFeature[];

  constructor(data?: PartialMessage<UpdateLocalAudioTrack>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.UpdateLocalAudioTrack";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateLocalAudioTrack;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateLocalAudioTrack;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateLocalAudioTrack;

  static equals(a: UpdateLocalAudioTrack | PlainMessage<UpdateLocalAudioTrack> | undefined, b: UpdateLocalAudioTrack | PlainMessage<UpdateLocalAudioTrack> | undefined): boolean;
}

/**
 * @generated from message livekit.UpdateLocalVideoTrack
 */
declare class UpdateLocalVideoTrack extends Message<UpdateLocalVideoTrack> {
  /**
   * @generated from field: string track_sid = 1;
   */
  trackSid: string;

  /**
   * @generated from field: uint32 width = 2;
   */
  width: number;

  /**
   * @generated from field: uint32 height = 3;
   */
  height: number;

  constructor(data?: PartialMessage<UpdateLocalVideoTrack>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.UpdateLocalVideoTrack";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateLocalVideoTrack;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateLocalVideoTrack;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateLocalVideoTrack;

  static equals(a: UpdateLocalVideoTrack | PlainMessage<UpdateLocalVideoTrack> | undefined, b: UpdateLocalVideoTrack | PlainMessage<UpdateLocalVideoTrack> | undefined): boolean;
}

/**
 * @generated from message livekit.LeaveRequest
 */
declare class LeaveRequest extends Message<LeaveRequest> {
  /**
   * sent when server initiates the disconnect due to server-restart
   * indicates clients should attempt full-reconnect sequence
   * NOTE: `can_reconnect` obsoleted by `action` starting in protocol version 13
   *
   * @generated from field: bool can_reconnect = 1;
   */
  canReconnect: boolean;

  /**
   * @generated from field: livekit.DisconnectReason reason = 2;
   */
  reason: DisconnectReason;

  /**
   * @generated from field: livekit.LeaveRequest.Action action = 3;
   */
  action: LeaveRequest_Action;

  /**
   * @generated from field: livekit.RegionSettings regions = 4;
   */
  regions?: RegionSettings;

  constructor(data?: PartialMessage<LeaveRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.LeaveRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): LeaveRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): LeaveRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): LeaveRequest;

  static equals(a: LeaveRequest | PlainMessage<LeaveRequest> | undefined, b: LeaveRequest | PlainMessage<LeaveRequest> | undefined): boolean;
}

/**
 * indicates action clients should take on receiving this message
 *
 * @generated from enum livekit.LeaveRequest.Action
 */
declare enum LeaveRequest_Action {
  /**
   * should disconnect
   *
   * @generated from enum value: DISCONNECT = 0;
   */
  DISCONNECT = 0,

  /**
   * should attempt a resume with `reconnect=1` in join URL
   *
   * @generated from enum value: RESUME = 1;
   */
  RESUME = 1,

  /**
   * should attempt a reconnect, i. e. no `reconnect=1`
   *
   * @generated from enum value: RECONNECT = 2;
   */
  RECONNECT = 2,
}

/**
 * message to indicate published video track dimensions are changing
 *
 * @generated from message livekit.UpdateVideoLayers
 * @deprecated
 */
declare class UpdateVideoLayers extends Message<UpdateVideoLayers> {
  /**
   * @generated from field: string track_sid = 1;
   */
  trackSid: string;

  /**
   * @generated from field: repeated livekit.VideoLayer layers = 2;
   */
  layers: VideoLayer[];

  constructor(data?: PartialMessage<UpdateVideoLayers>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.UpdateVideoLayers";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateVideoLayers;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateVideoLayers;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateVideoLayers;

  static equals(a: UpdateVideoLayers | PlainMessage<UpdateVideoLayers> | undefined, b: UpdateVideoLayers | PlainMessage<UpdateVideoLayers> | undefined): boolean;
}

/**
 * @generated from message livekit.UpdateParticipantMetadata
 */
declare class UpdateParticipantMetadata extends Message<UpdateParticipantMetadata> {
  /**
   * @generated from field: string metadata = 1;
   */
  metadata: string;

  /**
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * attributes to update. it only updates attributes that have been set
   * to delete attributes, set the value to an empty string
   *
   * @generated from field: map<string, string> attributes = 3;
   */
  attributes: { [key: string]: string };

  /**
   * @generated from field: uint32 request_id = 4;
   */
  requestId: number;

  constructor(data?: PartialMessage<UpdateParticipantMetadata>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.UpdateParticipantMetadata";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateParticipantMetadata;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateParticipantMetadata;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateParticipantMetadata;

  static equals(a: UpdateParticipantMetadata | PlainMessage<UpdateParticipantMetadata> | undefined, b: UpdateParticipantMetadata | PlainMessage<UpdateParticipantMetadata> | undefined): boolean;
}

/**
 * @generated from message livekit.ICEServer
 */
declare class ICEServer extends Message<ICEServer> {
  /**
   * @generated from field: repeated string urls = 1;
   */
  urls: string[];

  /**
   * @generated from field: string username = 2;
   */
  username: string;

  /**
   * @generated from field: string credential = 3;
   */
  credential: string;

  constructor(data?: PartialMessage<ICEServer>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.ICEServer";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ICEServer;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ICEServer;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ICEServer;

  static equals(a: ICEServer | PlainMessage<ICEServer> | undefined, b: ICEServer | PlainMessage<ICEServer> | undefined): boolean;
}

/**
 * @generated from message livekit.SpeakersChanged
 */
declare class SpeakersChanged extends Message<SpeakersChanged> {
  /**
   * @generated from field: repeated livekit.SpeakerInfo speakers = 1;
   */
  speakers: SpeakerInfo[];

  constructor(data?: PartialMessage<SpeakersChanged>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.SpeakersChanged";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SpeakersChanged;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SpeakersChanged;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SpeakersChanged;

  static equals(a: SpeakersChanged | PlainMessage<SpeakersChanged> | undefined, b: SpeakersChanged | PlainMessage<SpeakersChanged> | undefined): boolean;
}

/**
 * @generated from message livekit.RoomUpdate
 */
declare class RoomUpdate extends Message<RoomUpdate> {
  /**
   * @generated from field: livekit.Room room = 1;
   */
  room?: Room;

  constructor(data?: PartialMessage<RoomUpdate>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.RoomUpdate";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RoomUpdate;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RoomUpdate;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RoomUpdate;

  static equals(a: RoomUpdate | PlainMessage<RoomUpdate> | undefined, b: RoomUpdate | PlainMessage<RoomUpdate> | undefined): boolean;
}

/**
 * @generated from message livekit.ConnectionQualityInfo
 */
declare class ConnectionQualityInfo extends Message<ConnectionQualityInfo> {
  /**
   * @generated from field: string participant_sid = 1;
   */
  participantSid: string;

  /**
   * @generated from field: livekit.ConnectionQuality quality = 2;
   */
  quality: ConnectionQuality;

  /**
   * @generated from field: float score = 3;
   */
  score: number;

  constructor(data?: PartialMessage<ConnectionQualityInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.ConnectionQualityInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ConnectionQualityInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ConnectionQualityInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ConnectionQualityInfo;

  static equals(a: ConnectionQualityInfo | PlainMessage<ConnectionQualityInfo> | undefined, b: ConnectionQualityInfo | PlainMessage<ConnectionQualityInfo> | undefined): boolean;
}

/**
 * @generated from message livekit.ConnectionQualityUpdate
 */
declare class ConnectionQualityUpdate extends Message<ConnectionQualityUpdate> {
  /**
   * @generated from field: repeated livekit.ConnectionQualityInfo updates = 1;
   */
  updates: ConnectionQualityInfo[];

  constructor(data?: PartialMessage<ConnectionQualityUpdate>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.ConnectionQualityUpdate";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ConnectionQualityUpdate;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ConnectionQualityUpdate;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ConnectionQualityUpdate;

  static equals(a: ConnectionQualityUpdate | PlainMessage<ConnectionQualityUpdate> | undefined, b: ConnectionQualityUpdate | PlainMessage<ConnectionQualityUpdate> | undefined): boolean;
}

/**
 * @generated from message livekit.StreamStateInfo
 */
declare class StreamStateInfo extends Message<StreamStateInfo> {
  /**
   * @generated from field: string participant_sid = 1;
   */
  participantSid: string;

  /**
   * @generated from field: string track_sid = 2;
   */
  trackSid: string;

  /**
   * @generated from field: livekit.StreamState state = 3;
   */
  state: StreamState;

  constructor(data?: PartialMessage<StreamStateInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.StreamStateInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): StreamStateInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): StreamStateInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): StreamStateInfo;

  static equals(a: StreamStateInfo | PlainMessage<StreamStateInfo> | undefined, b: StreamStateInfo | PlainMessage<StreamStateInfo> | undefined): boolean;
}

/**
 * @generated from message livekit.StreamStateUpdate
 */
declare class StreamStateUpdate extends Message<StreamStateUpdate> {
  /**
   * @generated from field: repeated livekit.StreamStateInfo stream_states = 1;
   */
  streamStates: StreamStateInfo[];

  constructor(data?: PartialMessage<StreamStateUpdate>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.StreamStateUpdate";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): StreamStateUpdate;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): StreamStateUpdate;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): StreamStateUpdate;

  static equals(a: StreamStateUpdate | PlainMessage<StreamStateUpdate> | undefined, b: StreamStateUpdate | PlainMessage<StreamStateUpdate> | undefined): boolean;
}

/**
 * @generated from message livekit.SubscribedQuality
 */
declare class SubscribedQuality extends Message<SubscribedQuality> {
  /**
   * @generated from field: livekit.VideoQuality quality = 1;
   */
  quality: VideoQuality;

  /**
   * @generated from field: bool enabled = 2;
   */
  enabled: boolean;

  constructor(data?: PartialMessage<SubscribedQuality>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.SubscribedQuality";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SubscribedQuality;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SubscribedQuality;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SubscribedQuality;

  static equals(a: SubscribedQuality | PlainMessage<SubscribedQuality> | undefined, b: SubscribedQuality | PlainMessage<SubscribedQuality> | undefined): boolean;
}

/**
 * @generated from message livekit.SubscribedCodec
 */
declare class SubscribedCodec extends Message<SubscribedCodec> {
  /**
   * @generated from field: string codec = 1;
   */
  codec: string;

  /**
   * @generated from field: repeated livekit.SubscribedQuality qualities = 2;
   */
  qualities: SubscribedQuality[];

  constructor(data?: PartialMessage<SubscribedCodec>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.SubscribedCodec";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SubscribedCodec;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SubscribedCodec;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SubscribedCodec;

  static equals(a: SubscribedCodec | PlainMessage<SubscribedCodec> | undefined, b: SubscribedCodec | PlainMessage<SubscribedCodec> | undefined): boolean;
}

/**
 * @generated from message livekit.SubscribedQualityUpdate
 */
declare class SubscribedQualityUpdate extends Message<SubscribedQualityUpdate> {
  /**
   * @generated from field: string track_sid = 1;
   */
  trackSid: string;

  /**
   * @generated from field: repeated livekit.SubscribedQuality subscribed_qualities = 2 [deprecated = true];
   * @deprecated
   */
  subscribedQualities: SubscribedQuality[];

  /**
   * @generated from field: repeated livekit.SubscribedCodec subscribed_codecs = 3;
   */
  subscribedCodecs: SubscribedCodec[];

  constructor(data?: PartialMessage<SubscribedQualityUpdate>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.SubscribedQualityUpdate";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SubscribedQualityUpdate;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SubscribedQualityUpdate;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SubscribedQualityUpdate;

  static equals(a: SubscribedQualityUpdate | PlainMessage<SubscribedQualityUpdate> | undefined, b: SubscribedQualityUpdate | PlainMessage<SubscribedQualityUpdate> | undefined): boolean;
}

/**
 * @generated from message livekit.TrackPermission
 */
declare class TrackPermission extends Message<TrackPermission> {
  /**
   * permission could be granted either by participant sid or identity
   *
   * @generated from field: string participant_sid = 1;
   */
  participantSid: string;

  /**
   * @generated from field: bool all_tracks = 2;
   */
  allTracks: boolean;

  /**
   * @generated from field: repeated string track_sids = 3;
   */
  trackSids: string[];

  /**
   * @generated from field: string participant_identity = 4;
   */
  participantIdentity: string;

  constructor(data?: PartialMessage<TrackPermission>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.TrackPermission";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): TrackPermission;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): TrackPermission;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): TrackPermission;

  static equals(a: TrackPermission | PlainMessage<TrackPermission> | undefined, b: TrackPermission | PlainMessage<TrackPermission> | undefined): boolean;
}

/**
 * @generated from message livekit.SubscriptionPermission
 */
declare class SubscriptionPermission extends Message<SubscriptionPermission> {
  /**
   * @generated from field: bool all_participants = 1;
   */
  allParticipants: boolean;

  /**
   * @generated from field: repeated livekit.TrackPermission track_permissions = 2;
   */
  trackPermissions: TrackPermission[];

  constructor(data?: PartialMessage<SubscriptionPermission>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.SubscriptionPermission";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SubscriptionPermission;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SubscriptionPermission;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SubscriptionPermission;

  static equals(a: SubscriptionPermission | PlainMessage<SubscriptionPermission> | undefined, b: SubscriptionPermission | PlainMessage<SubscriptionPermission> | undefined): boolean;
}

/**
 * @generated from message livekit.SubscriptionPermissionUpdate
 */
declare class SubscriptionPermissionUpdate extends Message<SubscriptionPermissionUpdate> {
  /**
   * @generated from field: string participant_sid = 1;
   */
  participantSid: string;

  /**
   * @generated from field: string track_sid = 2;
   */
  trackSid: string;

  /**
   * @generated from field: bool allowed = 3;
   */
  allowed: boolean;

  constructor(data?: PartialMessage<SubscriptionPermissionUpdate>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.SubscriptionPermissionUpdate";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SubscriptionPermissionUpdate;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SubscriptionPermissionUpdate;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SubscriptionPermissionUpdate;

  static equals(a: SubscriptionPermissionUpdate | PlainMessage<SubscriptionPermissionUpdate> | undefined, b: SubscriptionPermissionUpdate | PlainMessage<SubscriptionPermissionUpdate> | undefined): boolean;
}

/**
 * @generated from message livekit.RoomMovedResponse
 */
declare class RoomMovedResponse extends Message<RoomMovedResponse> {
  /**
   * information about the new room
   *
   * @generated from field: livekit.Room room = 1;
   */
  room?: Room;

  /**
   * new reconnect token that can be used to reconnect to the new room
   *
   * @generated from field: string token = 2;
   */
  token: string;

  /**
   * @generated from field: livekit.ParticipantInfo participant = 3;
   */
  participant?: ParticipantInfo;

  /**
   * @generated from field: repeated livekit.ParticipantInfo other_participants = 4;
   */
  otherParticipants: ParticipantInfo[];

  constructor(data?: PartialMessage<RoomMovedResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.RoomMovedResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RoomMovedResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RoomMovedResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RoomMovedResponse;

  static equals(a: RoomMovedResponse | PlainMessage<RoomMovedResponse> | undefined, b: RoomMovedResponse | PlainMessage<RoomMovedResponse> | undefined): boolean;
}

/**
 * @generated from message livekit.SyncState
 */
declare class SyncState extends Message<SyncState> {
  /**
   * last subscribe answer before reconnecting
   *
   * @generated from field: livekit.SessionDescription answer = 1;
   */
  answer?: SessionDescription;

  /**
   * @generated from field: livekit.UpdateSubscription subscription = 2;
   */
  subscription?: UpdateSubscription;

  /**
   * @generated from field: repeated livekit.TrackPublishedResponse publish_tracks = 3;
   */
  publishTracks: TrackPublishedResponse[];

  /**
   * @generated from field: repeated livekit.DataChannelInfo data_channels = 4;
   */
  dataChannels: DataChannelInfo[];

  /**
   * last received server side offer before reconnecting
   *
   * @generated from field: livekit.SessionDescription offer = 5;
   */
  offer?: SessionDescription;

  /**
   * @generated from field: repeated string track_sids_disabled = 6;
   */
  trackSidsDisabled: string[];

  /**
   * @generated from field: repeated livekit.DataChannelReceiveState datachannel_receive_states = 7;
   */
  datachannelReceiveStates: DataChannelReceiveState[];

  constructor(data?: PartialMessage<SyncState>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.SyncState";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SyncState;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SyncState;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SyncState;

  static equals(a: SyncState | PlainMessage<SyncState> | undefined, b: SyncState | PlainMessage<SyncState> | undefined): boolean;
}

/**
 * @generated from message livekit.DataChannelReceiveState
 */
declare class DataChannelReceiveState extends Message<DataChannelReceiveState> {
  /**
   * @generated from field: string publisher_sid = 1;
   */
  publisherSid: string;

  /**
   * @generated from field: uint32 last_seq = 2;
   */
  lastSeq: number;

  constructor(data?: PartialMessage<DataChannelReceiveState>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.DataChannelReceiveState";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DataChannelReceiveState;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DataChannelReceiveState;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DataChannelReceiveState;

  static equals(a: DataChannelReceiveState | PlainMessage<DataChannelReceiveState> | undefined, b: DataChannelReceiveState | PlainMessage<DataChannelReceiveState> | undefined): boolean;
}

/**
 * @generated from message livekit.DataChannelInfo
 */
declare class DataChannelInfo extends Message<DataChannelInfo> {
  /**
   * @generated from field: string label = 1;
   */
  label: string;

  /**
   * @generated from field: uint32 id = 2;
   */
  id: number;

  /**
   * @generated from field: livekit.SignalTarget target = 3;
   */
  target: SignalTarget;

  constructor(data?: PartialMessage<DataChannelInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.DataChannelInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DataChannelInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DataChannelInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DataChannelInfo;

  static equals(a: DataChannelInfo | PlainMessage<DataChannelInfo> | undefined, b: DataChannelInfo | PlainMessage<DataChannelInfo> | undefined): boolean;
}

/**
 * @generated from message livekit.SimulateScenario
 */
declare class SimulateScenario extends Message<SimulateScenario> {
  /**
   * @generated from oneof livekit.SimulateScenario.scenario
   */
  scenario: {
    /**
     * simulate N seconds of speaker activity
     *
     * @generated from field: int32 speaker_update = 1;
     */
    value: number;
    case: "speakerUpdate";
  } | {
    /**
     * simulate local node failure
     *
     * @generated from field: bool node_failure = 2;
     */
    value: boolean;
    case: "nodeFailure";
  } | {
    /**
     * simulate migration
     *
     * @generated from field: bool migration = 3;
     */
    value: boolean;
    case: "migration";
  } | {
    /**
     * server to send leave
     *
     * @generated from field: bool server_leave = 4;
     */
    value: boolean;
    case: "serverLeave";
  } | {
    /**
     * switch candidate protocol to tcp
     *
     * @generated from field: livekit.CandidateProtocol switch_candidate_protocol = 5;
     */
    value: CandidateProtocol;
    case: "switchCandidateProtocol";
  } | {
    /**
     * maximum bandwidth for subscribers, in bps
     * when zero, clears artificial bandwidth limit
     *
     * @generated from field: int64 subscriber_bandwidth = 6;
     */
    value: bigint;
    case: "subscriberBandwidth";
  } | {
    /**
     * disconnect signal on resume
     *
     * @generated from field: bool disconnect_signal_on_resume = 7;
     */
    value: boolean;
    case: "disconnectSignalOnResume";
  } | {
    /**
     * disconnect signal on resume before sending any messages from server
     *
     * @generated from field: bool disconnect_signal_on_resume_no_messages = 8;
     */
    value: boolean;
    case: "disconnectSignalOnResumeNoMessages";
  } | {
    /**
     * full reconnect leave request
     *
     * @generated from field: bool leave_request_full_reconnect = 9;
     */
    value: boolean;
    case: "leaveRequestFullReconnect";
  } | { case: undefined; value?: undefined };

  constructor(data?: PartialMessage<SimulateScenario>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.SimulateScenario";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SimulateScenario;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SimulateScenario;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SimulateScenario;

  static equals(a: SimulateScenario | PlainMessage<SimulateScenario> | undefined, b: SimulateScenario | PlainMessage<SimulateScenario> | undefined): boolean;
}

/**
 * @generated from message livekit.Ping
 */
declare class Ping extends Message<Ping> {
  /**
   * @generated from field: int64 timestamp = 1;
   */
  timestamp: bigint;

  /**
   * rtt in milliseconds calculated by client
   *
   * @generated from field: int64 rtt = 2;
   */
  rtt: bigint;

  constructor(data?: PartialMessage<Ping>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.Ping";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Ping;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Ping;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Ping;

  static equals(a: Ping | PlainMessage<Ping> | undefined, b: Ping | PlainMessage<Ping> | undefined): boolean;
}

/**
 * @generated from message livekit.Pong
 */
declare class Pong extends Message<Pong> {
  /**
   * timestamp field of last received ping request
   *
   * @generated from field: int64 last_ping_timestamp = 1;
   */
  lastPingTimestamp: bigint;

  /**
   * @generated from field: int64 timestamp = 2;
   */
  timestamp: bigint;

  constructor(data?: PartialMessage<Pong>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.Pong";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Pong;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Pong;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Pong;

  static equals(a: Pong | PlainMessage<Pong> | undefined, b: Pong | PlainMessage<Pong> | undefined): boolean;
}

/**
 * @generated from message livekit.RegionSettings
 */
declare class RegionSettings extends Message<RegionSettings> {
  /**
   * @generated from field: repeated livekit.RegionInfo regions = 1;
   */
  regions: RegionInfo[];

  constructor(data?: PartialMessage<RegionSettings>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.RegionSettings";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RegionSettings;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RegionSettings;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RegionSettings;

  static equals(a: RegionSettings | PlainMessage<RegionSettings> | undefined, b: RegionSettings | PlainMessage<RegionSettings> | undefined): boolean;
}

/**
 * @generated from message livekit.RegionInfo
 */
declare class RegionInfo extends Message<RegionInfo> {
  /**
   * @generated from field: string region = 1;
   */
  region: string;

  /**
   * @generated from field: string url = 2;
   */
  url: string;

  /**
   * @generated from field: int64 distance = 3;
   */
  distance: bigint;

  constructor(data?: PartialMessage<RegionInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.RegionInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RegionInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RegionInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RegionInfo;

  static equals(a: RegionInfo | PlainMessage<RegionInfo> | undefined, b: RegionInfo | PlainMessage<RegionInfo> | undefined): boolean;
}

/**
 * @generated from message livekit.SubscriptionResponse
 */
declare class SubscriptionResponse extends Message<SubscriptionResponse> {
  /**
   * @generated from field: string track_sid = 1;
   */
  trackSid: string;

  /**
   * @generated from field: livekit.SubscriptionError err = 2;
   */
  err: SubscriptionError;

  constructor(data?: PartialMessage<SubscriptionResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.SubscriptionResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SubscriptionResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SubscriptionResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SubscriptionResponse;

  static equals(a: SubscriptionResponse | PlainMessage<SubscriptionResponse> | undefined, b: SubscriptionResponse | PlainMessage<SubscriptionResponse> | undefined): boolean;
}

/**
 * @generated from message livekit.RequestResponse
 */
declare class RequestResponse extends Message<RequestResponse> {
  /**
   * @generated from field: uint32 request_id = 1;
   */
  requestId: number;

  /**
   * @generated from field: livekit.RequestResponse.Reason reason = 2;
   */
  reason: RequestResponse_Reason;

  /**
   * @generated from field: string message = 3;
   */
  message: string;

  constructor(data?: PartialMessage<RequestResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.RequestResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RequestResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RequestResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RequestResponse;

  static equals(a: RequestResponse | PlainMessage<RequestResponse> | undefined, b: RequestResponse | PlainMessage<RequestResponse> | undefined): boolean;
}

/**
 * @generated from enum livekit.RequestResponse.Reason
 */
declare enum RequestResponse_Reason {
  /**
   * @generated from enum value: OK = 0;
   */
  OK = 0,

  /**
   * @generated from enum value: NOT_FOUND = 1;
   */
  NOT_FOUND = 1,

  /**
   * @generated from enum value: NOT_ALLOWED = 2;
   */
  NOT_ALLOWED = 2,

  /**
   * @generated from enum value: LIMIT_EXCEEDED = 3;
   */
  LIMIT_EXCEEDED = 3,
}

/**
 * @generated from message livekit.TrackSubscribed
 */
declare class TrackSubscribed extends Message<TrackSubscribed> {
  /**
   * @generated from field: string track_sid = 1;
   */
  trackSid: string;

  constructor(data?: PartialMessage<TrackSubscribed>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.TrackSubscribed";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): TrackSubscribed;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): TrackSubscribed;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): TrackSubscribed;

  static equals(a: TrackSubscribed | PlainMessage<TrackSubscribed> | undefined, b: TrackSubscribed | PlainMessage<TrackSubscribed> | undefined): boolean;
}

// Copyright 2023 LiveKit, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.



/**
 * @generated from enum livekit.SIPStatusCode
 */
declare enum SIPStatusCode {
  /**
   * @generated from enum value: SIP_STATUS_UNKNOWN = 0;
   */
  SIP_STATUS_UNKNOWN = 0,

  /**
   * @generated from enum value: SIP_STATUS_TRYING = 100;
   */
  SIP_STATUS_TRYING = 100,

  /**
   * @generated from enum value: SIP_STATUS_RINGING = 180;
   */
  SIP_STATUS_RINGING = 180,

  /**
   * @generated from enum value: SIP_STATUS_CALL_IS_FORWARDED = 181;
   */
  SIP_STATUS_CALL_IS_FORWARDED = 181,

  /**
   * @generated from enum value: SIP_STATUS_QUEUED = 182;
   */
  SIP_STATUS_QUEUED = 182,

  /**
   * @generated from enum value: SIP_STATUS_SESSION_PROGRESS = 183;
   */
  SIP_STATUS_SESSION_PROGRESS = 183,

  /**
   * @generated from enum value: SIP_STATUS_OK = 200;
   */
  SIP_STATUS_OK = 200,

  /**
   * @generated from enum value: SIP_STATUS_ACCEPTED = 202;
   */
  SIP_STATUS_ACCEPTED = 202,

  /**
   * @generated from enum value: SIP_STATUS_MOVED_PERMANENTLY = 301;
   */
  SIP_STATUS_MOVED_PERMANENTLY = 301,

  /**
   * @generated from enum value: SIP_STATUS_MOVED_TEMPORARILY = 302;
   */
  SIP_STATUS_MOVED_TEMPORARILY = 302,

  /**
   * @generated from enum value: SIP_STATUS_USE_PROXY = 305;
   */
  SIP_STATUS_USE_PROXY = 305,

  /**
   * @generated from enum value: SIP_STATUS_BAD_REQUEST = 400;
   */
  SIP_STATUS_BAD_REQUEST = 400,

  /**
   * @generated from enum value: SIP_STATUS_UNAUTHORIZED = 401;
   */
  SIP_STATUS_UNAUTHORIZED = 401,

  /**
   * @generated from enum value: SIP_STATUS_PAYMENT_REQUIRED = 402;
   */
  SIP_STATUS_PAYMENT_REQUIRED = 402,

  /**
   * @generated from enum value: SIP_STATUS_FORBIDDEN = 403;
   */
  SIP_STATUS_FORBIDDEN = 403,

  /**
   * @generated from enum value: SIP_STATUS_NOTFOUND = 404;
   */
  SIP_STATUS_NOTFOUND = 404,

  /**
   * @generated from enum value: SIP_STATUS_METHOD_NOT_ALLOWED = 405;
   */
  SIP_STATUS_METHOD_NOT_ALLOWED = 405,

  /**
   * @generated from enum value: SIP_STATUS_NOT_ACCEPTABLE = 406;
   */
  SIP_STATUS_NOT_ACCEPTABLE = 406,

  /**
   * @generated from enum value: SIP_STATUS_PROXY_AUTH_REQUIRED = 407;
   */
  SIP_STATUS_PROXY_AUTH_REQUIRED = 407,

  /**
   * @generated from enum value: SIP_STATUS_REQUEST_TIMEOUT = 408;
   */
  SIP_STATUS_REQUEST_TIMEOUT = 408,

  /**
   * @generated from enum value: SIP_STATUS_CONFLICT = 409;
   */
  SIP_STATUS_CONFLICT = 409,

  /**
   * @generated from enum value: SIP_STATUS_GONE = 410;
   */
  SIP_STATUS_GONE = 410,

  /**
   * @generated from enum value: SIP_STATUS_REQUEST_ENTITY_TOO_LARGE = 413;
   */
  SIP_STATUS_REQUEST_ENTITY_TOO_LARGE = 413,

  /**
   * @generated from enum value: SIP_STATUS_REQUEST_URI_TOO_LONG = 414;
   */
  SIP_STATUS_REQUEST_URI_TOO_LONG = 414,

  /**
   * @generated from enum value: SIP_STATUS_UNSUPPORTED_MEDIA_TYPE = 415;
   */
  SIP_STATUS_UNSUPPORTED_MEDIA_TYPE = 415,

  /**
   * @generated from enum value: SIP_STATUS_REQUESTED_RANGE_NOT_SATISFIABLE = 416;
   */
  SIP_STATUS_REQUESTED_RANGE_NOT_SATISFIABLE = 416,

  /**
   * @generated from enum value: SIP_STATUS_BAD_EXTENSION = 420;
   */
  SIP_STATUS_BAD_EXTENSION = 420,

  /**
   * @generated from enum value: SIP_STATUS_EXTENSION_REQUIRED = 421;
   */
  SIP_STATUS_EXTENSION_REQUIRED = 421,

  /**
   * @generated from enum value: SIP_STATUS_INTERVAL_TOO_BRIEF = 423;
   */
  SIP_STATUS_INTERVAL_TOO_BRIEF = 423,

  /**
   * @generated from enum value: SIP_STATUS_TEMPORARILY_UNAVAILABLE = 480;
   */
  SIP_STATUS_TEMPORARILY_UNAVAILABLE = 480,

  /**
   * @generated from enum value: SIP_STATUS_CALL_TRANSACTION_DOES_NOT_EXISTS = 481;
   */
  SIP_STATUS_CALL_TRANSACTION_DOES_NOT_EXISTS = 481,

  /**
   * @generated from enum value: SIP_STATUS_LOOP_DETECTED = 482;
   */
  SIP_STATUS_LOOP_DETECTED = 482,

  /**
   * @generated from enum value: SIP_STATUS_TOO_MANY_HOPS = 483;
   */
  SIP_STATUS_TOO_MANY_HOPS = 483,

  /**
   * @generated from enum value: SIP_STATUS_ADDRESS_INCOMPLETE = 484;
   */
  SIP_STATUS_ADDRESS_INCOMPLETE = 484,

  /**
   * @generated from enum value: SIP_STATUS_AMBIGUOUS = 485;
   */
  SIP_STATUS_AMBIGUOUS = 485,

  /**
   * @generated from enum value: SIP_STATUS_BUSY_HERE = 486;
   */
  SIP_STATUS_BUSY_HERE = 486,

  /**
   * @generated from enum value: SIP_STATUS_REQUEST_TERMINATED = 487;
   */
  SIP_STATUS_REQUEST_TERMINATED = 487,

  /**
   * @generated from enum value: SIP_STATUS_NOT_ACCEPTABLE_HERE = 488;
   */
  SIP_STATUS_NOT_ACCEPTABLE_HERE = 488,

  /**
   * @generated from enum value: SIP_STATUS_INTERNAL_SERVER_ERROR = 500;
   */
  SIP_STATUS_INTERNAL_SERVER_ERROR = 500,

  /**
   * @generated from enum value: SIP_STATUS_NOT_IMPLEMENTED = 501;
   */
  SIP_STATUS_NOT_IMPLEMENTED = 501,

  /**
   * @generated from enum value: SIP_STATUS_BAD_GATEWAY = 502;
   */
  SIP_STATUS_BAD_GATEWAY = 502,

  /**
   * @generated from enum value: SIP_STATUS_SERVICE_UNAVAILABLE = 503;
   */
  SIP_STATUS_SERVICE_UNAVAILABLE = 503,

  /**
   * @generated from enum value: SIP_STATUS_GATEWAY_TIMEOUT = 504;
   */
  SIP_STATUS_GATEWAY_TIMEOUT = 504,

  /**
   * @generated from enum value: SIP_STATUS_VERSION_NOT_SUPPORTED = 505;
   */
  SIP_STATUS_VERSION_NOT_SUPPORTED = 505,

  /**
   * @generated from enum value: SIP_STATUS_MESSAGE_TOO_LARGE = 513;
   */
  SIP_STATUS_MESSAGE_TOO_LARGE = 513,

  /**
   * @generated from enum value: SIP_STATUS_GLOBAL_BUSY_EVERYWHERE = 600;
   */
  SIP_STATUS_GLOBAL_BUSY_EVERYWHERE = 600,

  /**
   * @generated from enum value: SIP_STATUS_GLOBAL_DECLINE = 603;
   */
  SIP_STATUS_GLOBAL_DECLINE = 603,

  /**
   * @generated from enum value: SIP_STATUS_GLOBAL_DOES_NOT_EXIST_ANYWHERE = 604;
   */
  SIP_STATUS_GLOBAL_DOES_NOT_EXIST_ANYWHERE = 604,

  /**
   * @generated from enum value: SIP_STATUS_GLOBAL_NOT_ACCEPTABLE = 606;
   */
  SIP_STATUS_GLOBAL_NOT_ACCEPTABLE = 606,
}

/**
 * @generated from enum livekit.SIPTransport
 */
declare enum SIPTransport {
  /**
   * @generated from enum value: SIP_TRANSPORT_AUTO = 0;
   */
  SIP_TRANSPORT_AUTO = 0,

  /**
   * @generated from enum value: SIP_TRANSPORT_UDP = 1;
   */
  SIP_TRANSPORT_UDP = 1,

  /**
   * @generated from enum value: SIP_TRANSPORT_TCP = 2;
   */
  SIP_TRANSPORT_TCP = 2,

  /**
   * @generated from enum value: SIP_TRANSPORT_TLS = 3;
   */
  SIP_TRANSPORT_TLS = 3,
}

/**
 * @generated from enum livekit.SIPHeaderOptions
 */
declare enum SIPHeaderOptions {
  /**
   * do not map any headers, except ones mapped explicitly
   *
   * @generated from enum value: SIP_NO_HEADERS = 0;
   */
  SIP_NO_HEADERS = 0,

  /**
   * map all X-* headers to sip.h.x-* attributes
   *
   * @generated from enum value: SIP_X_HEADERS = 1;
   */
  SIP_X_HEADERS = 1,

  /**
   * map all headers to sip.h.* attributes
   *
   * @generated from enum value: SIP_ALL_HEADERS = 2;
   */
  SIP_ALL_HEADERS = 2,
}

/**
 * @generated from enum livekit.SIPMediaEncryption
 */
declare enum SIPMediaEncryption {
  /**
   * do not enable encryption
   *
   * @generated from enum value: SIP_MEDIA_ENCRYPT_DISABLE = 0;
   */
  SIP_MEDIA_ENCRYPT_DISABLE = 0,

  /**
   * use encryption if available
   *
   * @generated from enum value: SIP_MEDIA_ENCRYPT_ALLOW = 1;
   */
  SIP_MEDIA_ENCRYPT_ALLOW = 1,

  /**
   * require encryption
   *
   * @generated from enum value: SIP_MEDIA_ENCRYPT_REQUIRE = 2;
   */
  SIP_MEDIA_ENCRYPT_REQUIRE = 2,
}

/**
 * @generated from enum livekit.SIPCallStatus
 */
declare enum SIPCallStatus {
  /**
   * Incoming call is being handled by the SIP service. The SIP participant hasn't joined a LiveKit room yet
   *
   * @generated from enum value: SCS_CALL_INCOMING = 0;
   */
  SCS_CALL_INCOMING = 0,

  /**
   * SIP participant for outgoing call has been created. The SIP outgoing call is being established
   *
   * @generated from enum value: SCS_PARTICIPANT_JOINED = 1;
   */
  SCS_PARTICIPANT_JOINED = 1,

  /**
   * Call is ongoing. SIP participant is active in the LiveKit room
   *
   * @generated from enum value: SCS_ACTIVE = 2;
   */
  SCS_ACTIVE = 2,

  /**
   * Call has ended
   *
   * @generated from enum value: SCS_DISCONNECTED = 3;
   */
  SCS_DISCONNECTED = 3,

  /**
   * Call has ended or never succeeded because of an error
   *
   * @generated from enum value: SCS_ERROR = 4;
   */
  SCS_ERROR = 4,
}

/**
 * @generated from enum livekit.SIPTransferStatus
 */
declare enum SIPTransferStatus {
  /**
   * @generated from enum value: STS_TRANSFER_ONGOING = 0;
   */
  STS_TRANSFER_ONGOING = 0,

  /**
   * @generated from enum value: STS_TRANSFER_FAILED = 1;
   */
  STS_TRANSFER_FAILED = 1,

  /**
   * @generated from enum value: STS_TRANSFER_SUCCESSFUL = 2;
   */
  STS_TRANSFER_SUCCESSFUL = 2,
}

/**
 * @generated from enum livekit.SIPFeature
 */
declare enum SIPFeature {
  /**
   * @generated from enum value: NONE = 0;
   */
  NONE = 0,

  /**
   * @generated from enum value: KRISP_ENABLED = 1;
   */
  KRISP_ENABLED = 1,
}

/**
 * @generated from enum livekit.SIPCallDirection
 */
declare enum SIPCallDirection {
  /**
   * @generated from enum value: SCD_UNKNOWN = 0;
   */
  SCD_UNKNOWN = 0,

  /**
   * @generated from enum value: SCD_INBOUND = 1;
   */
  SCD_INBOUND = 1,

  /**
   * @generated from enum value: SCD_OUTBOUND = 2;
   */
  SCD_OUTBOUND = 2,
}

/**
 * SIPStatus is returned as an error detail in CreateSIPParticipant.
 *
 * @generated from message livekit.SIPStatus
 */
declare class SIPStatus extends Message<SIPStatus> {
  /**
   * @generated from field: livekit.SIPStatusCode code = 1;
   */
  code: SIPStatusCode;

  /**
   * @generated from field: string status = 2;
   */
  status: string;

  constructor(data?: PartialMessage<SIPStatus>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.SIPStatus";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SIPStatus;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SIPStatus;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SIPStatus;

  static equals(a: SIPStatus | PlainMessage<SIPStatus> | undefined, b: SIPStatus | PlainMessage<SIPStatus> | undefined): boolean;
}

/**
 * @generated from message livekit.CreateSIPTrunkRequest
 * @deprecated
 */
declare class CreateSIPTrunkRequest extends Message<CreateSIPTrunkRequest> {
  /**
   * CIDR or IPs that traffic is accepted from
   * An empty list means all inbound traffic is accepted.
   *
   * @generated from field: repeated string inbound_addresses = 1;
   */
  inboundAddresses: string[];

  /**
   * IP that SIP INVITE is sent too
   *
   * @generated from field: string outbound_address = 2;
   */
  outboundAddress: string;

  /**
   * Number used to make outbound calls
   *
   * @generated from field: string outbound_number = 3;
   */
  outboundNumber: string;

  /**
   * @generated from field: repeated string inbound_numbers_regex = 4 [deprecated = true];
   * @deprecated
   */
  inboundNumbersRegex: string[];

  /**
   * Accepted `To` values. This Trunk will only accept a call made to
   * these numbers. This allows you to have distinct Trunks for different phone
   * numbers at the same provider.
   *
   * @generated from field: repeated string inbound_numbers = 9;
   */
  inboundNumbers: string[];

  /**
   * Username and password used to authenticate inbound and outbound SIP invites
   * May be empty to have no Authentication
   *
   * @generated from field: string inbound_username = 5;
   */
  inboundUsername: string;

  /**
   * @generated from field: string inbound_password = 6;
   */
  inboundPassword: string;

  /**
   * @generated from field: string outbound_username = 7;
   */
  outboundUsername: string;

  /**
   * @generated from field: string outbound_password = 8;
   */
  outboundPassword: string;

  /**
   * Optional human-readable name for the Trunk.
   *
   * @generated from field: string name = 10;
   */
  name: string;

  /**
   * Optional user-defined metadata for the Trunk.
   *
   * @generated from field: string metadata = 11;
   */
  metadata: string;

  constructor(data?: PartialMessage<CreateSIPTrunkRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.CreateSIPTrunkRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreateSIPTrunkRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreateSIPTrunkRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreateSIPTrunkRequest;

  static equals(a: CreateSIPTrunkRequest | PlainMessage<CreateSIPTrunkRequest> | undefined, b: CreateSIPTrunkRequest | PlainMessage<CreateSIPTrunkRequest> | undefined): boolean;
}

/**
 * @generated from message livekit.SIPTrunkInfo
 * @deprecated
 */
declare class SIPTrunkInfo extends Message<SIPTrunkInfo> {
  /**
   * @generated from field: string sip_trunk_id = 1;
   */
  sipTrunkId: string;

  /**
   * @generated from field: livekit.SIPTrunkInfo.TrunkKind kind = 14;
   */
  kind: SIPTrunkInfo_TrunkKind;

  /**
   * CIDR or IPs that traffic is accepted from
   * An empty list means all inbound traffic is accepted.
   *
   * @generated from field: repeated string inbound_addresses = 2;
   */
  inboundAddresses: string[];

  /**
   * IP that SIP INVITE is sent too
   *
   * @generated from field: string outbound_address = 3;
   */
  outboundAddress: string;

  /**
   * Number used to make outbound calls
   *
   * @generated from field: string outbound_number = 4;
   */
  outboundNumber: string;

  /**
   * Transport used for inbound and outbound calls.
   *
   * @generated from field: livekit.SIPTransport transport = 13;
   */
  transport: SIPTransport;

  /**
   * @generated from field: repeated string inbound_numbers_regex = 5 [deprecated = true];
   * @deprecated
   */
  inboundNumbersRegex: string[];

  /**
   * Accepted `To` values. This Trunk will only accept a call made to
   * these numbers. This allows you to have distinct Trunks for different phone
   * numbers at the same provider.
   *
   * @generated from field: repeated string inbound_numbers = 10;
   */
  inboundNumbers: string[];

  /**
   * Username and password used to authenticate inbound and outbound SIP invites
   * May be empty to have no Authentication
   *
   * @generated from field: string inbound_username = 6;
   */
  inboundUsername: string;

  /**
   * @generated from field: string inbound_password = 7;
   */
  inboundPassword: string;

  /**
   * @generated from field: string outbound_username = 8;
   */
  outboundUsername: string;

  /**
   * @generated from field: string outbound_password = 9;
   */
  outboundPassword: string;

  /**
   * Human-readable name for the Trunk.
   *
   * @generated from field: string name = 11;
   */
  name: string;

  /**
   * User-defined metadata for the Trunk.
   *
   * @generated from field: string metadata = 12;
   */
  metadata: string;

  constructor(data?: PartialMessage<SIPTrunkInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.SIPTrunkInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SIPTrunkInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SIPTrunkInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SIPTrunkInfo;

  static equals(a: SIPTrunkInfo | PlainMessage<SIPTrunkInfo> | undefined, b: SIPTrunkInfo | PlainMessage<SIPTrunkInfo> | undefined): boolean;
}

/**
 * @generated from enum livekit.SIPTrunkInfo.TrunkKind
 */
declare enum SIPTrunkInfo_TrunkKind {
  /**
   * @generated from enum value: TRUNK_LEGACY = 0;
   */
  TRUNK_LEGACY = 0,

  /**
   * @generated from enum value: TRUNK_INBOUND = 1;
   */
  TRUNK_INBOUND = 1,

  /**
   * @generated from enum value: TRUNK_OUTBOUND = 2;
   */
  TRUNK_OUTBOUND = 2,
}

/**
 * @generated from message livekit.CreateSIPInboundTrunkRequest
 */
declare class CreateSIPInboundTrunkRequest extends Message<CreateSIPInboundTrunkRequest> {
  /**
   * Trunk ID is ignored
   *
   * @generated from field: livekit.SIPInboundTrunkInfo trunk = 1;
   */
  trunk?: SIPInboundTrunkInfo;

  constructor(data?: PartialMessage<CreateSIPInboundTrunkRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.CreateSIPInboundTrunkRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreateSIPInboundTrunkRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreateSIPInboundTrunkRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreateSIPInboundTrunkRequest;

  static equals(a: CreateSIPInboundTrunkRequest | PlainMessage<CreateSIPInboundTrunkRequest> | undefined, b: CreateSIPInboundTrunkRequest | PlainMessage<CreateSIPInboundTrunkRequest> | undefined): boolean;
}

/**
 * @generated from message livekit.UpdateSIPInboundTrunkRequest
 */
declare class UpdateSIPInboundTrunkRequest extends Message<UpdateSIPInboundTrunkRequest> {
  /**
   * @generated from field: string sip_trunk_id = 1;
   */
  sipTrunkId: string;

  /**
   * @generated from oneof livekit.UpdateSIPInboundTrunkRequest.action
   */
  action: {
    /**
     * @generated from field: livekit.SIPInboundTrunkInfo replace = 2;
     */
    value: SIPInboundTrunkInfo;
    case: "replace";
  } | {
    /**
     * @generated from field: livekit.SIPInboundTrunkUpdate update = 3;
     */
    value: SIPInboundTrunkUpdate;
    case: "update";
  } | { case: undefined; value?: undefined };

  constructor(data?: PartialMessage<UpdateSIPInboundTrunkRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.UpdateSIPInboundTrunkRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateSIPInboundTrunkRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateSIPInboundTrunkRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateSIPInboundTrunkRequest;

  static equals(a: UpdateSIPInboundTrunkRequest | PlainMessage<UpdateSIPInboundTrunkRequest> | undefined, b: UpdateSIPInboundTrunkRequest | PlainMessage<UpdateSIPInboundTrunkRequest> | undefined): boolean;
}

/**
 * @generated from message livekit.SIPInboundTrunkInfo
 */
declare class SIPInboundTrunkInfo extends Message<SIPInboundTrunkInfo> {
  /**
   * @generated from field: string sip_trunk_id = 1;
   */
  sipTrunkId: string;

  /**
   * Human-readable name for the Trunk.
   *
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * User-defined metadata for the Trunk.
   *
   * @generated from field: string metadata = 3;
   */
  metadata: string;

  /**
   * Numbers associated with LiveKit SIP. The Trunk will only accept calls made to these numbers.
   * Creating multiple Trunks with different phone numbers allows having different rules for a single provider.
   *
   * @generated from field: repeated string numbers = 4;
   */
  numbers: string[];

  /**
   * CIDR or IPs that traffic is accepted from.
   * An empty list means all inbound traffic is accepted.
   *
   * @generated from field: repeated string allowed_addresses = 5;
   */
  allowedAddresses: string[];

  /**
   * Numbers that are allowed to make calls to this Trunk.
   * An empty list means calls from any phone number is accepted.
   *
   * @generated from field: repeated string allowed_numbers = 6;
   */
  allowedNumbers: string[];

  /**
   * Username and password used to authenticate inbound SIP invites.
   * May be empty to have no authentication.
   *
   * @generated from field: string auth_username = 7;
   */
  authUsername: string;

  /**
   * @generated from field: string auth_password = 8;
   */
  authPassword: string;

  /**
   * Include these SIP X-* headers in 200 OK responses.
   *
   * @generated from field: map<string, string> headers = 9;
   */
  headers: { [key: string]: string };

  /**
   * Map SIP X-* headers from INVITE to SIP participant attributes.
   *
   * @generated from field: map<string, string> headers_to_attributes = 10;
   */
  headersToAttributes: { [key: string]: string };

  /**
   * Map LiveKit attributes to SIP X-* headers when sending BYE or REFER requests.
   * Keys are the names of attributes and values are the names of X-* headers they will be mapped to.
   *
   * @generated from field: map<string, string> attributes_to_headers = 14;
   */
  attributesToHeaders: { [key: string]: string };

  /**
   * Map SIP headers from INVITE to sip.h.* participant attributes automatically.
   *
   * When the names of required headers is known, using headers_to_attributes is strongly recommended.
   *
   * When mapping INVITE headers to response headers with attributes_to_headers map,
   * lowercase header names should be used, for example: sip.h.x-custom-header.
   *
   * @generated from field: livekit.SIPHeaderOptions include_headers = 15;
   */
  includeHeaders: SIPHeaderOptions;

  /**
   * Max time for the caller to wait for track subscription.
   *
   * @generated from field: google.protobuf.Duration ringing_timeout = 11;
   */
  ringingTimeout?: Duration;

  /**
   * Max call duration.
   *
   * @generated from field: google.protobuf.Duration max_call_duration = 12;
   */
  maxCallDuration?: Duration;

  /**
   * @generated from field: bool krisp_enabled = 13;
   */
  krispEnabled: boolean;

  /**
   * @generated from field: livekit.SIPMediaEncryption media_encryption = 16;
   */
  mediaEncryption: SIPMediaEncryption;

  constructor(data?: PartialMessage<SIPInboundTrunkInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.SIPInboundTrunkInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SIPInboundTrunkInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SIPInboundTrunkInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SIPInboundTrunkInfo;

  static equals(a: SIPInboundTrunkInfo | PlainMessage<SIPInboundTrunkInfo> | undefined, b: SIPInboundTrunkInfo | PlainMessage<SIPInboundTrunkInfo> | undefined): boolean;
}

/**
 * @generated from message livekit.SIPInboundTrunkUpdate
 */
declare class SIPInboundTrunkUpdate extends Message<SIPInboundTrunkUpdate> {
  /**
   * @generated from field: livekit.ListUpdate numbers = 1;
   */
  numbers?: ListUpdate;

  /**
   * @generated from field: livekit.ListUpdate allowed_addresses = 2;
   */
  allowedAddresses?: ListUpdate;

  /**
   * @generated from field: livekit.ListUpdate allowed_numbers = 3;
   */
  allowedNumbers?: ListUpdate;

  /**
   * @generated from field: optional string auth_username = 4;
   */
  authUsername?: string;

  /**
   * @generated from field: optional string auth_password = 5;
   */
  authPassword?: string;

  /**
   * @generated from field: optional string name = 6;
   */
  name?: string;

  /**
   * @generated from field: optional string metadata = 7;
   */
  metadata?: string;

  /**
   * @generated from field: optional livekit.SIPMediaEncryption media_encryption = 8;
   */
  mediaEncryption?: SIPMediaEncryption;

  constructor(data?: PartialMessage<SIPInboundTrunkUpdate>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.SIPInboundTrunkUpdate";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SIPInboundTrunkUpdate;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SIPInboundTrunkUpdate;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SIPInboundTrunkUpdate;

  static equals(a: SIPInboundTrunkUpdate | PlainMessage<SIPInboundTrunkUpdate> | undefined, b: SIPInboundTrunkUpdate | PlainMessage<SIPInboundTrunkUpdate> | undefined): boolean;
}

/**
 * @generated from message livekit.CreateSIPOutboundTrunkRequest
 */
declare class CreateSIPOutboundTrunkRequest extends Message<CreateSIPOutboundTrunkRequest> {
  /**
   * Trunk ID is ignored
   *
   * @generated from field: livekit.SIPOutboundTrunkInfo trunk = 1;
   */
  trunk?: SIPOutboundTrunkInfo;

  constructor(data?: PartialMessage<CreateSIPOutboundTrunkRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.CreateSIPOutboundTrunkRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreateSIPOutboundTrunkRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreateSIPOutboundTrunkRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreateSIPOutboundTrunkRequest;

  static equals(a: CreateSIPOutboundTrunkRequest | PlainMessage<CreateSIPOutboundTrunkRequest> | undefined, b: CreateSIPOutboundTrunkRequest | PlainMessage<CreateSIPOutboundTrunkRequest> | undefined): boolean;
}

/**
 * @generated from message livekit.UpdateSIPOutboundTrunkRequest
 */
declare class UpdateSIPOutboundTrunkRequest extends Message<UpdateSIPOutboundTrunkRequest> {
  /**
   * @generated from field: string sip_trunk_id = 1;
   */
  sipTrunkId: string;

  /**
   * @generated from oneof livekit.UpdateSIPOutboundTrunkRequest.action
   */
  action: {
    /**
     * @generated from field: livekit.SIPOutboundTrunkInfo replace = 2;
     */
    value: SIPOutboundTrunkInfo;
    case: "replace";
  } | {
    /**
     * @generated from field: livekit.SIPOutboundTrunkUpdate update = 3;
     */
    value: SIPOutboundTrunkUpdate;
    case: "update";
  } | { case: undefined; value?: undefined };

  constructor(data?: PartialMessage<UpdateSIPOutboundTrunkRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.UpdateSIPOutboundTrunkRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateSIPOutboundTrunkRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateSIPOutboundTrunkRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateSIPOutboundTrunkRequest;

  static equals(a: UpdateSIPOutboundTrunkRequest | PlainMessage<UpdateSIPOutboundTrunkRequest> | undefined, b: UpdateSIPOutboundTrunkRequest | PlainMessage<UpdateSIPOutboundTrunkRequest> | undefined): boolean;
}

/**
 * @generated from message livekit.SIPOutboundTrunkInfo
 */
declare class SIPOutboundTrunkInfo extends Message<SIPOutboundTrunkInfo> {
  /**
   * @generated from field: string sip_trunk_id = 1;
   */
  sipTrunkId: string;

  /**
   * Human-readable name for the Trunk.
   *
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * User-defined metadata for the Trunk.
   *
   * @generated from field: string metadata = 3;
   */
  metadata: string;

  /**
   * Hostname or IP that SIP INVITE is sent too.
   * Note that this is not a SIP URI and should not contain the 'sip:' protocol prefix.
   *
   * @generated from field: string address = 4;
   */
  address: string;

  /**
   * country where the call terminates as ISO 3166-1 alpha-2 (https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2). This will be used by the livekit infrastructure to route calls.
   *
   * @generated from field: string destination_country = 14;
   */
  destinationCountry: string;

  /**
   * SIP Transport used for outbound call.
   *
   * @generated from field: livekit.SIPTransport transport = 5;
   */
  transport: SIPTransport;

  /**
   * Numbers used to make the calls. Random one from this list will be selected.
   *
   * @generated from field: repeated string numbers = 6;
   */
  numbers: string[];

  /**
   * Username and password used to authenticate with SIP server.
   * May be empty to have no authentication.
   *
   * @generated from field: string auth_username = 7;
   */
  authUsername: string;

  /**
   * @generated from field: string auth_password = 8;
   */
  authPassword: string;

  /**
   * Include these SIP X-* headers in INVITE request.
   * These headers are sent as-is and may help identify this call as coming from LiveKit for the other SIP endpoint.
   *
   * @generated from field: map<string, string> headers = 9;
   */
  headers: { [key: string]: string };

  /**
   * Map SIP X-* headers from 200 OK to SIP participant attributes.
   * Keys are the names of X-* headers and values are the names of attributes they will be mapped to.
   *
   * @generated from field: map<string, string> headers_to_attributes = 10;
   */
  headersToAttributes: { [key: string]: string };

  /**
   * Map LiveKit attributes to SIP X-* headers when sending BYE or REFER requests.
   * Keys are the names of attributes and values are the names of X-* headers they will be mapped to.
   *
   * @generated from field: map<string, string> attributes_to_headers = 11;
   */
  attributesToHeaders: { [key: string]: string };

  /**
   * Map SIP headers from 200 OK to sip.h.* participant attributes automatically.
   *
   * When the names of required headers is known, using headers_to_attributes is strongly recommended.
   *
   * When mapping 200 OK headers to follow-up request headers with attributes_to_headers map,
   * lowercase header names should be used, for example: sip.h.x-custom-header.
   *
   * @generated from field: livekit.SIPHeaderOptions include_headers = 12;
   */
  includeHeaders: SIPHeaderOptions;

  /**
   * @generated from field: livekit.SIPMediaEncryption media_encryption = 13;
   */
  mediaEncryption: SIPMediaEncryption;

  constructor(data?: PartialMessage<SIPOutboundTrunkInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.SIPOutboundTrunkInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SIPOutboundTrunkInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SIPOutboundTrunkInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SIPOutboundTrunkInfo;

  static equals(a: SIPOutboundTrunkInfo | PlainMessage<SIPOutboundTrunkInfo> | undefined, b: SIPOutboundTrunkInfo | PlainMessage<SIPOutboundTrunkInfo> | undefined): boolean;
}

/**
 * @generated from message livekit.SIPOutboundTrunkUpdate
 */
declare class SIPOutboundTrunkUpdate extends Message<SIPOutboundTrunkUpdate> {
  /**
   * @generated from field: optional string address = 1;
   */
  address?: string;

  /**
   * @generated from field: optional livekit.SIPTransport transport = 2;
   */
  transport?: SIPTransport;

  /**
   * @generated from field: optional string destination_country = 9;
   */
  destinationCountry?: string;

  /**
   * @generated from field: livekit.ListUpdate numbers = 3;
   */
  numbers?: ListUpdate;

  /**
   * @generated from field: optional string auth_username = 4;
   */
  authUsername?: string;

  /**
   * @generated from field: optional string auth_password = 5;
   */
  authPassword?: string;

  /**
   * @generated from field: optional string name = 6;
   */
  name?: string;

  /**
   * @generated from field: optional string metadata = 7;
   */
  metadata?: string;

  /**
   * @generated from field: optional livekit.SIPMediaEncryption media_encryption = 8;
   */
  mediaEncryption?: SIPMediaEncryption;

  constructor(data?: PartialMessage<SIPOutboundTrunkUpdate>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.SIPOutboundTrunkUpdate";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SIPOutboundTrunkUpdate;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SIPOutboundTrunkUpdate;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SIPOutboundTrunkUpdate;

  static equals(a: SIPOutboundTrunkUpdate | PlainMessage<SIPOutboundTrunkUpdate> | undefined, b: SIPOutboundTrunkUpdate | PlainMessage<SIPOutboundTrunkUpdate> | undefined): boolean;
}

/**
 * @generated from message livekit.GetSIPInboundTrunkRequest
 */
declare class GetSIPInboundTrunkRequest extends Message<GetSIPInboundTrunkRequest> {
  /**
   * @generated from field: string sip_trunk_id = 1;
   */
  sipTrunkId: string;

  constructor(data?: PartialMessage<GetSIPInboundTrunkRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.GetSIPInboundTrunkRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetSIPInboundTrunkRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetSIPInboundTrunkRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetSIPInboundTrunkRequest;

  static equals(a: GetSIPInboundTrunkRequest | PlainMessage<GetSIPInboundTrunkRequest> | undefined, b: GetSIPInboundTrunkRequest | PlainMessage<GetSIPInboundTrunkRequest> | undefined): boolean;
}

/**
 * @generated from message livekit.GetSIPInboundTrunkResponse
 */
declare class GetSIPInboundTrunkResponse extends Message<GetSIPInboundTrunkResponse> {
  /**
   * @generated from field: livekit.SIPInboundTrunkInfo trunk = 1;
   */
  trunk?: SIPInboundTrunkInfo;

  constructor(data?: PartialMessage<GetSIPInboundTrunkResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.GetSIPInboundTrunkResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetSIPInboundTrunkResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetSIPInboundTrunkResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetSIPInboundTrunkResponse;

  static equals(a: GetSIPInboundTrunkResponse | PlainMessage<GetSIPInboundTrunkResponse> | undefined, b: GetSIPInboundTrunkResponse | PlainMessage<GetSIPInboundTrunkResponse> | undefined): boolean;
}

/**
 * @generated from message livekit.GetSIPOutboundTrunkRequest
 */
declare class GetSIPOutboundTrunkRequest extends Message<GetSIPOutboundTrunkRequest> {
  /**
   * @generated from field: string sip_trunk_id = 1;
   */
  sipTrunkId: string;

  constructor(data?: PartialMessage<GetSIPOutboundTrunkRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.GetSIPOutboundTrunkRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetSIPOutboundTrunkRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetSIPOutboundTrunkRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetSIPOutboundTrunkRequest;

  static equals(a: GetSIPOutboundTrunkRequest | PlainMessage<GetSIPOutboundTrunkRequest> | undefined, b: GetSIPOutboundTrunkRequest | PlainMessage<GetSIPOutboundTrunkRequest> | undefined): boolean;
}

/**
 * @generated from message livekit.GetSIPOutboundTrunkResponse
 */
declare class GetSIPOutboundTrunkResponse extends Message<GetSIPOutboundTrunkResponse> {
  /**
   * @generated from field: livekit.SIPOutboundTrunkInfo trunk = 1;
   */
  trunk?: SIPOutboundTrunkInfo;

  constructor(data?: PartialMessage<GetSIPOutboundTrunkResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.GetSIPOutboundTrunkResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetSIPOutboundTrunkResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetSIPOutboundTrunkResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetSIPOutboundTrunkResponse;

  static equals(a: GetSIPOutboundTrunkResponse | PlainMessage<GetSIPOutboundTrunkResponse> | undefined, b: GetSIPOutboundTrunkResponse | PlainMessage<GetSIPOutboundTrunkResponse> | undefined): boolean;
}

/**
 * @generated from message livekit.ListSIPTrunkRequest
 * @deprecated
 */
declare class ListSIPTrunkRequest extends Message<ListSIPTrunkRequest> {
  /**
   * @generated from field: livekit.Pagination page = 1;
   */
  page?: Pagination;

  constructor(data?: PartialMessage<ListSIPTrunkRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.ListSIPTrunkRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListSIPTrunkRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListSIPTrunkRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListSIPTrunkRequest;

  static equals(a: ListSIPTrunkRequest | PlainMessage<ListSIPTrunkRequest> | undefined, b: ListSIPTrunkRequest | PlainMessage<ListSIPTrunkRequest> | undefined): boolean;
}

/**
 * @generated from message livekit.ListSIPTrunkResponse
 * @deprecated
 */
declare class ListSIPTrunkResponse extends Message<ListSIPTrunkResponse> {
  /**
   * @generated from field: repeated livekit.SIPTrunkInfo items = 1;
   */
  items: SIPTrunkInfo[];

  constructor(data?: PartialMessage<ListSIPTrunkResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.ListSIPTrunkResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListSIPTrunkResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListSIPTrunkResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListSIPTrunkResponse;

  static equals(a: ListSIPTrunkResponse | PlainMessage<ListSIPTrunkResponse> | undefined, b: ListSIPTrunkResponse | PlainMessage<ListSIPTrunkResponse> | undefined): boolean;
}

/**
 * ListSIPInboundTrunkRequest lists inbound trunks for given filters. If no filters are set, all trunks are listed.
 *
 * @generated from message livekit.ListSIPInboundTrunkRequest
 */
declare class ListSIPInboundTrunkRequest extends Message<ListSIPInboundTrunkRequest> {
  /**
   * @generated from field: livekit.Pagination page = 3;
   */
  page?: Pagination;

  /**
   * Trunk IDs to list. If this option is set, the response will contains trunks in the same order.
   * If any of the trunks is missing, a nil item in that position will be sent in the response.
   *
   * @generated from field: repeated string trunk_ids = 1;
   */
  trunkIds: string[];

  /**
   * Only list trunks that contain one of the numbers, including wildcard trunks.
   *
   * @generated from field: repeated string numbers = 2;
   */
  numbers: string[];

  constructor(data?: PartialMessage<ListSIPInboundTrunkRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.ListSIPInboundTrunkRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListSIPInboundTrunkRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListSIPInboundTrunkRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListSIPInboundTrunkRequest;

  static equals(a: ListSIPInboundTrunkRequest | PlainMessage<ListSIPInboundTrunkRequest> | undefined, b: ListSIPInboundTrunkRequest | PlainMessage<ListSIPInboundTrunkRequest> | undefined): boolean;
}

/**
 * @generated from message livekit.ListSIPInboundTrunkResponse
 */
declare class ListSIPInboundTrunkResponse extends Message<ListSIPInboundTrunkResponse> {
  /**
   * @generated from field: repeated livekit.SIPInboundTrunkInfo items = 1;
   */
  items: SIPInboundTrunkInfo[];

  constructor(data?: PartialMessage<ListSIPInboundTrunkResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.ListSIPInboundTrunkResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListSIPInboundTrunkResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListSIPInboundTrunkResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListSIPInboundTrunkResponse;

  static equals(a: ListSIPInboundTrunkResponse | PlainMessage<ListSIPInboundTrunkResponse> | undefined, b: ListSIPInboundTrunkResponse | PlainMessage<ListSIPInboundTrunkResponse> | undefined): boolean;
}

/**
 * ListSIPOutboundTrunkRequest lists outbound trunks for given filters. If no filters are set, all trunks are listed.
 *
 * @generated from message livekit.ListSIPOutboundTrunkRequest
 */
declare class ListSIPOutboundTrunkRequest extends Message<ListSIPOutboundTrunkRequest> {
  /**
   * @generated from field: livekit.Pagination page = 3;
   */
  page?: Pagination;

  /**
   * Trunk IDs to list. If this option is set, the response will contains trunks in the same order.
   * If any of the trunks is missing, a nil item in that position will be sent in the response.
   *
   * @generated from field: repeated string trunk_ids = 1;
   */
  trunkIds: string[];

  /**
   * Only list trunks that contain one of the numbers, including wildcard trunks.
   *
   * @generated from field: repeated string numbers = 2;
   */
  numbers: string[];

  constructor(data?: PartialMessage<ListSIPOutboundTrunkRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.ListSIPOutboundTrunkRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListSIPOutboundTrunkRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListSIPOutboundTrunkRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListSIPOutboundTrunkRequest;

  static equals(a: ListSIPOutboundTrunkRequest | PlainMessage<ListSIPOutboundTrunkRequest> | undefined, b: ListSIPOutboundTrunkRequest | PlainMessage<ListSIPOutboundTrunkRequest> | undefined): boolean;
}

/**
 * @generated from message livekit.ListSIPOutboundTrunkResponse
 */
declare class ListSIPOutboundTrunkResponse extends Message<ListSIPOutboundTrunkResponse> {
  /**
   * @generated from field: repeated livekit.SIPOutboundTrunkInfo items = 1;
   */
  items: SIPOutboundTrunkInfo[];

  constructor(data?: PartialMessage<ListSIPOutboundTrunkResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.ListSIPOutboundTrunkResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListSIPOutboundTrunkResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListSIPOutboundTrunkResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListSIPOutboundTrunkResponse;

  static equals(a: ListSIPOutboundTrunkResponse | PlainMessage<ListSIPOutboundTrunkResponse> | undefined, b: ListSIPOutboundTrunkResponse | PlainMessage<ListSIPOutboundTrunkResponse> | undefined): boolean;
}

/**
 * @generated from message livekit.DeleteSIPTrunkRequest
 */
declare class DeleteSIPTrunkRequest extends Message<DeleteSIPTrunkRequest> {
  /**
   * @generated from field: string sip_trunk_id = 1;
   */
  sipTrunkId: string;

  constructor(data?: PartialMessage<DeleteSIPTrunkRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.DeleteSIPTrunkRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeleteSIPTrunkRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeleteSIPTrunkRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeleteSIPTrunkRequest;

  static equals(a: DeleteSIPTrunkRequest | PlainMessage<DeleteSIPTrunkRequest> | undefined, b: DeleteSIPTrunkRequest | PlainMessage<DeleteSIPTrunkRequest> | undefined): boolean;
}

/**
 * @generated from message livekit.SIPDispatchRuleDirect
 */
declare class SIPDispatchRuleDirect extends Message<SIPDispatchRuleDirect> {
  /**
   * What room should call be directed into
   *
   * @generated from field: string room_name = 1;
   */
  roomName: string;

  /**
   * Optional pin required to enter room
   *
   * @generated from field: string pin = 2;
   */
  pin: string;

  constructor(data?: PartialMessage<SIPDispatchRuleDirect>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.SIPDispatchRuleDirect";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SIPDispatchRuleDirect;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SIPDispatchRuleDirect;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SIPDispatchRuleDirect;

  static equals(a: SIPDispatchRuleDirect | PlainMessage<SIPDispatchRuleDirect> | undefined, b: SIPDispatchRuleDirect | PlainMessage<SIPDispatchRuleDirect> | undefined): boolean;
}

/**
 * @generated from message livekit.SIPDispatchRuleIndividual
 */
declare class SIPDispatchRuleIndividual extends Message<SIPDispatchRuleIndividual> {
  /**
   * Prefix used on new room name
   *
   * @generated from field: string room_prefix = 1;
   */
  roomPrefix: string;

  /**
   * Optional pin required to enter room
   *
   * @generated from field: string pin = 2;
   */
  pin: string;

  constructor(data?: PartialMessage<SIPDispatchRuleIndividual>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.SIPDispatchRuleIndividual";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SIPDispatchRuleIndividual;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SIPDispatchRuleIndividual;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SIPDispatchRuleIndividual;

  static equals(a: SIPDispatchRuleIndividual | PlainMessage<SIPDispatchRuleIndividual> | undefined, b: SIPDispatchRuleIndividual | PlainMessage<SIPDispatchRuleIndividual> | undefined): boolean;
}

/**
 * @generated from message livekit.SIPDispatchRuleCallee
 */
declare class SIPDispatchRuleCallee extends Message<SIPDispatchRuleCallee> {
  /**
   * Prefix used on new room name
   *
   * @generated from field: string room_prefix = 1;
   */
  roomPrefix: string;

  /**
   * Optional pin required to enter room
   *
   * @generated from field: string pin = 2;
   */
  pin: string;

  /**
   * Optionally append random suffix
   *
   * @generated from field: bool randomize = 3;
   */
  randomize: boolean;

  constructor(data?: PartialMessage<SIPDispatchRuleCallee>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.SIPDispatchRuleCallee";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SIPDispatchRuleCallee;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SIPDispatchRuleCallee;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SIPDispatchRuleCallee;

  static equals(a: SIPDispatchRuleCallee | PlainMessage<SIPDispatchRuleCallee> | undefined, b: SIPDispatchRuleCallee | PlainMessage<SIPDispatchRuleCallee> | undefined): boolean;
}

/**
 * @generated from message livekit.SIPDispatchRule
 */
declare class SIPDispatchRule extends Message<SIPDispatchRule> {
  /**
   * @generated from oneof livekit.SIPDispatchRule.rule
   */
  rule: {
    /**
     * SIPDispatchRuleDirect is a `SIP Dispatch Rule` that puts a user directly into a room
     * This places users into an existing room. Optionally you can require a pin before a user can
     * enter the room
     *
     * @generated from field: livekit.SIPDispatchRuleDirect dispatch_rule_direct = 1;
     */
    value: SIPDispatchRuleDirect;
    case: "dispatchRuleDirect";
  } | {
    /**
     * SIPDispatchRuleIndividual is a `SIP Dispatch Rule` that creates a new room for each caller.
     *
     * @generated from field: livekit.SIPDispatchRuleIndividual dispatch_rule_individual = 2;
     */
    value: SIPDispatchRuleIndividual;
    case: "dispatchRuleIndividual";
  } | {
    /**
     * SIPDispatchRuleCallee is a `SIP Dispatch Rule` that creates a new room for each callee.
     *
     * @generated from field: livekit.SIPDispatchRuleCallee dispatch_rule_callee = 3;
     */
    value: SIPDispatchRuleCallee;
    case: "dispatchRuleCallee";
  } | { case: undefined; value?: undefined };

  constructor(data?: PartialMessage<SIPDispatchRule>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.SIPDispatchRule";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SIPDispatchRule;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SIPDispatchRule;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SIPDispatchRule;

  static equals(a: SIPDispatchRule | PlainMessage<SIPDispatchRule> | undefined, b: SIPDispatchRule | PlainMessage<SIPDispatchRule> | undefined): boolean;
}

/**
 * @generated from message livekit.CreateSIPDispatchRuleRequest
 */
declare class CreateSIPDispatchRuleRequest extends Message<CreateSIPDispatchRuleRequest> {
  /**
   * Rule ID is ignored
   *
   * @generated from field: livekit.SIPDispatchRuleInfo dispatch_rule = 10;
   */
  dispatchRule?: SIPDispatchRuleInfo;

  /**
   * @generated from field: livekit.SIPDispatchRule rule = 1 [deprecated = true];
   * @deprecated
   */
  rule?: SIPDispatchRule;

  /**
   * What trunks are accepted for this dispatch rule
   * If empty all trunks will match this dispatch rule
   *
   * @generated from field: repeated string trunk_ids = 2 [deprecated = true];
   * @deprecated
   */
  trunkIds: string[];

  /**
   * By default the From value (Phone number) is used for participant name/identity and added to attributes.
   * If true, a random value for identity will be used and numbers will be omitted from attributes.
   *
   * @generated from field: bool hide_phone_number = 3 [deprecated = true];
   * @deprecated
   */
  hidePhoneNumber: boolean;

  /**
   * Dispatch Rule will only accept a call made to these numbers (if set).
   *
   * @generated from field: repeated string inbound_numbers = 6 [deprecated = true];
   * @deprecated
   */
  inboundNumbers: string[];

  /**
   * Optional human-readable name for the Dispatch Rule.
   *
   * @generated from field: string name = 4 [deprecated = true];
   * @deprecated
   */
  name: string;

  /**
   * User-defined metadata for the Dispatch Rule.
   * Participants created by this rule will inherit this metadata.
   *
   * @generated from field: string metadata = 5 [deprecated = true];
   * @deprecated
   */
  metadata: string;

  /**
   * User-defined attributes for the Dispatch Rule.
   * Participants created by this rule will inherit these attributes.
   *
   * @generated from field: map<string, string> attributes = 7 [deprecated = true];
   * @deprecated
   */
  attributes: { [key: string]: string };

  /**
   * Cloud-only, config preset to use
   *
   * @generated from field: string room_preset = 8 [deprecated = true];
   * @deprecated
   */
  roomPreset: string;

  /**
   * RoomConfiguration to use if the participant initiates the room
   *
   * @generated from field: livekit.RoomConfiguration room_config = 9 [deprecated = true];
   * @deprecated
   */
  roomConfig?: RoomConfiguration;

  constructor(data?: PartialMessage<CreateSIPDispatchRuleRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.CreateSIPDispatchRuleRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreateSIPDispatchRuleRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreateSIPDispatchRuleRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreateSIPDispatchRuleRequest;

  static equals(a: CreateSIPDispatchRuleRequest | PlainMessage<CreateSIPDispatchRuleRequest> | undefined, b: CreateSIPDispatchRuleRequest | PlainMessage<CreateSIPDispatchRuleRequest> | undefined): boolean;
}

/**
 * @generated from message livekit.UpdateSIPDispatchRuleRequest
 */
declare class UpdateSIPDispatchRuleRequest extends Message<UpdateSIPDispatchRuleRequest> {
  /**
   * @generated from field: string sip_dispatch_rule_id = 1;
   */
  sipDispatchRuleId: string;

  /**
   * @generated from oneof livekit.UpdateSIPDispatchRuleRequest.action
   */
  action: {
    /**
     * @generated from field: livekit.SIPDispatchRuleInfo replace = 2;
     */
    value: SIPDispatchRuleInfo;
    case: "replace";
  } | {
    /**
     * @generated from field: livekit.SIPDispatchRuleUpdate update = 3;
     */
    value: SIPDispatchRuleUpdate;
    case: "update";
  } | { case: undefined; value?: undefined };

  constructor(data?: PartialMessage<UpdateSIPDispatchRuleRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.UpdateSIPDispatchRuleRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateSIPDispatchRuleRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateSIPDispatchRuleRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateSIPDispatchRuleRequest;

  static equals(a: UpdateSIPDispatchRuleRequest | PlainMessage<UpdateSIPDispatchRuleRequest> | undefined, b: UpdateSIPDispatchRuleRequest | PlainMessage<UpdateSIPDispatchRuleRequest> | undefined): boolean;
}

/**
 * @generated from message livekit.SIPDispatchRuleInfo
 */
declare class SIPDispatchRuleInfo extends Message<SIPDispatchRuleInfo> {
  /**
   * @generated from field: string sip_dispatch_rule_id = 1;
   */
  sipDispatchRuleId: string;

  /**
   * @generated from field: livekit.SIPDispatchRule rule = 2;
   */
  rule?: SIPDispatchRule;

  /**
   * @generated from field: repeated string trunk_ids = 3;
   */
  trunkIds: string[];

  /**
   * @generated from field: bool hide_phone_number = 4;
   */
  hidePhoneNumber: boolean;

  /**
   * Dispatch Rule will only accept a call made to these numbers (if set).
   *
   * @generated from field: repeated string inbound_numbers = 7;
   */
  inboundNumbers: string[];

  /**
   * Human-readable name for the Dispatch Rule.
   *
   * @generated from field: string name = 5;
   */
  name: string;

  /**
   * User-defined metadata for the Dispatch Rule.
   * Participants created by this rule will inherit this metadata.
   *
   * @generated from field: string metadata = 6;
   */
  metadata: string;

  /**
   * User-defined attributes for the Dispatch Rule.
   * Participants created by this rule will inherit these attributes.
   *
   * @generated from field: map<string, string> attributes = 8;
   */
  attributes: { [key: string]: string };

  /**
   * Cloud-only, config preset to use
   *
   * @generated from field: string room_preset = 9;
   */
  roomPreset: string;

  /**
   * RoomConfiguration to use if the participant initiates the room
   *
   * @generated from field: livekit.RoomConfiguration room_config = 10;
   */
  roomConfig?: RoomConfiguration;

  /**
   * @generated from field: bool krisp_enabled = 11;
   */
  krispEnabled: boolean;

  /**
   * NEXT ID: 13
   *
   * @generated from field: livekit.SIPMediaEncryption media_encryption = 12;
   */
  mediaEncryption: SIPMediaEncryption;

  constructor(data?: PartialMessage<SIPDispatchRuleInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.SIPDispatchRuleInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SIPDispatchRuleInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SIPDispatchRuleInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SIPDispatchRuleInfo;

  static equals(a: SIPDispatchRuleInfo | PlainMessage<SIPDispatchRuleInfo> | undefined, b: SIPDispatchRuleInfo | PlainMessage<SIPDispatchRuleInfo> | undefined): boolean;
}

/**
 * @generated from message livekit.SIPDispatchRuleUpdate
 */
declare class SIPDispatchRuleUpdate extends Message<SIPDispatchRuleUpdate> {
  /**
   * @generated from field: livekit.ListUpdate trunk_ids = 1;
   */
  trunkIds?: ListUpdate;

  /**
   * @generated from field: livekit.SIPDispatchRule rule = 2;
   */
  rule?: SIPDispatchRule;

  /**
   * @generated from field: optional string name = 3;
   */
  name?: string;

  /**
   * @generated from field: optional string metadata = 4;
   */
  metadata?: string;

  /**
   * @generated from field: map<string, string> attributes = 5;
   */
  attributes: { [key: string]: string };

  /**
   * @generated from field: optional livekit.SIPMediaEncryption media_encryption = 6;
   */
  mediaEncryption?: SIPMediaEncryption;

  constructor(data?: PartialMessage<SIPDispatchRuleUpdate>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.SIPDispatchRuleUpdate";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SIPDispatchRuleUpdate;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SIPDispatchRuleUpdate;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SIPDispatchRuleUpdate;

  static equals(a: SIPDispatchRuleUpdate | PlainMessage<SIPDispatchRuleUpdate> | undefined, b: SIPDispatchRuleUpdate | PlainMessage<SIPDispatchRuleUpdate> | undefined): boolean;
}

/**
 * ListSIPDispatchRuleRequest lists dispatch rules for given filters. If no filters are set, all rules are listed.
 *
 * @generated from message livekit.ListSIPDispatchRuleRequest
 */
declare class ListSIPDispatchRuleRequest extends Message<ListSIPDispatchRuleRequest> {
  /**
   * @generated from field: livekit.Pagination page = 3;
   */
  page?: Pagination;

  /**
   * Rule IDs to list. If this option is set, the response will contains rules in the same order.
   * If any of the rules is missing, a nil item in that position will be sent in the response.
   *
   * @generated from field: repeated string dispatch_rule_ids = 1;
   */
  dispatchRuleIds: string[];

  /**
   * Only list rules that contain one of the Trunk IDs, including wildcard rules.
   *
   * @generated from field: repeated string trunk_ids = 2;
   */
  trunkIds: string[];

  constructor(data?: PartialMessage<ListSIPDispatchRuleRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.ListSIPDispatchRuleRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListSIPDispatchRuleRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListSIPDispatchRuleRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListSIPDispatchRuleRequest;

  static equals(a: ListSIPDispatchRuleRequest | PlainMessage<ListSIPDispatchRuleRequest> | undefined, b: ListSIPDispatchRuleRequest | PlainMessage<ListSIPDispatchRuleRequest> | undefined): boolean;
}

/**
 * @generated from message livekit.ListSIPDispatchRuleResponse
 */
declare class ListSIPDispatchRuleResponse extends Message<ListSIPDispatchRuleResponse> {
  /**
   * @generated from field: repeated livekit.SIPDispatchRuleInfo items = 1;
   */
  items: SIPDispatchRuleInfo[];

  constructor(data?: PartialMessage<ListSIPDispatchRuleResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.ListSIPDispatchRuleResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListSIPDispatchRuleResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListSIPDispatchRuleResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListSIPDispatchRuleResponse;

  static equals(a: ListSIPDispatchRuleResponse | PlainMessage<ListSIPDispatchRuleResponse> | undefined, b: ListSIPDispatchRuleResponse | PlainMessage<ListSIPDispatchRuleResponse> | undefined): boolean;
}

/**
 * @generated from message livekit.DeleteSIPDispatchRuleRequest
 */
declare class DeleteSIPDispatchRuleRequest extends Message<DeleteSIPDispatchRuleRequest> {
  /**
   * @generated from field: string sip_dispatch_rule_id = 1;
   */
  sipDispatchRuleId: string;

  constructor(data?: PartialMessage<DeleteSIPDispatchRuleRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.DeleteSIPDispatchRuleRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeleteSIPDispatchRuleRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeleteSIPDispatchRuleRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeleteSIPDispatchRuleRequest;

  static equals(a: DeleteSIPDispatchRuleRequest | PlainMessage<DeleteSIPDispatchRuleRequest> | undefined, b: DeleteSIPDispatchRuleRequest | PlainMessage<DeleteSIPDispatchRuleRequest> | undefined): boolean;
}

/**
 * @generated from message livekit.SIPOutboundConfig
 */
declare class SIPOutboundConfig extends Message<SIPOutboundConfig> {
  /**
   * SIP server address
   *
   * @generated from field: string hostname = 1;
   */
  hostname: string;

  /**
   * country where the call terminates as ISO 3166-1 alpha-2 (https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2). This will be used by the livekit infrastructure to route calls.
   *
   * @generated from field: string destination_country = 7;
   */
  destinationCountry: string;

  /**
   * SIP Transport used for outbound call.
   *
   * @generated from field: livekit.SIPTransport transport = 2;
   */
  transport: SIPTransport;

  /**
   * Username and password used to authenticate with SIP server.
   * May be empty to have no authentication.
   *
   * @generated from field: string auth_username = 3;
   */
  authUsername: string;

  /**
   * @generated from field: string auth_password = 4;
   */
  authPassword: string;

  /**
   * Map SIP X-* headers from 200 OK to SIP participant attributes.
   * Keys are the names of X-* headers and values are the names of attributes they will be mapped to.
   *
   * @generated from field: map<string, string> headers_to_attributes = 5;
   */
  headersToAttributes: { [key: string]: string };

  /**
   * Map LiveKit attributes to SIP X-* headers when sending BYE or REFER requests.
   * Keys are the names of attributes and values are the names of X-* headers they will be mapped to.
   *
   * @generated from field: map<string, string> attributes_to_headers = 6;
   */
  attributesToHeaders: { [key: string]: string };

  constructor(data?: PartialMessage<SIPOutboundConfig>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.SIPOutboundConfig";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SIPOutboundConfig;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SIPOutboundConfig;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SIPOutboundConfig;

  static equals(a: SIPOutboundConfig | PlainMessage<SIPOutboundConfig> | undefined, b: SIPOutboundConfig | PlainMessage<SIPOutboundConfig> | undefined): boolean;
}

/**
 * A SIP Participant is a singular SIP session connected to a LiveKit room via
 * a SIP Trunk into a SIP DispatchRule
 *
 * @generated from message livekit.CreateSIPParticipantRequest
 */
declare class CreateSIPParticipantRequest extends Message<CreateSIPParticipantRequest> {
  /**
   * What SIP Trunk should be used to dial the user
   *
   * @generated from field: string sip_trunk_id = 1;
   */
  sipTrunkId: string;

  /**
   * @generated from field: livekit.SIPOutboundConfig trunk = 20;
   */
  trunk?: SIPOutboundConfig;

  /**
   * What number should be dialed via SIP
   *
   * @generated from field: string sip_call_to = 2;
   */
  sipCallTo: string;

  /**
   * Optional SIP From number to use. If empty, trunk number is used.
   *
   * @generated from field: string sip_number = 15;
   */
  sipNumber: string;

  /**
   * What LiveKit room should this participant be connected too
   *
   * @generated from field: string room_name = 3;
   */
  roomName: string;

  /**
   * Optional identity of the participant in LiveKit room
   *
   * @generated from field: string participant_identity = 4;
   */
  participantIdentity: string;

  /**
   * Optional name of the participant in LiveKit room
   *
   * @generated from field: string participant_name = 7;
   */
  participantName: string;

  /**
   * Optional user-defined metadata. Will be attached to a created Participant in the room.
   *
   * @generated from field: string participant_metadata = 8;
   */
  participantMetadata: string;

  /**
   * Optional user-defined attributes. Will be attached to a created Participant in the room.
   *
   * @generated from field: map<string, string> participant_attributes = 9;
   */
  participantAttributes: { [key: string]: string };

  /**
   * Optionally send following DTMF digits (extension codes) when making a call.
   * Character 'w' can be used to add a 0.5 sec delay.
   *
   * @generated from field: string dtmf = 5;
   */
  dtmf: string;

  /**
   * Optionally play dialtone in the room as an audible indicator for existing participants. The `play_ringtone` option is deprectated but has the same effect.
   *
   * @generated from field: bool play_ringtone = 6 [deprecated = true];
   * @deprecated
   */
  playRingtone: boolean;

  /**
   * @generated from field: bool play_dialtone = 13;
   */
  playDialtone: boolean;

  /**
   * By default the From value (Phone number) is used for participant name/identity (if not set) and added to attributes.
   * If true, a random value for identity will be used and numbers will be omitted from attributes.
   *
   * @generated from field: bool hide_phone_number = 10;
   */
  hidePhoneNumber: boolean;

  /**
   * These headers are sent as-is and may help identify this call as coming from LiveKit for the other SIP endpoint.
   *
   * @generated from field: map<string, string> headers = 16;
   */
  headers: { [key: string]: string };

  /**
   * Map SIP headers from 200 OK to sip.h.* participant attributes automatically.
   *
   * When the names of required headers is known, using headers_to_attributes is strongly recommended.
   *
   * When mapping 200 OK headers to follow-up request headers with attributes_to_headers map,
   * lowercase header names should be used, for example: sip.h.x-custom-header.
   *
   * @generated from field: livekit.SIPHeaderOptions include_headers = 17;
   */
  includeHeaders: SIPHeaderOptions;

  /**
   * Max time for the callee to answer the call.
   *
   * @generated from field: google.protobuf.Duration ringing_timeout = 11;
   */
  ringingTimeout?: Duration;

  /**
   * Max call duration.
   *
   * @generated from field: google.protobuf.Duration max_call_duration = 12;
   */
  maxCallDuration?: Duration;

  /**
   * Enable voice isolation for the callee.
   *
   * @generated from field: bool krisp_enabled = 14;
   */
  krispEnabled: boolean;

  /**
   * @generated from field: livekit.SIPMediaEncryption media_encryption = 18;
   */
  mediaEncryption: SIPMediaEncryption;

  /**
   * Wait for the answer for the call before returning.
   *
   * NEXT ID: 21
   *
   * @generated from field: bool wait_until_answered = 19;
   */
  waitUntilAnswered: boolean;

  constructor(data?: PartialMessage<CreateSIPParticipantRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.CreateSIPParticipantRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreateSIPParticipantRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreateSIPParticipantRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreateSIPParticipantRequest;

  static equals(a: CreateSIPParticipantRequest | PlainMessage<CreateSIPParticipantRequest> | undefined, b: CreateSIPParticipantRequest | PlainMessage<CreateSIPParticipantRequest> | undefined): boolean;
}

/**
 * @generated from message livekit.SIPParticipantInfo
 */
declare class SIPParticipantInfo extends Message<SIPParticipantInfo> {
  /**
   * @generated from field: string participant_id = 1;
   */
  participantId: string;

  /**
   * @generated from field: string participant_identity = 2;
   */
  participantIdentity: string;

  /**
   * @generated from field: string room_name = 3;
   */
  roomName: string;

  /**
   * @generated from field: string sip_call_id = 4;
   */
  sipCallId: string;

  constructor(data?: PartialMessage<SIPParticipantInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.SIPParticipantInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SIPParticipantInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SIPParticipantInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SIPParticipantInfo;

  static equals(a: SIPParticipantInfo | PlainMessage<SIPParticipantInfo> | undefined, b: SIPParticipantInfo | PlainMessage<SIPParticipantInfo> | undefined): boolean;
}

/**
 * @generated from message livekit.TransferSIPParticipantRequest
 */
declare class TransferSIPParticipantRequest extends Message<TransferSIPParticipantRequest> {
  /**
   * @generated from field: string participant_identity = 1;
   */
  participantIdentity: string;

  /**
   * @generated from field: string room_name = 2;
   */
  roomName: string;

  /**
   * @generated from field: string transfer_to = 3;
   */
  transferTo: string;

  /**
   * Optionally play dialtone to the SIP participant as an audible indicator of being transferred
   *
   * @generated from field: bool play_dialtone = 4;
   */
  playDialtone: boolean;

  /**
   * Add the following headers to the REFER SIP request.
   *
   * @generated from field: map<string, string> headers = 5;
   */
  headers: { [key: string]: string };

  /**
   * Max time for the transfer destination to answer the call.
   *
   * @generated from field: google.protobuf.Duration ringing_timeout = 6;
   */
  ringingTimeout?: Duration;

  constructor(data?: PartialMessage<TransferSIPParticipantRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.TransferSIPParticipantRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): TransferSIPParticipantRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): TransferSIPParticipantRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): TransferSIPParticipantRequest;

  static equals(a: TransferSIPParticipantRequest | PlainMessage<TransferSIPParticipantRequest> | undefined, b: TransferSIPParticipantRequest | PlainMessage<TransferSIPParticipantRequest> | undefined): boolean;
}

/**
 * @generated from message livekit.SIPCallInfo
 */
declare class SIPCallInfo extends Message<SIPCallInfo> {
  /**
   * @generated from field: string call_id = 1;
   */
  callId: string;

  /**
   * @generated from field: string trunk_id = 2;
   */
  trunkId: string;

  /**
   * @generated from field: string dispatch_rule_id = 16;
   */
  dispatchRuleId: string;

  /**
   * @generated from field: string region = 17;
   */
  region: string;

  /**
   * @generated from field: string room_name = 3;
   */
  roomName: string;

  /**
   * ID of the current/previous room published to
   *
   * @generated from field: string room_id = 4;
   */
  roomId: string;

  /**
   * @generated from field: string participant_identity = 5;
   */
  participantIdentity: string;

  /**
   * @generated from field: map<string, string> participant_attributes = 18;
   */
  participantAttributes: { [key: string]: string };

  /**
   * @generated from field: livekit.SIPUri from_uri = 6;
   */
  fromUri?: SIPUri;

  /**
   * @generated from field: livekit.SIPUri to_uri = 7;
   */
  toUri?: SIPUri;

  /**
   * @generated from field: int64 created_at = 9 [deprecated = true];
   * @deprecated
   */
  createdAt: bigint;

  /**
   * @generated from field: int64 started_at = 10 [deprecated = true];
   * @deprecated
   */
  startedAt: bigint;

  /**
   * @generated from field: int64 ended_at = 11 [deprecated = true];
   * @deprecated
   */
  endedAt: bigint;

  /**
   * @generated from field: repeated livekit.SIPFeature enabled_features = 14;
   */
  enabledFeatures: SIPFeature[];

  /**
   * @generated from field: livekit.SIPCallDirection call_direction = 15;
   */
  callDirection: SIPCallDirection;

  /**
   * @generated from field: livekit.SIPCallStatus call_status = 8;
   */
  callStatus: SIPCallStatus;

  /**
   * @generated from field: int64 created_at_ns = 22;
   */
  createdAtNs: bigint;

  /**
   * @generated from field: int64 started_at_ns = 23;
   */
  startedAtNs: bigint;

  /**
   * @generated from field: int64 ended_at_ns = 24;
   */
  endedAtNs: bigint;

  /**
   * @generated from field: livekit.DisconnectReason disconnect_reason = 12;
   */
  disconnectReason: DisconnectReason;

  /**
   * @generated from field: string error = 13;
   */
  error: string;

  /**
   * @generated from field: livekit.SIPStatus call_status_code = 19;
   */
  callStatusCode?: SIPStatus;

  /**
   * @generated from field: string audio_codec = 20;
   */
  audioCodec: string;

  /**
   * @generated from field: string media_encryption = 21;
   */
  mediaEncryption: string;

  constructor(data?: PartialMessage<SIPCallInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.SIPCallInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SIPCallInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SIPCallInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SIPCallInfo;

  static equals(a: SIPCallInfo | PlainMessage<SIPCallInfo> | undefined, b: SIPCallInfo | PlainMessage<SIPCallInfo> | undefined): boolean;
}

/**
 * @generated from message livekit.SIPTransferInfo
 */
declare class SIPTransferInfo extends Message<SIPTransferInfo> {
  /**
   * @generated from field: string transfer_id = 1;
   */
  transferId: string;

  /**
   * @generated from field: string call_id = 2;
   */
  callId: string;

  /**
   * @generated from field: string transfer_to = 3;
   */
  transferTo: string;

  /**
   * @generated from field: int64 transfer_initiated_at_ns = 4;
   */
  transferInitiatedAtNs: bigint;

  /**
   * @generated from field: int64 transfer_completed_at_ns = 5;
   */
  transferCompletedAtNs: bigint;

  /**
   * @generated from field: livekit.SIPTransferStatus transfer_status = 6;
   */
  transferStatus: SIPTransferStatus;

  /**
   * @generated from field: string error = 7;
   */
  error: string;

  /**
   * @generated from field: livekit.SIPStatus transfer_status_code = 8;
   */
  transferStatusCode?: SIPStatus;

  constructor(data?: PartialMessage<SIPTransferInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.SIPTransferInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SIPTransferInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SIPTransferInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SIPTransferInfo;

  static equals(a: SIPTransferInfo | PlainMessage<SIPTransferInfo> | undefined, b: SIPTransferInfo | PlainMessage<SIPTransferInfo> | undefined): boolean;
}

/**
 * @generated from message livekit.SIPUri
 */
declare class SIPUri extends Message<SIPUri> {
  /**
   * @generated from field: string user = 1;
   */
  user: string;

  /**
   * @generated from field: string host = 2;
   */
  host: string;

  /**
   * @generated from field: string ip = 3;
   */
  ip: string;

  /**
   * @generated from field: uint32 port = 4;
   */
  port: number;

  /**
   * @generated from field: livekit.SIPTransport transport = 5;
   */
  transport: SIPTransport;

  constructor(data?: PartialMessage<SIPUri>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.SIPUri";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SIPUri;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SIPUri;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SIPUri;

  static equals(a: SIPUri | PlainMessage<SIPUri> | undefined, b: SIPUri | PlainMessage<SIPUri> | undefined): boolean;
}

// Copyright 2023 LiveKit, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.



/**
 * @generated from message livekit.WebhookEvent
 */
declare class WebhookEvent extends Message<WebhookEvent> {
  /**
   * one of room_started, room_finished, participant_joined, participant_left,
   * track_published, track_unpublished, egress_started, egress_updated, egress_ended,
   * ingress_started, ingress_ended
   *
   * @generated from field: string event = 1;
   */
  event: string;

  /**
   * @generated from field: livekit.Room room = 2;
   */
  room?: Room;

  /**
   * set when event is participant_* or track_*
   *
   * @generated from field: livekit.ParticipantInfo participant = 3;
   */
  participant?: ParticipantInfo;

  /**
   * set when event is egress_*
   *
   * @generated from field: livekit.EgressInfo egress_info = 9;
   */
  egressInfo?: EgressInfo;

  /**
   * set when event is ingress_*
   *
   * @generated from field: livekit.IngressInfo ingress_info = 10;
   */
  ingressInfo?: IngressInfo;

  /**
   * set when event is track_*
   *
   * @generated from field: livekit.TrackInfo track = 8;
   */
  track?: TrackInfo;

  /**
   * unique event uuid
   *
   * @generated from field: string id = 6;
   */
  id: string;

  /**
   * timestamp in seconds
   *
   * @generated from field: int64 created_at = 7;
   */
  createdAt: bigint;

  /**
   * @generated from field: int32 num_dropped = 11 [deprecated = true];
   * @deprecated
   */
  numDropped: number;

  constructor(data?: PartialMessage<WebhookEvent>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "livekit.WebhookEvent";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): WebhookEvent;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): WebhookEvent;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): WebhookEvent;

  static equals(a: WebhookEvent | PlainMessage<WebhookEvent> | undefined, b: WebhookEvent | PlainMessage<WebhookEvent> | undefined): boolean;
}

declare const version: string;

export { ActiveSpeakerUpdate, AddTrackRequest, AgentDispatch, AgentDispatchState, AliOSSUpload, AudioCodec, AudioMixing, AudioTrackFeature, AutoParticipantEgress, AutoTrackEgress, AvailabilityRequest, AvailabilityResponse, AzureBlobUpload, BackupCodecPolicy, CandidateProtocol, ChatMessage, ClientConfigSetting, ClientConfiguration, ClientInfo, ClientInfo_SDK, Codec, ConnectionQuality, ConnectionQualityInfo, ConnectionQualityUpdate, CreateAgentDispatchRequest, CreateIngressRequest, CreateRoomRequest, CreateSIPDispatchRuleRequest, CreateSIPInboundTrunkRequest, CreateSIPOutboundTrunkRequest, CreateSIPParticipantRequest, CreateSIPTrunkRequest, DataChannelInfo, DataChannelReceiveState, DataPacket, DataPacket_Kind, DataStream, DataStream_ByteHeader, DataStream_Chunk, DataStream_Header, DataStream_OperationType, DataStream_TextHeader, DataStream_Trailer, DeleteAgentDispatchRequest, DeleteIngressRequest, DeleteRoomRequest, DeleteRoomResponse, DeleteSIPDispatchRuleRequest, DeleteSIPTrunkRequest, DirectFileOutput, DisabledCodecs, DisconnectReason, EgressInfo, EgressSourceType, EgressStatus, EncodedFileOutput, EncodedFileType, EncodingOptions, EncodingOptionsPreset, Encryption, Encryption_Type, EventMetric, FileInfo, ForwardParticipantRequest, ForwardParticipantResponse, GCPUpload, GetSIPInboundTrunkRequest, GetSIPInboundTrunkResponse, GetSIPOutboundTrunkRequest, GetSIPOutboundTrunkResponse, ICEServer, ImageCodec, ImageFileSuffix, ImageOutput, ImagesInfo, IngressAudioEncodingOptions, IngressAudioEncodingPreset, IngressAudioOptions, IngressInfo, IngressInput, IngressState, IngressState_Status, IngressVideoEncodingOptions, IngressVideoEncodingPreset, IngressVideoOptions, InputAudioState, InputVideoState, Job, JobAssignment, JobState, JobStatus, JobTermination, JobType, JoinResponse, LeaveRequest, LeaveRequest_Action, ListAgentDispatchRequest, ListAgentDispatchResponse, ListEgressRequest, ListEgressResponse, ListIngressRequest, ListIngressResponse, ListParticipantsRequest, ListParticipantsResponse, ListRoomsRequest, ListRoomsResponse, ListSIPDispatchRuleRequest, ListSIPDispatchRuleResponse, ListSIPInboundTrunkRequest, ListSIPInboundTrunkResponse, ListSIPOutboundTrunkRequest, ListSIPOutboundTrunkResponse, ListSIPTrunkRequest, ListSIPTrunkResponse, ListUpdate, MetricLabel, MetricSample, MetricsBatch, MigrateJobRequest, MoveParticipantRequest, MoveParticipantResponse, MuteRoomTrackRequest, MuteRoomTrackResponse, MuteTrackRequest, Pagination, ParticipantEgressRequest, ParticipantInfo, ParticipantInfo_Kind, ParticipantInfo_KindDetail, ParticipantInfo_State, ParticipantPermission, ParticipantTracks, ParticipantUpdate, Ping, PlayoutDelay, Pong, ProxyConfig, RTCPSenderReportState, RTPDrift, RTPForwarderState, RTPMungerState, RTPStats, ReconnectReason, ReconnectResponse, RegionInfo, RegionSettings, RegisterWorkerRequest, RegisterWorkerResponse, RemoveParticipantResponse, RequestResponse, RequestResponse_Reason, Room, RoomAgent, RoomAgentDispatch, RoomCompositeEgressRequest, RoomConfiguration, RoomEgress, RoomMovedResponse, RoomParticipantIdentity, RoomUpdate, RpcAck, RpcError, RpcRequest, RpcResponse, S3Upload, SIPCallDirection, SIPCallInfo, SIPCallStatus, SIPDispatchRule, SIPDispatchRuleCallee, SIPDispatchRuleDirect, SIPDispatchRuleIndividual, SIPDispatchRuleInfo, SIPDispatchRuleUpdate, SIPFeature, SIPHeaderOptions, SIPInboundTrunkInfo, SIPInboundTrunkUpdate, SIPMediaEncryption, SIPOutboundConfig, SIPOutboundTrunkInfo, SIPOutboundTrunkUpdate, SIPParticipantInfo, SIPStatus, SIPStatusCode, SIPTransferInfo, SIPTransferStatus, SIPTransport, SIPTrunkInfo, SIPTrunkInfo_TrunkKind, SIPUri, SegmentedFileOutput, SegmentedFileProtocol, SegmentedFileSuffix, SegmentsInfo, SendDataRequest, SendDataResponse, ServerInfo, ServerInfo_Edition, ServerMessage, SessionDescription, SignalRequest, SignalResponse, SignalTarget, SimulateJobRequest, SimulateScenario, SimulcastCodec, SimulcastCodecInfo, SipDTMF, SpeakerInfo, SpeakersChanged, StopEgressRequest, StreamInfo, StreamInfoList, StreamInfo_Status, StreamOutput, StreamProtocol, StreamState, StreamStateInfo, StreamStateUpdate, SubscribedCodec, SubscribedQuality, SubscribedQualityUpdate, SubscriptionError, SubscriptionPermission, SubscriptionPermissionUpdate, SubscriptionResponse, SyncState, TimeSeriesMetric, TimedVersion, TrackCompositeEgressRequest, TrackEgressRequest, TrackInfo, TrackPermission, TrackPublishedResponse, TrackSource, TrackSubscribed, TrackType, TrackUnpublishedResponse, Transcription, TranscriptionSegment, TransferSIPParticipantRequest, TrickleRequest, UpdateIngressRequest, UpdateJobStatus, UpdateLayoutRequest, UpdateLocalAudioTrack, UpdateLocalVideoTrack, UpdateParticipantMetadata, UpdateParticipantRequest, UpdateRoomMetadataRequest, UpdateSIPDispatchRuleRequest, UpdateSIPInboundTrunkRequest, UpdateSIPOutboundTrunkRequest, UpdateStreamRequest, UpdateSubscription, UpdateSubscriptionsRequest, UpdateSubscriptionsResponse, UpdateTrackSettings, UpdateVideoLayers, UpdateWorkerStatus, UserPacket, VP8MungerState, VideoCodec, VideoConfiguration, VideoLayer, VideoQuality, WebEgressRequest, WebhookConfig, WebhookEvent, WorkerMessage, WorkerPing, WorkerPong, WorkerStatus, version };
