"use strict";
// Copyright 2021-2024 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
Object.defineProperty(exports, "__esModule", { value: true });
exports.Empty = void 0;
const message_js_1 = require("../../message.js");
const proto3_js_1 = require("../../proto3.js");
/**
 * A generic empty message that you can re-use to avoid defining duplicated
 * empty messages in your APIs. A typical example is to use it as the request
 * or the response type of an API method. For instance:
 *
 *     service Foo {
 *       rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty);
 *     }
 *
 *
 * @generated from message google.protobuf.Empty
 */
class Empty extends message_js_1.Message {
    constructor(data) {
        super();
        proto3_js_1.proto3.util.initPartial(data, this);
    }
    static fromBinary(bytes, options) {
        return new Empty().fromBinary(bytes, options);
    }
    static fromJson(jsonValue, options) {
        return new Empty().fromJson(jsonValue, options);
    }
    static fromJsonString(jsonString, options) {
        return new Empty().fromJsonString(jsonString, options);
    }
    static equals(a, b) {
        return proto3_js_1.proto3.util.equals(Empty, a, b);
    }
}
exports.Empty = Empty;
Empty.runtime = proto3_js_1.proto3;
Empty.typeName = "google.protobuf.Empty";
Empty.fields = proto3_js_1.proto3.util.newFieldList(() => []);
