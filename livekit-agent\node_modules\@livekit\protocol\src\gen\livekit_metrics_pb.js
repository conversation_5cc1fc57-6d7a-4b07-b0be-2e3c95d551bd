// @generated by protoc-gen-es v1.10.1 with parameter "target=dts+js"
// @generated from file livekit_metrics.proto (package livekit, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { proto3, Timestamp } from "@bufbuild/protobuf";

/**
 * index from [0: MAX_LABEL_PREDEFINED_MAX_VALUE) are for predefined labels (`MetricLabel`)
 *
 * @generated from enum livekit.MetricLabel
 */
export const MetricLabel = /*@__PURE__*/ proto3.makeEnum(
  "livekit.MetricLabel",
  [
    {no: 0, name: "AGENTS_LLM_TTFT"},
    {no: 1, name: "AGENTS_STT_TTFT"},
    {no: 2, name: "AGENTS_TTS_TTFB"},
    {no: 3, name: "CLIENT_VIDEO_SUBSCRIBER_FREEZE_COUNT"},
    {no: 4, name: "CLIENT_VIDEO_SUBSCRIBER_TOTAL_FREEZE_DURATION"},
    {no: 5, name: "CLIENT_VIDEO_SUBSCRIBER_PAUSE_COUNT"},
    {no: 6, name: "CLIENT_VIDEO_SUBSCRIBER_TOTAL_PAUSES_DURATION"},
    {no: 7, name: "CLIENT_AUDIO_SUBSCRIBER_CONCEALED_SAMPLES"},
    {no: 8, name: "CLIENT_AUDIO_SUBSCRIBER_SILENT_CONCEALED_SAMPLES"},
    {no: 9, name: "CLIENT_AUDIO_SUBSCRIBER_CONCEALMENT_EVENTS"},
    {no: 10, name: "CLIENT_AUDIO_SUBSCRIBER_INTERRUPTION_COUNT"},
    {no: 11, name: "CLIENT_AUDIO_SUBSCRIBER_TOTAL_INTERRUPTION_DURATION"},
    {no: 12, name: "CLIENT_SUBSCRIBER_JITTER_BUFFER_DELAY"},
    {no: 13, name: "CLIENT_SUBSCRIBER_JITTER_BUFFER_EMITTED_COUNT"},
    {no: 14, name: "CLIENT_VIDEO_PUBLISHER_QUALITY_LIMITATION_DURATION_BANDWIDTH"},
    {no: 15, name: "CLIENT_VIDEO_PUBLISHER_QUALITY_LIMITATION_DURATION_CPU"},
    {no: 16, name: "CLIENT_VIDEO_PUBLISHER_QUALITY_LIMITATION_DURATION_OTHER"},
    {no: 17, name: "PUBLISHER_RTT"},
    {no: 18, name: "SERVER_MESH_RTT"},
    {no: 19, name: "SUBSCRIBER_RTT"},
    {no: 4096, name: "METRIC_LABEL_PREDEFINED_MAX_VALUE"},
  ],
);

/**
 * @generated from message livekit.MetricsBatch
 */
export const MetricsBatch = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.MetricsBatch",
  () => [
    { no: 1, name: "timestamp_ms", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
    { no: 2, name: "normalized_timestamp", kind: "message", T: Timestamp },
    { no: 3, name: "str_data", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
    { no: 4, name: "time_series", kind: "message", T: TimeSeriesMetric, repeated: true },
    { no: 5, name: "events", kind: "message", T: EventMetric, repeated: true },
  ],
);

/**
 * @generated from message livekit.TimeSeriesMetric
 */
export const TimeSeriesMetric = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.TimeSeriesMetric",
  () => [
    { no: 1, name: "label", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 2, name: "participant_identity", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 3, name: "track_sid", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 4, name: "samples", kind: "message", T: MetricSample, repeated: true },
    { no: 5, name: "rid", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
  ],
);

/**
 * @generated from message livekit.MetricSample
 */
export const MetricSample = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.MetricSample",
  () => [
    { no: 1, name: "timestamp_ms", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
    { no: 2, name: "normalized_timestamp", kind: "message", T: Timestamp },
    { no: 3, name: "value", kind: "scalar", T: 2 /* ScalarType.FLOAT */ },
  ],
);

/**
 * @generated from message livekit.EventMetric
 */
export const EventMetric = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.EventMetric",
  () => [
    { no: 1, name: "label", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 2, name: "participant_identity", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 3, name: "track_sid", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 4, name: "start_timestamp_ms", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
    { no: 5, name: "end_timestamp_ms", kind: "scalar", T: 3 /* ScalarType.INT64 */, opt: true },
    { no: 6, name: "normalized_start_timestamp", kind: "message", T: Timestamp },
    { no: 7, name: "normalized_end_timestamp", kind: "message", T: Timestamp, opt: true },
    { no: 8, name: "metadata", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 9, name: "rid", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
  ],
);

