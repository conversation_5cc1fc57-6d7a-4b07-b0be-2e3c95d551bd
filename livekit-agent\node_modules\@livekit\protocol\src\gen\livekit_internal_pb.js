// Copyright 2023 LiveKit, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// @generated by protoc-gen-es v1.10.1 with parameter "target=dts+js"
// @generated from file livekit_internal.proto (package livekit, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { proto3 } from "@bufbuild/protobuf";
import { ClientInfo, PlayoutDelay, ReconnectReason } from "./livekit_models_pb.js";
import { CreateRoomRequest } from "./livekit_room_pb.js";
import { AutoParticipantEgress, AutoTrackEgress } from "./livekit_egress_pb.js";
import { RoomAgentDispatch } from "./livekit_agent_dispatch_pb.js";

/**
 * @generated from enum livekit.NodeType
 */
export const NodeType = /*@__PURE__*/ proto3.makeEnum(
  "livekit.NodeType",
  [
    {no: 0, name: "SERVER"},
    {no: 1, name: "CONTROLLER"},
    {no: 2, name: "MEDIA"},
    {no: 4, name: "TURN"},
    {no: 5, name: "SWEEPER"},
    {no: 6, name: "DIRECTOR"},
    {no: 7, name: "HOSTED_AGENT"},
  ],
);

/**
 * @generated from enum livekit.NodeState
 */
export const NodeState = /*@__PURE__*/ proto3.makeEnum(
  "livekit.NodeState",
  [
    {no: 0, name: "STARTING_UP"},
    {no: 1, name: "SERVING"},
    {no: 2, name: "SHUTTING_DOWN"},
  ],
);

/**
 * @generated from enum livekit.ICECandidateType
 */
export const ICECandidateType = /*@__PURE__*/ proto3.makeEnum(
  "livekit.ICECandidateType",
  [
    {no: 0, name: "ICT_NONE"},
    {no: 1, name: "ICT_TCP"},
    {no: 2, name: "ICT_TLS"},
  ],
);

/**
 * @generated from message livekit.Node
 */
export const Node = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.Node",
  () => [
    { no: 1, name: "id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "ip", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "num_cpus", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 4, name: "stats", kind: "message", T: NodeStats },
    { no: 5, name: "type", kind: "enum", T: proto3.getEnumType(NodeType) },
    { no: 6, name: "state", kind: "enum", T: proto3.getEnumType(NodeState) },
    { no: 7, name: "region", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ],
);

/**
 * @generated from message livekit.NodeStats
 */
export const NodeStats = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.NodeStats",
  () => [
    { no: 1, name: "started_at", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
    { no: 2, name: "updated_at", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
    { no: 3, name: "num_rooms", kind: "scalar", T: 5 /* ScalarType.INT32 */ },
    { no: 4, name: "num_clients", kind: "scalar", T: 5 /* ScalarType.INT32 */ },
    { no: 5, name: "num_tracks_in", kind: "scalar", T: 5 /* ScalarType.INT32 */ },
    { no: 6, name: "num_tracks_out", kind: "scalar", T: 5 /* ScalarType.INT32 */ },
    { no: 36, name: "num_track_publish_attempts", kind: "scalar", T: 5 /* ScalarType.INT32 */ },
    { no: 37, name: "track_publish_attempts_per_sec", kind: "scalar", T: 2 /* ScalarType.FLOAT */ },
    { no: 38, name: "num_track_publish_success", kind: "scalar", T: 5 /* ScalarType.INT32 */ },
    { no: 39, name: "track_publish_success_per_sec", kind: "scalar", T: 2 /* ScalarType.FLOAT */ },
    { no: 40, name: "num_track_subscribe_attempts", kind: "scalar", T: 5 /* ScalarType.INT32 */ },
    { no: 41, name: "track_subscribe_attempts_per_sec", kind: "scalar", T: 2 /* ScalarType.FLOAT */ },
    { no: 42, name: "num_track_subscribe_success", kind: "scalar", T: 5 /* ScalarType.INT32 */ },
    { no: 43, name: "track_subscribe_success_per_sec", kind: "scalar", T: 2 /* ScalarType.FLOAT */ },
    { no: 7, name: "bytes_in", kind: "scalar", T: 4 /* ScalarType.UINT64 */ },
    { no: 8, name: "bytes_out", kind: "scalar", T: 4 /* ScalarType.UINT64 */ },
    { no: 9, name: "packets_in", kind: "scalar", T: 4 /* ScalarType.UINT64 */ },
    { no: 10, name: "packets_out", kind: "scalar", T: 4 /* ScalarType.UINT64 */ },
    { no: 11, name: "nack_total", kind: "scalar", T: 4 /* ScalarType.UINT64 */ },
    { no: 12, name: "bytes_in_per_sec", kind: "scalar", T: 2 /* ScalarType.FLOAT */ },
    { no: 13, name: "bytes_out_per_sec", kind: "scalar", T: 2 /* ScalarType.FLOAT */ },
    { no: 14, name: "packets_in_per_sec", kind: "scalar", T: 2 /* ScalarType.FLOAT */ },
    { no: 15, name: "packets_out_per_sec", kind: "scalar", T: 2 /* ScalarType.FLOAT */ },
    { no: 16, name: "nack_per_sec", kind: "scalar", T: 2 /* ScalarType.FLOAT */ },
    { no: 17, name: "num_cpus", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 18, name: "load_avg_last1min", kind: "scalar", T: 2 /* ScalarType.FLOAT */ },
    { no: 19, name: "load_avg_last5min", kind: "scalar", T: 2 /* ScalarType.FLOAT */ },
    { no: 20, name: "load_avg_last15min", kind: "scalar", T: 2 /* ScalarType.FLOAT */ },
    { no: 21, name: "cpu_load", kind: "scalar", T: 2 /* ScalarType.FLOAT */ },
    { no: 33, name: "memory_load", kind: "scalar", T: 2 /* ScalarType.FLOAT */ },
    { no: 34, name: "memory_total", kind: "scalar", T: 4 /* ScalarType.UINT64 */ },
    { no: 35, name: "memory_used", kind: "scalar", T: 4 /* ScalarType.UINT64 */ },
    { no: 28, name: "sys_packets_out", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 29, name: "sys_packets_dropped", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 30, name: "sys_packets_out_per_sec", kind: "scalar", T: 2 /* ScalarType.FLOAT */ },
    { no: 31, name: "sys_packets_dropped_per_sec", kind: "scalar", T: 2 /* ScalarType.FLOAT */ },
    { no: 32, name: "sys_packets_dropped_pct_per_sec", kind: "scalar", T: 2 /* ScalarType.FLOAT */ },
    { no: 22, name: "retransmit_bytes_out", kind: "scalar", T: 4 /* ScalarType.UINT64 */ },
    { no: 23, name: "retransmit_packets_out", kind: "scalar", T: 4 /* ScalarType.UINT64 */ },
    { no: 24, name: "retransmit_bytes_out_per_sec", kind: "scalar", T: 2 /* ScalarType.FLOAT */ },
    { no: 25, name: "retransmit_packets_out_per_sec", kind: "scalar", T: 2 /* ScalarType.FLOAT */ },
    { no: 26, name: "participant_signal_connected", kind: "scalar", T: 4 /* ScalarType.UINT64 */ },
    { no: 27, name: "participant_signal_connected_per_sec", kind: "scalar", T: 2 /* ScalarType.FLOAT */ },
    { no: 44, name: "participant_rtc_connected", kind: "scalar", T: 4 /* ScalarType.UINT64 */ },
    { no: 45, name: "participant_rtc_connected_per_sec", kind: "scalar", T: 2 /* ScalarType.FLOAT */ },
    { no: 46, name: "participant_rtc_init", kind: "scalar", T: 4 /* ScalarType.UINT64 */ },
    { no: 47, name: "participant_rtc_init_per_sec", kind: "scalar", T: 2 /* ScalarType.FLOAT */ },
    { no: 48, name: "forward_latency", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 49, name: "forward_jitter", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 50, name: "rates", kind: "message", T: NodeStatsRate, repeated: true },
  ],
);

/**
 * rates of different node stats (per second)
 *
 * @generated from message livekit.NodeStatsRate
 */
export const NodeStatsRate = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.NodeStatsRate",
  () => [
    { no: 1, name: "started_at", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
    { no: 2, name: "ended_at", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
    { no: 3, name: "duration", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
    { no: 4, name: "track_publish_attempts", kind: "scalar", T: 2 /* ScalarType.FLOAT */ },
    { no: 5, name: "track_publish_success", kind: "scalar", T: 2 /* ScalarType.FLOAT */ },
    { no: 6, name: "track_subscribe_attempts", kind: "scalar", T: 2 /* ScalarType.FLOAT */ },
    { no: 7, name: "track_subscribe_success", kind: "scalar", T: 2 /* ScalarType.FLOAT */ },
    { no: 8, name: "bytes_in", kind: "scalar", T: 2 /* ScalarType.FLOAT */ },
    { no: 9, name: "bytes_out", kind: "scalar", T: 2 /* ScalarType.FLOAT */ },
    { no: 10, name: "packets_in", kind: "scalar", T: 2 /* ScalarType.FLOAT */ },
    { no: 11, name: "packets_out", kind: "scalar", T: 2 /* ScalarType.FLOAT */ },
    { no: 12, name: "nack_total", kind: "scalar", T: 2 /* ScalarType.FLOAT */ },
    { no: 13, name: "sys_packets_out", kind: "scalar", T: 2 /* ScalarType.FLOAT */ },
    { no: 14, name: "sys_packets_dropped", kind: "scalar", T: 2 /* ScalarType.FLOAT */ },
    { no: 15, name: "retransmit_bytes_out", kind: "scalar", T: 2 /* ScalarType.FLOAT */ },
    { no: 16, name: "retransmit_packets_out", kind: "scalar", T: 2 /* ScalarType.FLOAT */ },
    { no: 17, name: "participant_signal_connected", kind: "scalar", T: 2 /* ScalarType.FLOAT */ },
    { no: 18, name: "participant_rtc_connected", kind: "scalar", T: 2 /* ScalarType.FLOAT */ },
    { no: 19, name: "participant_rtc_init", kind: "scalar", T: 2 /* ScalarType.FLOAT */ },
    { no: 20, name: "cpu_load", kind: "scalar", T: 2 /* ScalarType.FLOAT */ },
    { no: 21, name: "memory_load", kind: "scalar", T: 2 /* ScalarType.FLOAT */ },
    { no: 22, name: "memory_used", kind: "scalar", T: 2 /* ScalarType.FLOAT */ },
    { no: 23, name: "memory_total", kind: "scalar", T: 2 /* ScalarType.FLOAT */ },
  ],
);

/**
 * @generated from message livekit.StartSession
 */
export const StartSession = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.StartSession",
  () => [
    { no: 1, name: "room_name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "identity", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "connection_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "reconnect", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 9, name: "auto_subscribe", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 10, name: "hidden", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 11, name: "client", kind: "message", T: ClientInfo },
    { no: 12, name: "recorder", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 13, name: "name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 14, name: "grants_json", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 15, name: "adaptive_stream", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 16, name: "participant_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 17, name: "reconnect_reason", kind: "enum", T: proto3.getEnumType(ReconnectReason) },
    { no: 18, name: "subscriber_allow_pause", kind: "scalar", T: 8 /* ScalarType.BOOL */, opt: true },
    { no: 19, name: "disable_ice_lite", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 20, name: "create_room", kind: "message", T: CreateRoomRequest },
  ],
);

/**
 * room info that should not be returned to clients
 *
 * @generated from message livekit.RoomInternal
 */
export const RoomInternal = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.RoomInternal",
  () => [
    { no: 1, name: "track_egress", kind: "message", T: AutoTrackEgress },
    { no: 2, name: "participant_egress", kind: "message", T: AutoParticipantEgress },
    { no: 3, name: "playout_delay", kind: "message", T: PlayoutDelay },
    { no: 5, name: "agent_dispatches", kind: "message", T: RoomAgentDispatch, repeated: true },
    { no: 4, name: "sync_streams", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 6, name: "replay_enabled", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
  ],
);

/**
 * @generated from message livekit.ICEConfig
 */
export const ICEConfig = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.ICEConfig",
  () => [
    { no: 1, name: "preference_subscriber", kind: "enum", T: proto3.getEnumType(ICECandidateType) },
    { no: 2, name: "preference_publisher", kind: "enum", T: proto3.getEnumType(ICECandidateType) },
  ],
);

