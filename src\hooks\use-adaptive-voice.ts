'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { useIsMobile } from '@/hooks/use-mobile';
import { HTTPVoiceService, TTSOptions, STTOptions } from '@/lib/http-voice-service';
import { MobileVoiceService, MobileTTSOptions, MobileSTTOptions } from '@/lib/mobile-voice-service';

export interface UseAdaptiveVoiceOptions {
  autoConnect?: boolean;
  language?: string;
}

export interface UseAdaptiveVoiceReturn {
  // Connection state
  isConnected: boolean;
  isConnecting: boolean;
  
  // Recording state
  isRecording: boolean;
  
  // Functions
  connect: () => Promise<void>;
  disconnect: () => Promise<void>;
  startRecording: (options?: STTOptions | MobileSTTOptions) => Promise<void>;
  stopRecording: () => Promise<void>;
  synthesizeSpeech: (text: string, options?: Partial<TTSOptions | MobileTTSOptions>) => Promise<void>;
  
  // Events
  transcription: string;
  isTranscriptionFinal: boolean;
  error: Error | null;
  
  // Mobile-specific
  isMobile: boolean;
  userInteracted: boolean;
}

export function useAdaptiveVoice(options: UseAdaptiveVoiceOptions = {}): UseAdaptiveVoiceReturn {
  const isMobile = useIsMobile();
  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [transcription, setTranscription] = useState('');
  const [isTranscriptionFinal, setIsTranscriptionFinal] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [userInteracted, setUserInteracted] = useState(false);

  const httpServiceRef = useRef<HTTPVoiceService | null>(null);
  const mobileServiceRef = useRef<MobileVoiceService | null>(null);

  // Initialize the appropriate service based on device type
  useEffect(() => {
    const initializeService = async () => {
      if (typeof window === 'undefined') return;

      setIsConnecting(true);
      setError(null);

      try {
        const callbacks = {
          onConnectionStateChange: (state: boolean | 'disconnected' | 'connecting' | 'connected') => {
            const connected = state === true || state === 'connected';
            setIsConnected(connected);
            if (!connected) setIsConnecting(false);
          },
          onTranscription: (text: string, isFinal: boolean) => {
            setTranscription(text);
            setIsTranscriptionFinal(isFinal);
          },
          onAudioReceived: (audioUrl: string) => {
            console.log('Audio received:', audioUrl);
          },
          onError: (err: Error) => {
            console.error('Voice service error:', err);
            setError(err);
            setIsRecording(false);
          },
        };

        if (isMobile) {
          // Use mobile-optimized service
          mobileServiceRef.current = new MobileVoiceService(callbacks);
          if (options.language) {
            mobileServiceRef.current.setLanguage(options.language);
          }
          
          // Check for user interaction
          const checkUserInteraction = () => {
            if (mobileServiceRef.current?.hasUserInteracted) {
              setUserInteracted(true);
            }
          };
          
          const interval = setInterval(checkUserInteraction, 1000);
          setTimeout(() => clearInterval(interval), 10000);
          
        } else {
          // Use HTTP service for desktop
          httpServiceRef.current = new HTTPVoiceService(callbacks);
          if (options.autoConnect) {
            await httpServiceRef.current.initialize();
          }
        }

        setIsConnecting(false);
      } catch (err) {
        console.error('Failed to initialize voice service:', err);
        setError(err as Error);
        setIsConnecting(false);
      }
    };

    initializeService();

    return () => {
      // Cleanup
      if (httpServiceRef.current) {
        httpServiceRef.current.disconnect();
      }
      if (mobileServiceRef.current) {
        mobileServiceRef.current.disconnect();
      }
    };
  }, [isMobile, options.autoConnect, options.language]);

  const connect = useCallback(async () => {
    if (isConnected || isConnecting) return;

    setIsConnecting(true);
    setError(null);

    try {
      if (isMobile && mobileServiceRef.current) {
        await mobileServiceRef.current.connect();
      } else if (httpServiceRef.current) {
        await httpServiceRef.current.initialize();
      }
    } catch (err) {
      setError(err as Error);
      throw err;
    } finally {
      setIsConnecting(false);
    }
  }, [isConnected, isConnecting, isMobile]);

  const disconnect = useCallback(async () => {
    try {
      if (isMobile && mobileServiceRef.current) {
        await mobileServiceRef.current.disconnect();
      } else if (httpServiceRef.current) {
        await httpServiceRef.current.disconnect();
      }
      setIsConnected(false);
      setIsRecording(false);
    } catch (err) {
      setError(err as Error);
    }
  }, [isMobile]);

  const startRecording = useCallback(async (recordingOptions?: STTOptions | MobileSTTOptions) => {
    if (!isConnected || isRecording) return;

    try {
      setError(null);
      setTranscription('');
      setIsTranscriptionFinal(false);

      const defaultOptions = {
        language: options.language || 'en-US',
        continuous: false,
        interimResults: true,
        ...recordingOptions,
      };

      if (isMobile && mobileServiceRef.current) {
        await mobileServiceRef.current.startRecording(defaultOptions);
      } else if (httpServiceRef.current) {
        await httpServiceRef.current.startRecording(defaultOptions);
      }
      
      setIsRecording(true);
    } catch (err) {
      setError(err as Error);
      throw err;
    }
  }, [isConnected, isRecording, isMobile, options.language]);

  const stopRecording = useCallback(async () => {
    if (!isRecording) return;

    try {
      if (isMobile && mobileServiceRef.current) {
        await mobileServiceRef.current.stopRecording();
      } else if (httpServiceRef.current) {
        await httpServiceRef.current.stopRecording();
      }
      setIsRecording(false);
    } catch (err) {
      setError(err as Error);
    }
  }, [isRecording, isMobile]);

  const synthesizeSpeech = useCallback(async (text: string, ttsOptions?: Partial<TTSOptions | MobileTTSOptions>) => {
    if (!isConnected) {
      throw new Error('Voice service not connected');
    }

    try {
      setError(null);

      const speechOptions = {
        text,
        language: options.language || 'en-US',
        voice: 'alloy',
        ...ttsOptions,
      };

      if (isMobile && mobileServiceRef.current) {
        await mobileServiceRef.current.synthesizeSpeech(speechOptions);
      } else if (httpServiceRef.current) {
        await httpServiceRef.current.synthesizeSpeech(speechOptions);
      }
    } catch (err) {
      setError(err as Error);
      throw err;
    }
  }, [isConnected, isMobile, options.language]);

  return {
    isConnected,
    isConnecting,
    isRecording,
    connect,
    disconnect,
    startRecording,
    stopRecording,
    synthesizeSpeech,
    transcription,
    isTranscriptionFinal,
    error,
    isMobile,
    userInteracted,
  };
}
