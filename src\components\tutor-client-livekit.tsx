"use client"

import * as React from "react"
import { useActionState } from "react"
import { Send, Mic, Volume2, Play } from "lucide-react"

import { getTutorResponse, buildLanguageCurriculum } from "@/lib/actions"
import { useUserData } from "@/context/user-data-context"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar"
import { TutorEmptyState } from "@/components/ui/empty-state"
import { BotAvatarFallback, UserAvatarWithInitials } from "@/components/ui/avatar-fallbacks"
import { TypingIndicator, TutorLoadingState } from "@/components/ui/loading-states"
import { useAuth } from "@/context/auth-context"
import { useAdaptiveVoice } from "@/hooks/use-adaptive-voice"
import { useTutorFormState } from "@/context/form-state-context"

interface Message {
  id: string
  role: "user" | "bot"
  text: string
  name: string
}

// Language code mapping for LiveKit STT
const getLanguageCode = (language: string): string => {
  const languageMap: Record<string, string> = {
    'Spanish': 'es-ES',
    'French': 'fr-FR',
    'German': 'de-DE',
    'Italian': 'it-IT',
    'Portuguese': 'pt-PT',
    'Japanese': 'ja-JP',
    'Korean': 'ko-KR',
    'Chinese': 'zh-CN',
  }
  return languageMap[language] || 'en-US'
}

export function TutorClientLiveKit() {
  const { user } = useAuth()
  const { addPoints, updateStreak } = useUserData()
  const formRef = React.useRef<HTMLFormElement>(null)
  const inputRef = React.useRef<HTMLInputElement>(null)
  const [messages, setMessages] = React.useState<Message[]>([])

  // Persistent form state
  const { language, difficulty, setLanguage, setDifficulty } = useTutorFormState()
  const [isChatting, setIsChatting] = React.useState(false)
  const [isStarting, setIsStarting] = React.useState(false)
  const [isTyping, setIsTyping] = React.useState(false)
  const [showCurriculumBuilder, setShowCurriculumBuilder] = React.useState(false)
  const [curriculumState, curriculumDispatch] = React.useActionState(buildLanguageCurriculum, { error: null, data: null })

  // Adaptive voice integration with dynamic language (mobile-optimized)
  const {
    isConnected,
    isConnecting,
    isRecording,
    connect,
    disconnect,
    startRecording,
    stopRecording,
    synthesizeSpeech,
    transcription,
    isTranscriptionFinal,
    error: voiceError,
    isMobile,
    userInteracted,
  } = useAdaptiveVoice({
    autoConnect: false, // Don't auto-connect until tutor session starts
    language: getLanguageCode(language),
  });

  // Update input with transcription
  React.useEffect(() => {
    if (transcription && inputRef.current) {
      inputRef.current.value = transcription;
    }
  }, [transcription]);

  // Handle voice service errors
  React.useEffect(() => {
    if (voiceError) {
      console.error('Voice service error:', voiceError);
    }
  }, [voiceError]);

  const startConversation = async () => {
    setIsStarting(true);
    setMessages([]); // Clear previous messages

    try {
      // Connect to LiveKit when starting conversation
      if (!isConnected) {
        try {
          await connect();
        } catch (error) {
          console.error('Failed to connect voice service:', error);
        }
      }

      const formData = new FormData();
      formData.append('message', '');
      formData.append('history', '');
      formData.append('language', language);
      formData.append('difficulty', difficulty);

      const result = await getTutorResponse(formData);

      if (result?.data?.feedback) {
        setMessages([{ id: `bot-${Date.now()}`, role: "bot", text: result.data.feedback, name: "Lin" }]);
        setIsChatting(true);
      } else if (result?.error) {
        console.error('Tutor response error:', result.error);
        setMessages([{ id: `bot-error-${Date.now()}`, role: "bot", text: "Sorry, I couldn't start the conversation. Please try again.", name: "Lin" }]);
      } else {
        setMessages([{ id: `bot-error-${Date.now()}`, role: "bot", text: "Sorry, I couldn't start the conversation. Please try again.", name: "Lin" }]);
      }
    } catch (error) {
      console.error('Error in startConversation:', error);
      setMessages([{ id: `bot-error-${Date.now()}`, role: "bot", text: "Sorry, I couldn't start the conversation. Please try again.", name: "Lin" }]);
    } finally {
      setIsStarting(false);
    }
  }

  const handleAction = async (formData: FormData) => {
    const userMessage = formData.get("message") as string
    if (!userMessage) return

    setMessages((prev) => [...prev, { id: `user-${Date.now()}`, role: "user", text: userMessage, name: "You" }])

    const history = messages.map(m => `${m.name}: ${m.text}`).join('\n');
    formData.append('history', history);
    formData.append('language', language);
    formData.append('difficulty', difficulty);
    
    formRef.current?.reset()
    inputRef.current?.focus()

    // Show typing indicator
    setIsTyping(true)

    const result = await getTutorResponse(formData)

    // Hide typing indicator
    setIsTyping(false)

    if (result?.data?.feedback) {
      setMessages((prev) => [...prev, { id: `bot-${Date.now()}`, role: "bot", text: result.data.feedback, name: "Lin" }])

      // Award points for tutor interaction
      await addPoints(5)
      await updateStreak()
    } else if (result?.error) {
       setMessages((prev) => [...prev, { id: `bot-error-${Date.now()}`, role: "bot", text: "Sorry, I encountered an error. Please try again.", name: "Lin" }])
    }
  }

  const handlePlayAudio = async (text: string) => {
    if (!isConnected) {
      console.warn('Voice service not connected');
      return;
    }

    // Check if mobile user needs to interact first
    if (isMobile && !userInteracted) {
      alert('Please tap anywhere on the screen to enable audio playback.');
      return;
    }

    try {
      await synthesizeSpeech(text, {
        language: getLanguageCode(language),
        voice: 'alloy' // You can customize voice based on language
      });
    } catch (error) {
      console.error('Failed to play audio:', error);
      if (error instanceof Error && error.message.includes('user interaction')) {
        alert('Audio playback requires user interaction. Please tap the screen and try again.');
      }
    }
  }

  const toggleRecording = async () => {
    if (!isConnected) {
      console.warn('Voice service not connected');
      return;
    }

    try {
      if (isRecording) {
        await stopRecording();
      } else {
        await startRecording({ 
          language: getLanguageCode(language),
          continuous: false,
          interimResults: true
        });
      }
    } catch (error) {
      console.error('Recording error:', error);
    }
  }

  // Update language when it changes (LiveKit handles this internally)
  React.useEffect(() => {
    // Language changes are handled by the LiveKit service
    console.log('Language changed to:', getLanguageCode(language));
  }, [language]);

  // Disconnect when component unmounts
  React.useEffect(() => {
    return () => {
      if (isConnected) {
        disconnect();
      }
    };
  }, []);

  if (!isChatting) {
    return (
      <div className="flex flex-col h-full">
        <TutorEmptyState />
        
        <div className="p-6 space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="language">Language</Label>
              <Select value={language} onValueChange={setLanguage}>
                <SelectTrigger>
                  <SelectValue placeholder="Select language" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Spanish">Spanish</SelectItem>
                  <SelectItem value="French">French</SelectItem>
                  <SelectItem value="German">German</SelectItem>
                  <SelectItem value="Italian">Italian</SelectItem>
                  <SelectItem value="Portuguese">Portuguese</SelectItem>
                  <SelectItem value="Japanese">Japanese</SelectItem>
                  <SelectItem value="Korean">Korean</SelectItem>
                  <SelectItem value="Chinese">Chinese</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="difficulty">Difficulty</Label>
              <Select value={difficulty} onValueChange={setDifficulty}>
                <SelectTrigger>
                  <SelectValue placeholder="Select difficulty" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Beginner">Beginner</SelectItem>
                  <SelectItem value="Intermediate">Intermediate</SelectItem>
                  <SelectItem value="Advanced">Advanced</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex flex-col items-center space-y-4">
            <Button 
              onClick={startConversation} 
              disabled={isStarting || isConnecting}
              className="w-full max-w-md"
            >
              {isStarting ? (
                <>
                  <TutorLoadingState />
                  Starting Conversation...
                </>
              ) : (
                <>
                  <Play className="w-4 h-4 mr-2" />
                  Start AI Tutor Session
                </>
              )}
            </Button>

            <Button
              onClick={() => setShowCurriculumBuilder(!showCurriculumBuilder)}
              variant="outline"
              className="w-full"
            >
              {showCurriculumBuilder ? 'Hide' : 'Build'} Learning Curriculum
            </Button>

            <p className="text-sm text-muted-foreground text-center">
              Voice features will be enabled during the session
            </p>
          </div>
        </div>

        {/* Curriculum Builder */}
        {showCurriculumBuilder && (
          <Card className="mt-4">
            <CardHeader>
              <CardTitle>AI-Powered Learning Curriculum Builder</CardTitle>
              <p className="text-sm text-muted-foreground">
                Lin will create a personalized, research-backed curriculum using the latest language learning methods
              </p>
            </CardHeader>
            <CardContent>
              <form action={curriculumDispatch} className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="curr-language">Language</Label>
                    <Select name="language" value={language} onValueChange={setLanguage} required>
                      <SelectTrigger>
                        <SelectValue placeholder="Select language" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Spanish">Spanish</SelectItem>
                        <SelectItem value="French">French</SelectItem>
                        <SelectItem value="German">German</SelectItem>
                        <SelectItem value="Italian">Italian</SelectItem>
                        <SelectItem value="Portuguese">Portuguese</SelectItem>
                        <SelectItem value="Japanese">Japanese</SelectItem>
                        <SelectItem value="Korean">Korean</SelectItem>
                        <SelectItem value="Mandarin Chinese">Mandarin Chinese</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="curr-level">Level</Label>
                    <Select name="level" value={difficulty} onValueChange={setDifficulty} required>
                      <SelectTrigger>
                        <SelectValue placeholder="Select level" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Beginner">Beginner</SelectItem>
                        <SelectItem value="Intermediate">Intermediate</SelectItem>
                        <SelectItem value="Advanced">Advanced</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div>
                  <Label htmlFor="goals">Learning Goals (Optional)</Label>
                  <Textarea
                    name="goals"
                    placeholder="e.g., Business communication, Travel, Academic study..."
                    className="min-h-[80px]"
                  />
                </div>

                <div>
                  <Label htmlFor="timeframe">Timeframe (Optional)</Label>
                  <Input
                    name="timeframe"
                    placeholder="e.g., 3 months, 1 year, intensive course..."
                  />
                </div>

                <Button type="submit" className="w-full">
                  Generate Curriculum with AI
                </Button>
              </form>

              {curriculumState.data && (
                <div className="mt-6 p-4 bg-muted rounded-lg">
                  <div className="flex items-center gap-2 mb-3">
                    <Badge variant="secondary">AI-Generated Curriculum</Badge>
                    <Badge variant="outline">Research-Based</Badge>
                  </div>
                  <div className="prose prose-sm max-w-none">
                    <div className="whitespace-pre-wrap text-sm">
                      {curriculumState.data.content}
                    </div>
                  </div>
                </div>
              )}

              {curriculumState.error && (
                <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                  <p className="text-red-600 text-sm">
                    {typeof curriculumState.error === 'string' ? curriculumState.error : 'Failed to generate curriculum'}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    )
  }

  return (
    <div className="flex flex-col h-full">
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center space-x-4">
          <div className="text-sm">
            <span className="font-medium">{language}</span> • <span className="text-muted-foreground">{difficulty}</span>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <div className="text-xs text-muted-foreground">
            Voice: {isConnecting ? 'Connecting...' : isConnected ? '🟢' : '🔴'}
          </div>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => {
              setIsChatting(false);
              setMessages([]);
              if (isConnected) disconnect();
            }}
          >
            New Session
          </Button>
        </div>
      </div>

      <ScrollArea className="flex-1 p-4">
        <div className="space-y-4">
          {messages.map((message) => (
            <div key={message.id} className="flex items-start gap-3">
              <Avatar className="w-8 h-8 border">
                {message.role === 'user' ? (
                  <UserAvatarWithInitials
                    name={user?.displayName || undefined}
                    identifier={user?.uid || 'user'}
                    className="w-8 h-8"
                  />
                ) : (
                  <BotAvatarFallback className="w-8 h-8" />
                )}
              </Avatar>
              <div className={`flex flex-1 flex-col gap-1 ${message.role === 'user' ? 'items-end' : 'items-start'}`}>
                <p className="text-neutral-500 text-[13px] font-normal leading-normal max-w-[360px]">{message.name}</p>
                <div className="flex items-end gap-2">
                  <p className={`text-base font-normal leading-normal flex max-w-[360px] rounded-xl px-4 py-3 ${message.role === 'user' ? 'bg-black text-neutral-50' : 'bg-[#ededed] text-[#141414]'}`}>
                      {message.text}
                  </p>
                  {message.role === 'bot' && (
                    <Button 
                      variant="ghost" 
                      size="icon" 
                      className="h-8 w-8 text-neutral-500" 
                      onClick={() => handlePlayAudio(message.text)}
                      disabled={!isConnected}
                      title={isConnected ? 'Play audio' : 'Voice service not connected'}
                    >
                        <Volume2 className="h-5 w-5" />
                    </Button>
                  )}
                </div>
              </div>
            </div>
          ))}
          {isTyping && (
            <div className="flex items-start gap-3">
              <Avatar className="w-8 h-8 border">
                <BotAvatarFallback className="w-8 h-8" />
              </Avatar>
              <div className="flex flex-1 flex-col gap-1 items-start">
                <p className="text-neutral-500 text-[13px] font-normal leading-normal max-w-[360px]">Lin</p>
                <div className="bg-[#ededed] rounded-xl px-4 py-3">
                  <TypingIndicator />
                </div>
              </div>
            </div>
          )}
        </div>
      </ScrollArea>

      {/* Voice Status Bar */}
      {transcription && !isTranscriptionFinal && (
        <div className="px-4 py-2 bg-blue-50 border-t">
          <div className="text-xs text-blue-600">
            Listening: "{transcription}"
          </div>
        </div>
      )}

      <form ref={formRef} action={handleAction} className="p-4 border-t">
          <div className="flex w-full items-center space-x-2">
              <Input
                  ref={inputRef}
                  name="message"
                  placeholder={`Type in ${language} or use voice...`}
                  className="flex-1"
                  autoComplete="off"
              />
              <div className="flex items-center gap-1">
                  <Button 
                    type="button" 
                    size="icon" 
                    onClick={toggleRecording} 
                    variant={isRecording ? 'destructive' : 'ghost'} 
                    className="text-neutral-500" 
                    disabled={!isChatting || !isConnected}
                    title={isConnected ? (isRecording ? 'Stop recording' : 'Start recording') : 'Voice service not connected'}
                  >
                      <Mic className="h-5 w-5" />
                  </Button>
                  <Button type="submit" size="icon" variant="ghost" className="text-neutral-500" disabled={!isChatting}>
                      <Send className="h-5 w-5" />
                  </Button>
              </div>
          </div>

          {/* Mobile audio interaction prompt */}
          {isMobile && !userInteracted && (
            <div className="text-xs text-amber-600 bg-amber-50 p-2 rounded border border-amber-200 mt-2">
              📱 Tap anywhere to enable audio features
            </div>
          )}

          {/* Voice service status */}
          {voiceError && (
            <div className="text-xs text-red-600 bg-red-50 p-2 rounded border border-red-200 mt-2">
              Voice error: {voiceError.message}
            </div>
          )}
      </form>
    </div>
  )
}
