# **App Name**: LingoLens

## Core Features:

- AI Conversation Practice: Multi-Agent System with 6 specialized AI tutors (Conversation, Grammar, Vocabulary, Pronunciation, Cultural, Assessment). Provides intelligent recommendations, real-time feedback, and supports 9 languages with difficulty adaptation and CEFR-based proficiency levels.
- Advanced Grammar System: AI-Powered Explanations with context-aware grammar assistance and examples. Includes interactive learning with real-time grammar checking and corrections, multiple language support, and visual examples.
- Comprehensive Vocabulary Builder: AI-Generated Flashcards with topic-specific vocabulary cards, spaced repetition system, audio pronunciation, two study modes (Browse and Flashcard), topic categories, and progress tracking.
- Real-Time Audio Integration: LiveKit Integration for professional-grade real-time audio processing. Includes speech-to-text, text-to-speech, multiple input modes, audio visualization, and multi-language voice support.
- Multi-Provider AI System: Multi-Provider AI System with 6 AI Providers (Google Gemini, OpenAI GPT, Anthropic Claude, Groq, OpenRouter, DeepSeek). Features smart fallback system, provider optimization, and a unified API.
- Cultural Intelligence & Language Features: Language-Specific Recommendations and Feature Detection for Japanese, Chinese, Korean, Russian, and Spanish. Focuses on cultural context, honorifics, tones, complex writing systems, and grammatical cases.
- Progress Tracking & Analytics: Comprehensive Dashboard for learning progress visualization. Includes performance metrics, achievement system, learning analytics, and session history.
- Modern User Experience: Responsive Design optimized for desktop, tablet, and mobile. Features dark/light mode, accessibility support, clean UI, and smooth animations.

## Style Guidelines:

- Primary color: Deep Indigo (#6666FF), conveying a sense of intellect and technological sophistication.
- Background color: Very light Indigo (#F0F0FF), almost white, provides a clean, distraction-free background to emphasize readability and focus on content.
- Accent color: Soft Violet (#9966FF), brighter than the primary, can highlight key interactive elements and guide user attention effectively.
- Font: 'Inter', a sans-serif font suitable for both headlines and body text, offers a clean, modern look and excellent readability on screen.
- Use minimalist icons for easy recognition of common actions such as settings or help.
- Design should have a clean, professional appearance for web, tablet and mobile use.
- Dark mode: black background and light grey text.
- Dark mode toggle