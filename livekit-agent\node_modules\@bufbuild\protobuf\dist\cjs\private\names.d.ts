import type { DescEnum, DescEnumValue, DescExtension, DescField, DescMessage, DescService } from "../descriptor-set.js";
import type { DescMethod, DescOneof } from "../descriptor-set.js";
/**
 * Returns the name of a protobuf element in generated code.
 *
 * Field names - including oneofs - are converted to lowerCamelCase. For
 * messages, enumerations and services, the package name is stripped from
 * the type name. For nested messages and enumerations, the names are joined
 * with an underscore. For methods, the first character is made lowercase.
 */
export declare function localName(desc: DescEnum | DescEnumValue | DescMessage | DescExtension | DescOneof | DescField | DescService | DescMethod): string;
/**
 * Returns the name of a field in generated code.
 */
export declare function localFieldName(protoName: string, inOneof: boolean): string;
/**
 * Returns the name of a oneof group in generated code.
 */
export declare function localOneofName(protoName: string): string;
/**
 * Returns the JSON name for a protobuf field, exactly like protoc does.
 */
export declare const fieldJsonName: typeof protoCamelCase;
/**
 * Finds a prefix shared by enum values, for example `MY_ENUM_` for
 * `enum MyEnum {MY_ENUM_A=0; MY_ENUM_B=1;}`.
 */
export declare function findEnumSharedPrefix(enumName: string, valueNames: string[]): string | undefined;
/**
 * Converts snake_case to protoCamelCase according to the convention
 * used by protoc to convert a field name to a JSON name.
 */
declare function protoCamelCase(snakeCase: string): string;
/**
 * Names that cannot be used for object properties because they are reserved
 * by built-in JavaScript properties.
 */
export declare const safeObjectProperty: (name: string) => string;
/**
 * Names that can be used for identifiers or class properties
 */
export declare const safeIdentifier: (name: string) => string;
export {};
