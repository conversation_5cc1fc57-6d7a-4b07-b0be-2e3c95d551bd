// Copyright 2023 LiveKit, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// @generated by protoc-gen-es v1.10.1 with parameter "target=dts+js"
// @generated from file livekit_analytics.proto (package livekit, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { proto3, Timestamp } from "@bufbuild/protobuf";
import { ClientInfo, ParticipantInfo, ParticipantInfo_State, ReconnectReason, Room, RTPStats, TrackInfo, VideoQuality } from "./livekit_models_pb.js";
import { EgressInfo } from "./livekit_egress_pb.js";
import { IngressInfo } from "./livekit_ingress_pb.js";
import { SIPCallInfo, SIPDispatchRuleInfo, SIPInboundTrunkInfo, SIPOutboundTrunkInfo, SIPTransferInfo } from "./livekit_sip_pb.js";
import { CreateRoomRequest, DeleteRoomRequest, ListParticipantsRequest, ListRoomsRequest, MuteRoomTrackRequest, RoomParticipantIdentity, SendDataRequest, UpdateParticipantRequest, UpdateRoomMetadataRequest, UpdateSubscriptionsRequest } from "./livekit_room_pb.js";

/**
 * @generated from enum livekit.StreamType
 */
export const StreamType = /*@__PURE__*/ proto3.makeEnum(
  "livekit.StreamType",
  [
    {no: 0, name: "UPSTREAM"},
    {no: 1, name: "DOWNSTREAM"},
  ],
);

/**
 * @generated from enum livekit.AnalyticsEventType
 */
export const AnalyticsEventType = /*@__PURE__*/ proto3.makeEnum(
  "livekit.AnalyticsEventType",
  [
    {no: 0, name: "ROOM_CREATED"},
    {no: 1, name: "ROOM_ENDED"},
    {no: 2, name: "PARTICIPANT_JOINED"},
    {no: 3, name: "PARTICIPANT_LEFT"},
    {no: 4, name: "TRACK_PUBLISHED"},
    {no: 20, name: "TRACK_PUBLISH_REQUESTED"},
    {no: 5, name: "TRACK_UNPUBLISHED"},
    {no: 6, name: "TRACK_SUBSCRIBED"},
    {no: 21, name: "TRACK_SUBSCRIBE_REQUESTED"},
    {no: 25, name: "TRACK_SUBSCRIBE_FAILED"},
    {no: 7, name: "TRACK_UNSUBSCRIBED"},
    {no: 10, name: "TRACK_PUBLISHED_UPDATE"},
    {no: 23, name: "TRACK_MUTED"},
    {no: 24, name: "TRACK_UNMUTED"},
    {no: 26, name: "TRACK_PUBLISH_STATS"},
    {no: 27, name: "TRACK_SUBSCRIBE_STATS"},
    {no: 11, name: "PARTICIPANT_ACTIVE"},
    {no: 22, name: "PARTICIPANT_RESUMED"},
    {no: 12, name: "EGRESS_STARTED"},
    {no: 13, name: "EGRESS_ENDED"},
    {no: 28, name: "EGRESS_UPDATED"},
    {no: 14, name: "TRACK_MAX_SUBSCRIBED_VIDEO_QUALITY"},
    {no: 15, name: "RECONNECTED"},
    {no: 18, name: "INGRESS_CREATED"},
    {no: 19, name: "INGRESS_DELETED"},
    {no: 16, name: "INGRESS_STARTED"},
    {no: 17, name: "INGRESS_ENDED"},
    {no: 29, name: "INGRESS_UPDATED"},
    {no: 30, name: "SIP_INBOUND_TRUNK_CREATED"},
    {no: 31, name: "SIP_INBOUND_TRUNK_DELETED"},
    {no: 32, name: "SIP_OUTBOUND_TRUNK_CREATED"},
    {no: 33, name: "SIP_OUTBOUND_TRUNK_DELETED"},
    {no: 34, name: "SIP_DISPATCH_RULE_CREATED"},
    {no: 35, name: "SIP_DISPATCH_RULE_DELETED"},
    {no: 36, name: "SIP_PARTICIPANT_CREATED"},
    {no: 37, name: "SIP_CALL_INCOMING"},
    {no: 38, name: "SIP_CALL_STARTED"},
    {no: 39, name: "SIP_CALL_ENDED"},
    {no: 43, name: "SIP_TRANSFER_REQUESTED"},
    {no: 44, name: "SIP_TRANSFER_COMPLETE"},
    {no: 40, name: "REPORT"},
    {no: 41, name: "API_CALL"},
    {no: 42, name: "WEBHOOK"},
  ],
);

/**
 * @generated from message livekit.AnalyticsVideoLayer
 */
export const AnalyticsVideoLayer = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.AnalyticsVideoLayer",
  () => [
    { no: 1, name: "layer", kind: "scalar", T: 5 /* ScalarType.INT32 */ },
    { no: 2, name: "packets", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 3, name: "bytes", kind: "scalar", T: 4 /* ScalarType.UINT64 */ },
    { no: 4, name: "frames", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
  ],
);

/**
 * @generated from message livekit.AnalyticsStream
 */
export const AnalyticsStream = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.AnalyticsStream",
  () => [
    { no: 1, name: "ssrc", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 2, name: "primary_packets", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 3, name: "primary_bytes", kind: "scalar", T: 4 /* ScalarType.UINT64 */ },
    { no: 4, name: "retransmit_packets", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 5, name: "retransmit_bytes", kind: "scalar", T: 4 /* ScalarType.UINT64 */ },
    { no: 6, name: "padding_packets", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 7, name: "padding_bytes", kind: "scalar", T: 4 /* ScalarType.UINT64 */ },
    { no: 8, name: "packets_lost", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 9, name: "frames", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 10, name: "rtt", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 11, name: "jitter", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 12, name: "nacks", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 13, name: "plis", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 14, name: "firs", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 15, name: "video_layers", kind: "message", T: AnalyticsVideoLayer, repeated: true },
    { no: 17, name: "start_time", kind: "message", T: Timestamp },
    { no: 18, name: "end_time", kind: "message", T: Timestamp },
    { no: 19, name: "packets_out_of_order", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
  ],
);

/**
 * @generated from message livekit.AnalyticsStat
 */
export const AnalyticsStat = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.AnalyticsStat",
  () => [
    { no: 14, name: "id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 1, name: "analytics_key", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "kind", kind: "enum", T: proto3.getEnumType(StreamType) },
    { no: 3, name: "time_stamp", kind: "message", T: Timestamp },
    { no: 4, name: "node", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 5, name: "room_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 6, name: "room_name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 7, name: "participant_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 8, name: "track_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 9, name: "score", kind: "scalar", T: 2 /* ScalarType.FLOAT */ },
    { no: 10, name: "streams", kind: "message", T: AnalyticsStream, repeated: true },
    { no: 11, name: "mime", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 12, name: "min_score", kind: "scalar", T: 2 /* ScalarType.FLOAT */ },
    { no: 13, name: "median_score", kind: "scalar", T: 2 /* ScalarType.FLOAT */ },
  ],
);

/**
 * @generated from message livekit.AnalyticsStats
 */
export const AnalyticsStats = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.AnalyticsStats",
  () => [
    { no: 1, name: "stats", kind: "message", T: AnalyticsStat, repeated: true },
  ],
);

/**
 * @generated from message livekit.AnalyticsClientMeta
 */
export const AnalyticsClientMeta = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.AnalyticsClientMeta",
  () => [
    { no: 1, name: "region", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "node", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "client_addr", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "client_connect_time", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 5, name: "connection_type", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 6, name: "reconnect_reason", kind: "enum", T: proto3.getEnumType(ReconnectReason) },
    { no: 7, name: "geo_hash", kind: "scalar", T: 9 /* ScalarType.STRING */, opt: true },
    { no: 8, name: "country", kind: "scalar", T: 9 /* ScalarType.STRING */, opt: true },
    { no: 9, name: "isp_asn", kind: "scalar", T: 13 /* ScalarType.UINT32 */, opt: true },
  ],
);

/**
 * @generated from message livekit.AnalyticsEvent
 */
export const AnalyticsEvent = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.AnalyticsEvent",
  () => [
    { no: 25, name: "id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 1, name: "type", kind: "enum", T: proto3.getEnumType(AnalyticsEventType) },
    { no: 2, name: "timestamp", kind: "message", T: Timestamp },
    { no: 3, name: "room_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "room", kind: "message", T: Room },
    { no: 5, name: "participant_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 6, name: "participant", kind: "message", T: ParticipantInfo },
    { no: 7, name: "track_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 8, name: "track", kind: "message", T: TrackInfo },
    { no: 10, name: "analytics_key", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 11, name: "client_info", kind: "message", T: ClientInfo },
    { no: 12, name: "client_meta", kind: "message", T: AnalyticsClientMeta },
    { no: 13, name: "egress_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 19, name: "ingress_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 14, name: "max_subscribed_video_quality", kind: "enum", T: proto3.getEnumType(VideoQuality) },
    { no: 15, name: "publisher", kind: "message", T: ParticipantInfo },
    { no: 16, name: "mime", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 17, name: "egress", kind: "message", T: EgressInfo },
    { no: 18, name: "ingress", kind: "message", T: IngressInfo },
    { no: 20, name: "error", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 21, name: "rtp_stats", kind: "message", T: RTPStats },
    { no: 22, name: "video_layer", kind: "scalar", T: 5 /* ScalarType.INT32 */ },
    { no: 24, name: "node_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 26, name: "sip_call_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 27, name: "sip_call", kind: "message", T: SIPCallInfo },
    { no: 28, name: "sip_trunk_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 29, name: "sip_inbound_trunk", kind: "message", T: SIPInboundTrunkInfo },
    { no: 30, name: "sip_outbound_trunk", kind: "message", T: SIPOutboundTrunkInfo },
    { no: 31, name: "sip_dispatch_rule_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 32, name: "sip_dispatch_rule", kind: "message", T: SIPDispatchRuleInfo },
    { no: 36, name: "sip_transfer", kind: "message", T: SIPTransferInfo },
    { no: 33, name: "report", kind: "message", T: ReportInfo },
    { no: 34, name: "api_call", kind: "message", T: APICallInfo },
    { no: 35, name: "webhook", kind: "message", T: WebhookInfo },
  ],
);

/**
 * @generated from message livekit.AnalyticsEvents
 */
export const AnalyticsEvents = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.AnalyticsEvents",
  () => [
    { no: 1, name: "events", kind: "message", T: AnalyticsEvent, repeated: true },
  ],
);

/**
 * @generated from message livekit.AnalyticsRoomParticipant
 */
export const AnalyticsRoomParticipant = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.AnalyticsRoomParticipant",
  () => [
    { no: 1, name: "id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "identity", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "state", kind: "enum", T: proto3.getEnumType(ParticipantInfo_State) },
    { no: 5, name: "joined_at", kind: "message", T: Timestamp },
  ],
);

/**
 * @generated from message livekit.AnalyticsRoom
 */
export const AnalyticsRoom = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.AnalyticsRoom",
  () => [
    { no: 1, name: "id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 5, name: "project_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "created_at", kind: "message", T: Timestamp },
    { no: 4, name: "participants", kind: "message", T: AnalyticsRoomParticipant, repeated: true },
  ],
);

/**
 * @generated from message livekit.AnalyticsNodeRooms
 */
export const AnalyticsNodeRooms = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.AnalyticsNodeRooms",
  () => [
    { no: 1, name: "node_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "sequence_number", kind: "scalar", T: 4 /* ScalarType.UINT64 */ },
    { no: 3, name: "timestamp", kind: "message", T: Timestamp },
    { no: 4, name: "rooms", kind: "message", T: AnalyticsRoom, repeated: true },
  ],
);

/**
 * @generated from message livekit.ReportInfo
 */
export const ReportInfo = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.ReportInfo",
  () => [
    { no: 1, name: "feature_usage", kind: "message", T: FeatureUsageInfo, oneof: "message" },
  ],
);

/**
 * @generated from message livekit.TimeRange
 */
export const TimeRange = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.TimeRange",
  () => [
    { no: 1, name: "started_at", kind: "message", T: Timestamp },
    { no: 2, name: "ended_at", kind: "message", T: Timestamp },
  ],
);

/**
 * @generated from message livekit.FeatureUsageInfo
 */
export const FeatureUsageInfo = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.FeatureUsageInfo",
  () => [
    { no: 1, name: "feature", kind: "enum", T: proto3.getEnumType(FeatureUsageInfo_Feature) },
    { no: 2, name: "project_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "room_name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "room_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 5, name: "participant_identity", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 6, name: "participant_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 7, name: "track_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 8, name: "time_ranges", kind: "message", T: TimeRange, repeated: true },
  ],
);

/**
 * @generated from enum livekit.FeatureUsageInfo.Feature
 */
export const FeatureUsageInfo_Feature = /*@__PURE__*/ proto3.makeEnum(
  "livekit.FeatureUsageInfo.Feature",
  [
    {no: 0, name: "KRISP_NOISE_CANCELLATION"},
    {no: 1, name: "KRISP_BACKGROUND_VOICE_CANCELLATION"},
  ],
);

/**
 * @generated from message livekit.APICallRequest
 */
export const APICallRequest = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.APICallRequest",
  () => [
    { no: 1, name: "create_room_request", kind: "message", T: CreateRoomRequest, oneof: "message" },
    { no: 2, name: "list_rooms_request", kind: "message", T: ListRoomsRequest, oneof: "message" },
    { no: 3, name: "delete_room_request", kind: "message", T: DeleteRoomRequest, oneof: "message" },
    { no: 4, name: "list_participants_request", kind: "message", T: ListParticipantsRequest, oneof: "message" },
    { no: 5, name: "room_participant_identity", kind: "message", T: RoomParticipantIdentity, oneof: "message" },
    { no: 6, name: "mute_room_track_request", kind: "message", T: MuteRoomTrackRequest, oneof: "message" },
    { no: 7, name: "update_participant_request", kind: "message", T: UpdateParticipantRequest, oneof: "message" },
    { no: 8, name: "update_subscriptions_request", kind: "message", T: UpdateSubscriptionsRequest, oneof: "message" },
    { no: 9, name: "send_data_request", kind: "message", T: SendDataRequest, oneof: "message" },
    { no: 10, name: "update_room_metadata_request", kind: "message", T: UpdateRoomMetadataRequest, oneof: "message" },
  ],
);

/**
 * @generated from message livekit.APICallInfo
 */
export const APICallInfo = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.APICallInfo",
  () => [
    { no: 1, name: "project_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "request", kind: "message", T: APICallRequest },
    { no: 3, name: "service", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "method", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 5, name: "node_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 6, name: "status", kind: "scalar", T: 5 /* ScalarType.INT32 */ },
    { no: 7, name: "twirp_error_code", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 8, name: "twirp_error_message", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 9, name: "room_name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 10, name: "room_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 11, name: "participant_identity", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 12, name: "participant_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 13, name: "track_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 14, name: "started_at", kind: "message", T: Timestamp },
    { no: 15, name: "duration_ns", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
  ],
);

/**
 * @generated from message livekit.WebhookInfo
 */
export const WebhookInfo = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.WebhookInfo",
  () => [
    { no: 1, name: "event_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "event", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "project_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "room_name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 5, name: "room_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 6, name: "participant_identity", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 7, name: "participant_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 8, name: "track_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 9, name: "egress_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 10, name: "ingress_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 11, name: "created_at", kind: "message", T: Timestamp },
    { no: 12, name: "queued_at", kind: "message", T: Timestamp },
    { no: 13, name: "queue_duration_ns", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
    { no: 14, name: "sent_at", kind: "message", T: Timestamp },
    { no: 15, name: "send_duration_ns", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
    { no: 16, name: "url", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 17, name: "num_dropped", kind: "scalar", T: 5 /* ScalarType.INT32 */ },
    { no: 18, name: "is_dropped", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 19, name: "service_status", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 20, name: "service_error_code", kind: "scalar", T: 5 /* ScalarType.INT32 */ },
    { no: 21, name: "service_error", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 22, name: "send_error", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ],
);

