"use strict";
// Copyright 2021-2024 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
Object.defineProperty(exports, "__esModule", { value: true });
exports.BytesValue = exports.StringValue = exports.BoolValue = exports.UInt32Value = exports.Int32Value = exports.UInt64Value = exports.Int64Value = exports.FloatValue = exports.DoubleValue = void 0;
const message_js_1 = require("../../message.js");
const proto3_js_1 = require("../../proto3.js");
const scalar_js_1 = require("../../scalar.js");
const proto_int64_js_1 = require("../../proto-int64.js");
/**
 * Wrapper message for `double`.
 *
 * The JSON representation for `DoubleValue` is JSON number.
 *
 * @generated from message google.protobuf.DoubleValue
 */
class DoubleValue extends message_js_1.Message {
    constructor(data) {
        super();
        /**
         * The double value.
         *
         * @generated from field: double value = 1;
         */
        this.value = 0;
        proto3_js_1.proto3.util.initPartial(data, this);
    }
    toJson(options) {
        return proto3_js_1.proto3.json.writeScalar(scalar_js_1.ScalarType.DOUBLE, this.value, true);
    }
    fromJson(json, options) {
        try {
            this.value = proto3_js_1.proto3.json.readScalar(scalar_js_1.ScalarType.DOUBLE, json);
        }
        catch (e) {
            let m = `cannot decode message google.protobuf.DoubleValue from JSON"`;
            if (e instanceof Error && e.message.length > 0) {
                m += `: ${e.message}`;
            }
            throw new Error(m);
        }
        return this;
    }
    static fromBinary(bytes, options) {
        return new DoubleValue().fromBinary(bytes, options);
    }
    static fromJson(jsonValue, options) {
        return new DoubleValue().fromJson(jsonValue, options);
    }
    static fromJsonString(jsonString, options) {
        return new DoubleValue().fromJsonString(jsonString, options);
    }
    static equals(a, b) {
        return proto3_js_1.proto3.util.equals(DoubleValue, a, b);
    }
}
exports.DoubleValue = DoubleValue;
DoubleValue.runtime = proto3_js_1.proto3;
DoubleValue.typeName = "google.protobuf.DoubleValue";
DoubleValue.fields = proto3_js_1.proto3.util.newFieldList(() => [
    { no: 1, name: "value", kind: "scalar", T: 1 /* ScalarType.DOUBLE */ },
]);
DoubleValue.fieldWrapper = {
    wrapField(value) {
        return new DoubleValue({ value });
    },
    unwrapField(value) {
        return value.value;
    }
};
/**
 * Wrapper message for `float`.
 *
 * The JSON representation for `FloatValue` is JSON number.
 *
 * @generated from message google.protobuf.FloatValue
 */
class FloatValue extends message_js_1.Message {
    constructor(data) {
        super();
        /**
         * The float value.
         *
         * @generated from field: float value = 1;
         */
        this.value = 0;
        proto3_js_1.proto3.util.initPartial(data, this);
    }
    toJson(options) {
        return proto3_js_1.proto3.json.writeScalar(scalar_js_1.ScalarType.FLOAT, this.value, true);
    }
    fromJson(json, options) {
        try {
            this.value = proto3_js_1.proto3.json.readScalar(scalar_js_1.ScalarType.FLOAT, json);
        }
        catch (e) {
            let m = `cannot decode message google.protobuf.FloatValue from JSON"`;
            if (e instanceof Error && e.message.length > 0) {
                m += `: ${e.message}`;
            }
            throw new Error(m);
        }
        return this;
    }
    static fromBinary(bytes, options) {
        return new FloatValue().fromBinary(bytes, options);
    }
    static fromJson(jsonValue, options) {
        return new FloatValue().fromJson(jsonValue, options);
    }
    static fromJsonString(jsonString, options) {
        return new FloatValue().fromJsonString(jsonString, options);
    }
    static equals(a, b) {
        return proto3_js_1.proto3.util.equals(FloatValue, a, b);
    }
}
exports.FloatValue = FloatValue;
FloatValue.runtime = proto3_js_1.proto3;
FloatValue.typeName = "google.protobuf.FloatValue";
FloatValue.fields = proto3_js_1.proto3.util.newFieldList(() => [
    { no: 1, name: "value", kind: "scalar", T: 2 /* ScalarType.FLOAT */ },
]);
FloatValue.fieldWrapper = {
    wrapField(value) {
        return new FloatValue({ value });
    },
    unwrapField(value) {
        return value.value;
    }
};
/**
 * Wrapper message for `int64`.
 *
 * The JSON representation for `Int64Value` is JSON string.
 *
 * @generated from message google.protobuf.Int64Value
 */
class Int64Value extends message_js_1.Message {
    constructor(data) {
        super();
        /**
         * The int64 value.
         *
         * @generated from field: int64 value = 1;
         */
        this.value = proto_int64_js_1.protoInt64.zero;
        proto3_js_1.proto3.util.initPartial(data, this);
    }
    toJson(options) {
        return proto3_js_1.proto3.json.writeScalar(scalar_js_1.ScalarType.INT64, this.value, true);
    }
    fromJson(json, options) {
        try {
            this.value = proto3_js_1.proto3.json.readScalar(scalar_js_1.ScalarType.INT64, json);
        }
        catch (e) {
            let m = `cannot decode message google.protobuf.Int64Value from JSON"`;
            if (e instanceof Error && e.message.length > 0) {
                m += `: ${e.message}`;
            }
            throw new Error(m);
        }
        return this;
    }
    static fromBinary(bytes, options) {
        return new Int64Value().fromBinary(bytes, options);
    }
    static fromJson(jsonValue, options) {
        return new Int64Value().fromJson(jsonValue, options);
    }
    static fromJsonString(jsonString, options) {
        return new Int64Value().fromJsonString(jsonString, options);
    }
    static equals(a, b) {
        return proto3_js_1.proto3.util.equals(Int64Value, a, b);
    }
}
exports.Int64Value = Int64Value;
Int64Value.runtime = proto3_js_1.proto3;
Int64Value.typeName = "google.protobuf.Int64Value";
Int64Value.fields = proto3_js_1.proto3.util.newFieldList(() => [
    { no: 1, name: "value", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
]);
Int64Value.fieldWrapper = {
    wrapField(value) {
        return new Int64Value({ value });
    },
    unwrapField(value) {
        return value.value;
    }
};
/**
 * Wrapper message for `uint64`.
 *
 * The JSON representation for `UInt64Value` is JSON string.
 *
 * @generated from message google.protobuf.UInt64Value
 */
class UInt64Value extends message_js_1.Message {
    constructor(data) {
        super();
        /**
         * The uint64 value.
         *
         * @generated from field: uint64 value = 1;
         */
        this.value = proto_int64_js_1.protoInt64.zero;
        proto3_js_1.proto3.util.initPartial(data, this);
    }
    toJson(options) {
        return proto3_js_1.proto3.json.writeScalar(scalar_js_1.ScalarType.UINT64, this.value, true);
    }
    fromJson(json, options) {
        try {
            this.value = proto3_js_1.proto3.json.readScalar(scalar_js_1.ScalarType.UINT64, json);
        }
        catch (e) {
            let m = `cannot decode message google.protobuf.UInt64Value from JSON"`;
            if (e instanceof Error && e.message.length > 0) {
                m += `: ${e.message}`;
            }
            throw new Error(m);
        }
        return this;
    }
    static fromBinary(bytes, options) {
        return new UInt64Value().fromBinary(bytes, options);
    }
    static fromJson(jsonValue, options) {
        return new UInt64Value().fromJson(jsonValue, options);
    }
    static fromJsonString(jsonString, options) {
        return new UInt64Value().fromJsonString(jsonString, options);
    }
    static equals(a, b) {
        return proto3_js_1.proto3.util.equals(UInt64Value, a, b);
    }
}
exports.UInt64Value = UInt64Value;
UInt64Value.runtime = proto3_js_1.proto3;
UInt64Value.typeName = "google.protobuf.UInt64Value";
UInt64Value.fields = proto3_js_1.proto3.util.newFieldList(() => [
    { no: 1, name: "value", kind: "scalar", T: 4 /* ScalarType.UINT64 */ },
]);
UInt64Value.fieldWrapper = {
    wrapField(value) {
        return new UInt64Value({ value });
    },
    unwrapField(value) {
        return value.value;
    }
};
/**
 * Wrapper message for `int32`.
 *
 * The JSON representation for `Int32Value` is JSON number.
 *
 * @generated from message google.protobuf.Int32Value
 */
class Int32Value extends message_js_1.Message {
    constructor(data) {
        super();
        /**
         * The int32 value.
         *
         * @generated from field: int32 value = 1;
         */
        this.value = 0;
        proto3_js_1.proto3.util.initPartial(data, this);
    }
    toJson(options) {
        return proto3_js_1.proto3.json.writeScalar(scalar_js_1.ScalarType.INT32, this.value, true);
    }
    fromJson(json, options) {
        try {
            this.value = proto3_js_1.proto3.json.readScalar(scalar_js_1.ScalarType.INT32, json);
        }
        catch (e) {
            let m = `cannot decode message google.protobuf.Int32Value from JSON"`;
            if (e instanceof Error && e.message.length > 0) {
                m += `: ${e.message}`;
            }
            throw new Error(m);
        }
        return this;
    }
    static fromBinary(bytes, options) {
        return new Int32Value().fromBinary(bytes, options);
    }
    static fromJson(jsonValue, options) {
        return new Int32Value().fromJson(jsonValue, options);
    }
    static fromJsonString(jsonString, options) {
        return new Int32Value().fromJsonString(jsonString, options);
    }
    static equals(a, b) {
        return proto3_js_1.proto3.util.equals(Int32Value, a, b);
    }
}
exports.Int32Value = Int32Value;
Int32Value.runtime = proto3_js_1.proto3;
Int32Value.typeName = "google.protobuf.Int32Value";
Int32Value.fields = proto3_js_1.proto3.util.newFieldList(() => [
    { no: 1, name: "value", kind: "scalar", T: 5 /* ScalarType.INT32 */ },
]);
Int32Value.fieldWrapper = {
    wrapField(value) {
        return new Int32Value({ value });
    },
    unwrapField(value) {
        return value.value;
    }
};
/**
 * Wrapper message for `uint32`.
 *
 * The JSON representation for `UInt32Value` is JSON number.
 *
 * @generated from message google.protobuf.UInt32Value
 */
class UInt32Value extends message_js_1.Message {
    constructor(data) {
        super();
        /**
         * The uint32 value.
         *
         * @generated from field: uint32 value = 1;
         */
        this.value = 0;
        proto3_js_1.proto3.util.initPartial(data, this);
    }
    toJson(options) {
        return proto3_js_1.proto3.json.writeScalar(scalar_js_1.ScalarType.UINT32, this.value, true);
    }
    fromJson(json, options) {
        try {
            this.value = proto3_js_1.proto3.json.readScalar(scalar_js_1.ScalarType.UINT32, json);
        }
        catch (e) {
            let m = `cannot decode message google.protobuf.UInt32Value from JSON"`;
            if (e instanceof Error && e.message.length > 0) {
                m += `: ${e.message}`;
            }
            throw new Error(m);
        }
        return this;
    }
    static fromBinary(bytes, options) {
        return new UInt32Value().fromBinary(bytes, options);
    }
    static fromJson(jsonValue, options) {
        return new UInt32Value().fromJson(jsonValue, options);
    }
    static fromJsonString(jsonString, options) {
        return new UInt32Value().fromJsonString(jsonString, options);
    }
    static equals(a, b) {
        return proto3_js_1.proto3.util.equals(UInt32Value, a, b);
    }
}
exports.UInt32Value = UInt32Value;
UInt32Value.runtime = proto3_js_1.proto3;
UInt32Value.typeName = "google.protobuf.UInt32Value";
UInt32Value.fields = proto3_js_1.proto3.util.newFieldList(() => [
    { no: 1, name: "value", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
]);
UInt32Value.fieldWrapper = {
    wrapField(value) {
        return new UInt32Value({ value });
    },
    unwrapField(value) {
        return value.value;
    }
};
/**
 * Wrapper message for `bool`.
 *
 * The JSON representation for `BoolValue` is JSON `true` and `false`.
 *
 * @generated from message google.protobuf.BoolValue
 */
class BoolValue extends message_js_1.Message {
    constructor(data) {
        super();
        /**
         * The bool value.
         *
         * @generated from field: bool value = 1;
         */
        this.value = false;
        proto3_js_1.proto3.util.initPartial(data, this);
    }
    toJson(options) {
        return proto3_js_1.proto3.json.writeScalar(scalar_js_1.ScalarType.BOOL, this.value, true);
    }
    fromJson(json, options) {
        try {
            this.value = proto3_js_1.proto3.json.readScalar(scalar_js_1.ScalarType.BOOL, json);
        }
        catch (e) {
            let m = `cannot decode message google.protobuf.BoolValue from JSON"`;
            if (e instanceof Error && e.message.length > 0) {
                m += `: ${e.message}`;
            }
            throw new Error(m);
        }
        return this;
    }
    static fromBinary(bytes, options) {
        return new BoolValue().fromBinary(bytes, options);
    }
    static fromJson(jsonValue, options) {
        return new BoolValue().fromJson(jsonValue, options);
    }
    static fromJsonString(jsonString, options) {
        return new BoolValue().fromJsonString(jsonString, options);
    }
    static equals(a, b) {
        return proto3_js_1.proto3.util.equals(BoolValue, a, b);
    }
}
exports.BoolValue = BoolValue;
BoolValue.runtime = proto3_js_1.proto3;
BoolValue.typeName = "google.protobuf.BoolValue";
BoolValue.fields = proto3_js_1.proto3.util.newFieldList(() => [
    { no: 1, name: "value", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
]);
BoolValue.fieldWrapper = {
    wrapField(value) {
        return new BoolValue({ value });
    },
    unwrapField(value) {
        return value.value;
    }
};
/**
 * Wrapper message for `string`.
 *
 * The JSON representation for `StringValue` is JSON string.
 *
 * @generated from message google.protobuf.StringValue
 */
class StringValue extends message_js_1.Message {
    constructor(data) {
        super();
        /**
         * The string value.
         *
         * @generated from field: string value = 1;
         */
        this.value = "";
        proto3_js_1.proto3.util.initPartial(data, this);
    }
    toJson(options) {
        return proto3_js_1.proto3.json.writeScalar(scalar_js_1.ScalarType.STRING, this.value, true);
    }
    fromJson(json, options) {
        try {
            this.value = proto3_js_1.proto3.json.readScalar(scalar_js_1.ScalarType.STRING, json);
        }
        catch (e) {
            let m = `cannot decode message google.protobuf.StringValue from JSON"`;
            if (e instanceof Error && e.message.length > 0) {
                m += `: ${e.message}`;
            }
            throw new Error(m);
        }
        return this;
    }
    static fromBinary(bytes, options) {
        return new StringValue().fromBinary(bytes, options);
    }
    static fromJson(jsonValue, options) {
        return new StringValue().fromJson(jsonValue, options);
    }
    static fromJsonString(jsonString, options) {
        return new StringValue().fromJsonString(jsonString, options);
    }
    static equals(a, b) {
        return proto3_js_1.proto3.util.equals(StringValue, a, b);
    }
}
exports.StringValue = StringValue;
StringValue.runtime = proto3_js_1.proto3;
StringValue.typeName = "google.protobuf.StringValue";
StringValue.fields = proto3_js_1.proto3.util.newFieldList(() => [
    { no: 1, name: "value", kind: "scalar", T: 9 /* ScalarType.STRING */ },
]);
StringValue.fieldWrapper = {
    wrapField(value) {
        return new StringValue({ value });
    },
    unwrapField(value) {
        return value.value;
    }
};
/**
 * Wrapper message for `bytes`.
 *
 * The JSON representation for `BytesValue` is JSON string.
 *
 * @generated from message google.protobuf.BytesValue
 */
class BytesValue extends message_js_1.Message {
    constructor(data) {
        super();
        /**
         * The bytes value.
         *
         * @generated from field: bytes value = 1;
         */
        this.value = new Uint8Array(0);
        proto3_js_1.proto3.util.initPartial(data, this);
    }
    toJson(options) {
        return proto3_js_1.proto3.json.writeScalar(scalar_js_1.ScalarType.BYTES, this.value, true);
    }
    fromJson(json, options) {
        try {
            this.value = proto3_js_1.proto3.json.readScalar(scalar_js_1.ScalarType.BYTES, json);
        }
        catch (e) {
            let m = `cannot decode message google.protobuf.BytesValue from JSON"`;
            if (e instanceof Error && e.message.length > 0) {
                m += `: ${e.message}`;
            }
            throw new Error(m);
        }
        return this;
    }
    static fromBinary(bytes, options) {
        return new BytesValue().fromBinary(bytes, options);
    }
    static fromJson(jsonValue, options) {
        return new BytesValue().fromJson(jsonValue, options);
    }
    static fromJsonString(jsonString, options) {
        return new BytesValue().fromJsonString(jsonString, options);
    }
    static equals(a, b) {
        return proto3_js_1.proto3.util.equals(BytesValue, a, b);
    }
}
exports.BytesValue = BytesValue;
BytesValue.runtime = proto3_js_1.proto3;
BytesValue.typeName = "google.protobuf.BytesValue";
BytesValue.fields = proto3_js_1.proto3.util.newFieldList(() => [
    { no: 1, name: "value", kind: "scalar", T: 12 /* ScalarType.BYTES */ },
]);
BytesValue.fieldWrapper = {
    wrapField(value) {
        return new BytesValue({ value });
    },
    unwrapField(value) {
        return value.value;
    }
};
