{"name": "@livekit/protocol", "version": "1.39.3", "description": "", "type": "module", "require": "dist/index.cjs", "files": ["src", "dist"], "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "main": "./dist/index.mjs", "types": "./dist/index.d.mts", "keywords": [], "author": "LiveKit", "license": "Apache-2.0", "devDependencies": {"@bufbuild/protoc-gen-es": "^1.10.0", "genversion": "^3.2.0", "typescript": "5.8.3", "unbuild": "^2.0.0"}, "dependencies": {"@bufbuild/protobuf": "^1.10.0"}, "scripts": {"generate:proto": "mkdir -p src/gen && protoc --es_out src/gen --es_opt target=dts+js -I=../../protobufs ../../protobufs/livekit_*.proto", "generate:version": "genversion --esm --semi src/gen/version.js", "build": "pnpm generate:version && pnpm generate:proto && unbuild"}}