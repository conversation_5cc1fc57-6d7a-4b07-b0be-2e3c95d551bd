// Copyright 2023 LiveKit, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// @generated by protoc-gen-es v1.10.1 with parameter "target=dts+js"
// @generated from file livekit_models.proto (package livekit, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { proto3, Timestamp } from "@bufbuild/protobuf";
import { MetricsBatch } from "./livekit_metrics_pb.js";

/**
 * @generated from enum livekit.AudioCodec
 */
export const AudioCodec = /*@__PURE__*/ proto3.makeEnum(
  "livekit.AudioCodec",
  [
    {no: 0, name: "DEFAULT_AC"},
    {no: 1, name: "OPUS"},
    {no: 2, name: "AAC"},
  ],
);

/**
 * @generated from enum livekit.VideoCodec
 */
export const VideoCodec = /*@__PURE__*/ proto3.makeEnum(
  "livekit.VideoCodec",
  [
    {no: 0, name: "DEFAULT_VC"},
    {no: 1, name: "H264_BASELINE"},
    {no: 2, name: "H264_MAIN"},
    {no: 3, name: "H264_HIGH"},
    {no: 4, name: "VP8"},
  ],
);

/**
 * @generated from enum livekit.ImageCodec
 */
export const ImageCodec = /*@__PURE__*/ proto3.makeEnum(
  "livekit.ImageCodec",
  [
    {no: 0, name: "IC_DEFAULT"},
    {no: 1, name: "IC_JPEG"},
  ],
);

/**
 * Policy for publisher to handle subscribers that are unable to support the primary codec of a track
 *
 * @generated from enum livekit.BackupCodecPolicy
 */
export const BackupCodecPolicy = /*@__PURE__*/ proto3.makeEnum(
  "livekit.BackupCodecPolicy",
  [
    {no: 0, name: "PREFER_REGRESSION"},
    {no: 1, name: "SIMULCAST"},
    {no: 2, name: "REGRESSION"},
  ],
);

/**
 * @generated from enum livekit.TrackType
 */
export const TrackType = /*@__PURE__*/ proto3.makeEnum(
  "livekit.TrackType",
  [
    {no: 0, name: "AUDIO"},
    {no: 1, name: "VIDEO"},
    {no: 2, name: "DATA"},
  ],
);

/**
 * @generated from enum livekit.TrackSource
 */
export const TrackSource = /*@__PURE__*/ proto3.makeEnum(
  "livekit.TrackSource",
  [
    {no: 0, name: "UNKNOWN"},
    {no: 1, name: "CAMERA"},
    {no: 2, name: "MICROPHONE"},
    {no: 3, name: "SCREEN_SHARE"},
    {no: 4, name: "SCREEN_SHARE_AUDIO"},
  ],
);

/**
 * @generated from enum livekit.VideoQuality
 */
export const VideoQuality = /*@__PURE__*/ proto3.makeEnum(
  "livekit.VideoQuality",
  [
    {no: 0, name: "LOW"},
    {no: 1, name: "MEDIUM"},
    {no: 2, name: "HIGH"},
    {no: 3, name: "OFF"},
  ],
);

/**
 * @generated from enum livekit.ConnectionQuality
 */
export const ConnectionQuality = /*@__PURE__*/ proto3.makeEnum(
  "livekit.ConnectionQuality",
  [
    {no: 0, name: "POOR"},
    {no: 1, name: "GOOD"},
    {no: 2, name: "EXCELLENT"},
    {no: 3, name: "LOST"},
  ],
);

/**
 * @generated from enum livekit.ClientConfigSetting
 */
export const ClientConfigSetting = /*@__PURE__*/ proto3.makeEnum(
  "livekit.ClientConfigSetting",
  [
    {no: 0, name: "UNSET"},
    {no: 1, name: "DISABLED"},
    {no: 2, name: "ENABLED"},
  ],
);

/**
 * @generated from enum livekit.DisconnectReason
 */
export const DisconnectReason = /*@__PURE__*/ proto3.makeEnum(
  "livekit.DisconnectReason",
  [
    {no: 0, name: "UNKNOWN_REASON"},
    {no: 1, name: "CLIENT_INITIATED"},
    {no: 2, name: "DUPLICATE_IDENTITY"},
    {no: 3, name: "SERVER_SHUTDOWN"},
    {no: 4, name: "PARTICIPANT_REMOVED"},
    {no: 5, name: "ROOM_DELETED"},
    {no: 6, name: "STATE_MISMATCH"},
    {no: 7, name: "JOIN_FAILURE"},
    {no: 8, name: "MIGRATION"},
    {no: 9, name: "SIGNAL_CLOSE"},
    {no: 10, name: "ROOM_CLOSED"},
    {no: 11, name: "USER_UNAVAILABLE"},
    {no: 12, name: "USER_REJECTED"},
    {no: 13, name: "SIP_TRUNK_FAILURE"},
    {no: 14, name: "CONNECTION_TIMEOUT"},
    {no: 15, name: "MEDIA_FAILURE"},
  ],
);

/**
 * @generated from enum livekit.ReconnectReason
 */
export const ReconnectReason = /*@__PURE__*/ proto3.makeEnum(
  "livekit.ReconnectReason",
  [
    {no: 0, name: "RR_UNKNOWN"},
    {no: 1, name: "RR_SIGNAL_DISCONNECTED"},
    {no: 2, name: "RR_PUBLISHER_FAILED"},
    {no: 3, name: "RR_SUBSCRIBER_FAILED"},
    {no: 4, name: "RR_SWITCH_CANDIDATE"},
  ],
);

/**
 * @generated from enum livekit.SubscriptionError
 */
export const SubscriptionError = /*@__PURE__*/ proto3.makeEnum(
  "livekit.SubscriptionError",
  [
    {no: 0, name: "SE_UNKNOWN"},
    {no: 1, name: "SE_CODEC_UNSUPPORTED"},
    {no: 2, name: "SE_TRACK_NOTFOUND"},
  ],
);

/**
 * @generated from enum livekit.AudioTrackFeature
 */
export const AudioTrackFeature = /*@__PURE__*/ proto3.makeEnum(
  "livekit.AudioTrackFeature",
  [
    {no: 0, name: "TF_STEREO"},
    {no: 1, name: "TF_NO_DTX"},
    {no: 2, name: "TF_AUTO_GAIN_CONTROL"},
    {no: 3, name: "TF_ECHO_CANCELLATION"},
    {no: 4, name: "TF_NOISE_SUPPRESSION"},
    {no: 5, name: "TF_ENHANCED_NOISE_CANCELLATION"},
    {no: 6, name: "TF_PRECONNECT_BUFFER"},
  ],
);

/**
 * @generated from message livekit.Pagination
 */
export const Pagination = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.Pagination",
  () => [
    { no: 1, name: "after_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "limit", kind: "scalar", T: 5 /* ScalarType.INT32 */ },
  ],
);

/**
 * ListUpdate is used for updated APIs where 'repeated string' field is modified.
 *
 * @generated from message livekit.ListUpdate
 */
export const ListUpdate = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.ListUpdate",
  () => [
    { no: 1, name: "set", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
  ],
);

/**
 * @generated from message livekit.Room
 */
export const Room = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.Room",
  () => [
    { no: 1, name: "sid", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "empty_timeout", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 14, name: "departure_timeout", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 4, name: "max_participants", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 5, name: "creation_time", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
    { no: 15, name: "creation_time_ms", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
    { no: 6, name: "turn_password", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 7, name: "enabled_codecs", kind: "message", T: Codec, repeated: true },
    { no: 8, name: "metadata", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 9, name: "num_participants", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 11, name: "num_publishers", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 10, name: "active_recording", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 13, name: "version", kind: "message", T: TimedVersion },
  ],
);

/**
 * @generated from message livekit.Codec
 */
export const Codec = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.Codec",
  () => [
    { no: 1, name: "mime", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "fmtp_line", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ],
);

/**
 * @generated from message livekit.PlayoutDelay
 */
export const PlayoutDelay = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.PlayoutDelay",
  () => [
    { no: 1, name: "enabled", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 2, name: "min", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 3, name: "max", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
  ],
);

/**
 * @generated from message livekit.ParticipantPermission
 */
export const ParticipantPermission = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.ParticipantPermission",
  () => [
    { no: 1, name: "can_subscribe", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 2, name: "can_publish", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 3, name: "can_publish_data", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 9, name: "can_publish_sources", kind: "enum", T: proto3.getEnumType(TrackSource), repeated: true },
    { no: 7, name: "hidden", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 8, name: "recorder", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 10, name: "can_update_metadata", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 11, name: "agent", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 12, name: "can_subscribe_metrics", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
  ],
);

/**
 * @generated from message livekit.ParticipantInfo
 */
export const ParticipantInfo = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.ParticipantInfo",
  () => [
    { no: 1, name: "sid", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "identity", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "state", kind: "enum", T: proto3.getEnumType(ParticipantInfo_State) },
    { no: 4, name: "tracks", kind: "message", T: TrackInfo, repeated: true },
    { no: 5, name: "metadata", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 6, name: "joined_at", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
    { no: 17, name: "joined_at_ms", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
    { no: 9, name: "name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 10, name: "version", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 11, name: "permission", kind: "message", T: ParticipantPermission },
    { no: 12, name: "region", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 13, name: "is_publisher", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 14, name: "kind", kind: "enum", T: proto3.getEnumType(ParticipantInfo_Kind) },
    { no: 15, name: "attributes", kind: "map", K: 9 /* ScalarType.STRING */, V: {kind: "scalar", T: 9 /* ScalarType.STRING */} },
    { no: 16, name: "disconnect_reason", kind: "enum", T: proto3.getEnumType(DisconnectReason) },
    { no: 18, name: "kind_details", kind: "enum", T: proto3.getEnumType(ParticipantInfo_KindDetail), repeated: true },
  ],
);

/**
 * @generated from enum livekit.ParticipantInfo.State
 */
export const ParticipantInfo_State = /*@__PURE__*/ proto3.makeEnum(
  "livekit.ParticipantInfo.State",
  [
    {no: 0, name: "JOINING"},
    {no: 1, name: "JOINED"},
    {no: 2, name: "ACTIVE"},
    {no: 3, name: "DISCONNECTED"},
  ],
);

/**
 * @generated from enum livekit.ParticipantInfo.Kind
 */
export const ParticipantInfo_Kind = /*@__PURE__*/ proto3.makeEnum(
  "livekit.ParticipantInfo.Kind",
  [
    {no: 0, name: "STANDARD"},
    {no: 1, name: "INGRESS"},
    {no: 2, name: "EGRESS"},
    {no: 3, name: "SIP"},
    {no: 4, name: "AGENT"},
  ],
);

/**
 * @generated from enum livekit.ParticipantInfo.KindDetail
 */
export const ParticipantInfo_KindDetail = /*@__PURE__*/ proto3.makeEnum(
  "livekit.ParticipantInfo.KindDetail",
  [
    {no: 0, name: "CLOUD_AGENT"},
    {no: 1, name: "FORWARDED"},
  ],
);

/**
 * @generated from message livekit.Encryption
 */
export const Encryption = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.Encryption",
  [],
);

/**
 * @generated from enum livekit.Encryption.Type
 */
export const Encryption_Type = /*@__PURE__*/ proto3.makeEnum(
  "livekit.Encryption.Type",
  [
    {no: 0, name: "NONE"},
    {no: 1, name: "GCM"},
    {no: 2, name: "CUSTOM"},
  ],
);

/**
 * @generated from message livekit.SimulcastCodecInfo
 */
export const SimulcastCodecInfo = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.SimulcastCodecInfo",
  () => [
    { no: 1, name: "mime_type", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "mid", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "cid", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "layers", kind: "message", T: VideoLayer, repeated: true },
  ],
);

/**
 * @generated from message livekit.TrackInfo
 */
export const TrackInfo = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.TrackInfo",
  () => [
    { no: 1, name: "sid", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "type", kind: "enum", T: proto3.getEnumType(TrackType) },
    { no: 3, name: "name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "muted", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 5, name: "width", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 6, name: "height", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 7, name: "simulcast", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 8, name: "disable_dtx", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 9, name: "source", kind: "enum", T: proto3.getEnumType(TrackSource) },
    { no: 10, name: "layers", kind: "message", T: VideoLayer, repeated: true },
    { no: 11, name: "mime_type", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 12, name: "mid", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 13, name: "codecs", kind: "message", T: SimulcastCodecInfo, repeated: true },
    { no: 14, name: "stereo", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 15, name: "disable_red", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 16, name: "encryption", kind: "enum", T: proto3.getEnumType(Encryption_Type) },
    { no: 17, name: "stream", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 18, name: "version", kind: "message", T: TimedVersion },
    { no: 19, name: "audio_features", kind: "enum", T: proto3.getEnumType(AudioTrackFeature), repeated: true },
    { no: 20, name: "backup_codec_policy", kind: "enum", T: proto3.getEnumType(BackupCodecPolicy) },
  ],
);

/**
 * provide information about available spatial layers
 *
 * @generated from message livekit.VideoLayer
 */
export const VideoLayer = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.VideoLayer",
  () => [
    { no: 1, name: "quality", kind: "enum", T: proto3.getEnumType(VideoQuality) },
    { no: 2, name: "width", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 3, name: "height", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 4, name: "bitrate", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 5, name: "ssrc", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 6, name: "spatial_layer", kind: "scalar", T: 5 /* ScalarType.INT32 */ },
    { no: 7, name: "rid", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ],
);

/**
 * new DataPacket API
 *
 * @generated from message livekit.DataPacket
 */
export const DataPacket = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.DataPacket",
  () => [
    { no: 1, name: "kind", kind: "enum", T: proto3.getEnumType(DataPacket_Kind) },
    { no: 4, name: "participant_identity", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 5, name: "destination_identities", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
    { no: 2, name: "user", kind: "message", T: UserPacket, oneof: "value" },
    { no: 3, name: "speaker", kind: "message", T: ActiveSpeakerUpdate, oneof: "value" },
    { no: 6, name: "sip_dtmf", kind: "message", T: SipDTMF, oneof: "value" },
    { no: 7, name: "transcription", kind: "message", T: Transcription, oneof: "value" },
    { no: 8, name: "metrics", kind: "message", T: MetricsBatch, oneof: "value" },
    { no: 9, name: "chat_message", kind: "message", T: ChatMessage, oneof: "value" },
    { no: 10, name: "rpc_request", kind: "message", T: RpcRequest, oneof: "value" },
    { no: 11, name: "rpc_ack", kind: "message", T: RpcAck, oneof: "value" },
    { no: 12, name: "rpc_response", kind: "message", T: RpcResponse, oneof: "value" },
    { no: 13, name: "stream_header", kind: "message", T: DataStream_Header, oneof: "value" },
    { no: 14, name: "stream_chunk", kind: "message", T: DataStream_Chunk, oneof: "value" },
    { no: 15, name: "stream_trailer", kind: "message", T: DataStream_Trailer, oneof: "value" },
    { no: 16, name: "sequence", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 17, name: "participant_sid", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ],
);

/**
 * @generated from enum livekit.DataPacket.Kind
 */
export const DataPacket_Kind = /*@__PURE__*/ proto3.makeEnum(
  "livekit.DataPacket.Kind",
  [
    {no: 0, name: "RELIABLE"},
    {no: 1, name: "LOSSY"},
  ],
);

/**
 * @generated from message livekit.ActiveSpeakerUpdate
 */
export const ActiveSpeakerUpdate = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.ActiveSpeakerUpdate",
  () => [
    { no: 1, name: "speakers", kind: "message", T: SpeakerInfo, repeated: true },
  ],
);

/**
 * @generated from message livekit.SpeakerInfo
 */
export const SpeakerInfo = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.SpeakerInfo",
  () => [
    { no: 1, name: "sid", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "level", kind: "scalar", T: 2 /* ScalarType.FLOAT */ },
    { no: 3, name: "active", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
  ],
);

/**
 * @generated from message livekit.UserPacket
 */
export const UserPacket = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.UserPacket",
  () => [
    { no: 1, name: "participant_sid", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 5, name: "participant_identity", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "payload", kind: "scalar", T: 12 /* ScalarType.BYTES */ },
    { no: 3, name: "destination_sids", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
    { no: 6, name: "destination_identities", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
    { no: 4, name: "topic", kind: "scalar", T: 9 /* ScalarType.STRING */, opt: true },
    { no: 8, name: "id", kind: "scalar", T: 9 /* ScalarType.STRING */, opt: true },
    { no: 9, name: "start_time", kind: "scalar", T: 4 /* ScalarType.UINT64 */, opt: true },
    { no: 10, name: "end_time", kind: "scalar", T: 4 /* ScalarType.UINT64 */, opt: true },
    { no: 11, name: "nonce", kind: "scalar", T: 12 /* ScalarType.BYTES */ },
  ],
);

/**
 * @generated from message livekit.SipDTMF
 */
export const SipDTMF = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.SipDTMF",
  () => [
    { no: 3, name: "code", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 4, name: "digit", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ],
);

/**
 * @generated from message livekit.Transcription
 */
export const Transcription = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.Transcription",
  () => [
    { no: 2, name: "transcribed_participant_identity", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "track_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "segments", kind: "message", T: TranscriptionSegment, repeated: true },
  ],
);

/**
 * @generated from message livekit.TranscriptionSegment
 */
export const TranscriptionSegment = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.TranscriptionSegment",
  () => [
    { no: 1, name: "id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "text", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "start_time", kind: "scalar", T: 4 /* ScalarType.UINT64 */ },
    { no: 4, name: "end_time", kind: "scalar", T: 4 /* ScalarType.UINT64 */ },
    { no: 5, name: "final", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 6, name: "language", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ],
);

/**
 * @generated from message livekit.ChatMessage
 */
export const ChatMessage = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.ChatMessage",
  () => [
    { no: 1, name: "id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "timestamp", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
    { no: 3, name: "edit_timestamp", kind: "scalar", T: 3 /* ScalarType.INT64 */, opt: true },
    { no: 4, name: "message", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 5, name: "deleted", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 6, name: "generated", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
  ],
);

/**
 * @generated from message livekit.RpcRequest
 */
export const RpcRequest = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.RpcRequest",
  () => [
    { no: 1, name: "id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "method", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "payload", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "response_timeout_ms", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 5, name: "version", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
  ],
);

/**
 * @generated from message livekit.RpcAck
 */
export const RpcAck = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.RpcAck",
  () => [
    { no: 1, name: "request_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ],
);

/**
 * @generated from message livekit.RpcResponse
 */
export const RpcResponse = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.RpcResponse",
  () => [
    { no: 1, name: "request_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "payload", kind: "scalar", T: 9 /* ScalarType.STRING */, oneof: "value" },
    { no: 3, name: "error", kind: "message", T: RpcError, oneof: "value" },
  ],
);

/**
 * @generated from message livekit.RpcError
 */
export const RpcError = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.RpcError",
  () => [
    { no: 1, name: "code", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 2, name: "message", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "data", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ],
);

/**
 * @generated from message livekit.ParticipantTracks
 */
export const ParticipantTracks = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.ParticipantTracks",
  () => [
    { no: 1, name: "participant_sid", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "track_sids", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
  ],
);

/**
 * details about the server
 *
 * @generated from message livekit.ServerInfo
 */
export const ServerInfo = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.ServerInfo",
  () => [
    { no: 1, name: "edition", kind: "enum", T: proto3.getEnumType(ServerInfo_Edition) },
    { no: 2, name: "version", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "protocol", kind: "scalar", T: 5 /* ScalarType.INT32 */ },
    { no: 4, name: "region", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 5, name: "node_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 6, name: "debug_info", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 7, name: "agent_protocol", kind: "scalar", T: 5 /* ScalarType.INT32 */ },
  ],
);

/**
 * @generated from enum livekit.ServerInfo.Edition
 */
export const ServerInfo_Edition = /*@__PURE__*/ proto3.makeEnum(
  "livekit.ServerInfo.Edition",
  [
    {no: 0, name: "Standard"},
    {no: 1, name: "Cloud"},
  ],
);

/**
 * details about the client
 *
 * @generated from message livekit.ClientInfo
 */
export const ClientInfo = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.ClientInfo",
  () => [
    { no: 1, name: "sdk", kind: "enum", T: proto3.getEnumType(ClientInfo_SDK) },
    { no: 2, name: "version", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "protocol", kind: "scalar", T: 5 /* ScalarType.INT32 */ },
    { no: 4, name: "os", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 5, name: "os_version", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 6, name: "device_model", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 7, name: "browser", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 8, name: "browser_version", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 9, name: "address", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 10, name: "network", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 11, name: "other_sdks", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ],
);

/**
 * @generated from enum livekit.ClientInfo.SDK
 */
export const ClientInfo_SDK = /*@__PURE__*/ proto3.makeEnum(
  "livekit.ClientInfo.SDK",
  [
    {no: 0, name: "UNKNOWN"},
    {no: 1, name: "JS"},
    {no: 2, name: "SWIFT"},
    {no: 3, name: "ANDROID"},
    {no: 4, name: "FLUTTER"},
    {no: 5, name: "GO"},
    {no: 6, name: "UNITY"},
    {no: 7, name: "REACT_NATIVE"},
    {no: 8, name: "RUST"},
    {no: 9, name: "PYTHON"},
    {no: 10, name: "CPP"},
    {no: 11, name: "UNITY_WEB"},
    {no: 12, name: "NODE"},
    {no: 13, name: "UNREAL"},
    {no: 14, name: "ESP32"},
  ],
);

/**
 * server provided client configuration
 *
 * @generated from message livekit.ClientConfiguration
 */
export const ClientConfiguration = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.ClientConfiguration",
  () => [
    { no: 1, name: "video", kind: "message", T: VideoConfiguration },
    { no: 2, name: "screen", kind: "message", T: VideoConfiguration },
    { no: 3, name: "resume_connection", kind: "enum", T: proto3.getEnumType(ClientConfigSetting) },
    { no: 4, name: "disabled_codecs", kind: "message", T: DisabledCodecs },
    { no: 5, name: "force_relay", kind: "enum", T: proto3.getEnumType(ClientConfigSetting) },
  ],
);

/**
 * @generated from message livekit.VideoConfiguration
 */
export const VideoConfiguration = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.VideoConfiguration",
  () => [
    { no: 1, name: "hardware_encoder", kind: "enum", T: proto3.getEnumType(ClientConfigSetting) },
  ],
);

/**
 * @generated from message livekit.DisabledCodecs
 */
export const DisabledCodecs = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.DisabledCodecs",
  () => [
    { no: 1, name: "codecs", kind: "message", T: Codec, repeated: true },
    { no: 2, name: "publish", kind: "message", T: Codec, repeated: true },
  ],
);

/**
 * @generated from message livekit.RTPDrift
 */
export const RTPDrift = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.RTPDrift",
  () => [
    { no: 1, name: "start_time", kind: "message", T: Timestamp },
    { no: 2, name: "end_time", kind: "message", T: Timestamp },
    { no: 3, name: "duration", kind: "scalar", T: 1 /* ScalarType.DOUBLE */ },
    { no: 4, name: "start_timestamp", kind: "scalar", T: 4 /* ScalarType.UINT64 */ },
    { no: 5, name: "end_timestamp", kind: "scalar", T: 4 /* ScalarType.UINT64 */ },
    { no: 6, name: "rtp_clock_ticks", kind: "scalar", T: 4 /* ScalarType.UINT64 */ },
    { no: 7, name: "drift_samples", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
    { no: 8, name: "drift_ms", kind: "scalar", T: 1 /* ScalarType.DOUBLE */ },
    { no: 9, name: "clock_rate", kind: "scalar", T: 1 /* ScalarType.DOUBLE */ },
  ],
);

/**
 * @generated from message livekit.RTPStats
 */
export const RTPStats = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.RTPStats",
  () => [
    { no: 1, name: "start_time", kind: "message", T: Timestamp },
    { no: 2, name: "end_time", kind: "message", T: Timestamp },
    { no: 3, name: "duration", kind: "scalar", T: 1 /* ScalarType.DOUBLE */ },
    { no: 4, name: "packets", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 5, name: "packet_rate", kind: "scalar", T: 1 /* ScalarType.DOUBLE */ },
    { no: 6, name: "bytes", kind: "scalar", T: 4 /* ScalarType.UINT64 */ },
    { no: 39, name: "header_bytes", kind: "scalar", T: 4 /* ScalarType.UINT64 */ },
    { no: 7, name: "bitrate", kind: "scalar", T: 1 /* ScalarType.DOUBLE */ },
    { no: 8, name: "packets_lost", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 9, name: "packet_loss_rate", kind: "scalar", T: 1 /* ScalarType.DOUBLE */ },
    { no: 10, name: "packet_loss_percentage", kind: "scalar", T: 2 /* ScalarType.FLOAT */ },
    { no: 11, name: "packets_duplicate", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 12, name: "packet_duplicate_rate", kind: "scalar", T: 1 /* ScalarType.DOUBLE */ },
    { no: 13, name: "bytes_duplicate", kind: "scalar", T: 4 /* ScalarType.UINT64 */ },
    { no: 40, name: "header_bytes_duplicate", kind: "scalar", T: 4 /* ScalarType.UINT64 */ },
    { no: 14, name: "bitrate_duplicate", kind: "scalar", T: 1 /* ScalarType.DOUBLE */ },
    { no: 15, name: "packets_padding", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 16, name: "packet_padding_rate", kind: "scalar", T: 1 /* ScalarType.DOUBLE */ },
    { no: 17, name: "bytes_padding", kind: "scalar", T: 4 /* ScalarType.UINT64 */ },
    { no: 41, name: "header_bytes_padding", kind: "scalar", T: 4 /* ScalarType.UINT64 */ },
    { no: 18, name: "bitrate_padding", kind: "scalar", T: 1 /* ScalarType.DOUBLE */ },
    { no: 19, name: "packets_out_of_order", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 20, name: "frames", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 21, name: "frame_rate", kind: "scalar", T: 1 /* ScalarType.DOUBLE */ },
    { no: 22, name: "jitter_current", kind: "scalar", T: 1 /* ScalarType.DOUBLE */ },
    { no: 23, name: "jitter_max", kind: "scalar", T: 1 /* ScalarType.DOUBLE */ },
    { no: 24, name: "gap_histogram", kind: "map", K: 5 /* ScalarType.INT32 */, V: {kind: "scalar", T: 13 /* ScalarType.UINT32 */} },
    { no: 25, name: "nacks", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 37, name: "nack_acks", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 26, name: "nack_misses", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 38, name: "nack_repeated", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 27, name: "plis", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 28, name: "last_pli", kind: "message", T: Timestamp },
    { no: 29, name: "firs", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 30, name: "last_fir", kind: "message", T: Timestamp },
    { no: 31, name: "rtt_current", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 32, name: "rtt_max", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 33, name: "key_frames", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 34, name: "last_key_frame", kind: "message", T: Timestamp },
    { no: 35, name: "layer_lock_plis", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 36, name: "last_layer_lock_pli", kind: "message", T: Timestamp },
    { no: 44, name: "packet_drift", kind: "message", T: RTPDrift },
    { no: 45, name: "ntp_report_drift", kind: "message", T: RTPDrift },
    { no: 46, name: "rebased_report_drift", kind: "message", T: RTPDrift },
    { no: 47, name: "received_report_drift", kind: "message", T: RTPDrift },
  ],
);

/**
 * @generated from message livekit.RTCPSenderReportState
 */
export const RTCPSenderReportState = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.RTCPSenderReportState",
  () => [
    { no: 1, name: "rtp_timestamp", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 2, name: "rtp_timestamp_ext", kind: "scalar", T: 4 /* ScalarType.UINT64 */ },
    { no: 3, name: "ntp_timestamp", kind: "scalar", T: 4 /* ScalarType.UINT64 */ },
    { no: 4, name: "at", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
    { no: 5, name: "at_adjusted", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
    { no: 6, name: "packets", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 7, name: "octets", kind: "scalar", T: 4 /* ScalarType.UINT64 */ },
  ],
);

/**
 * @generated from message livekit.RTPForwarderState
 */
export const RTPForwarderState = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.RTPForwarderState",
  () => [
    { no: 1, name: "started", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 2, name: "reference_layer_spatial", kind: "scalar", T: 5 /* ScalarType.INT32 */ },
    { no: 3, name: "pre_start_time", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
    { no: 4, name: "ext_first_timestamp", kind: "scalar", T: 4 /* ScalarType.UINT64 */ },
    { no: 5, name: "dummy_start_timestamp_offset", kind: "scalar", T: 4 /* ScalarType.UINT64 */ },
    { no: 6, name: "rtp_munger", kind: "message", T: RTPMungerState },
    { no: 7, name: "vp8_munger", kind: "message", T: VP8MungerState, oneof: "codec_munger" },
    { no: 8, name: "sender_report_state", kind: "message", T: RTCPSenderReportState, repeated: true },
  ],
);

/**
 * @generated from message livekit.RTPMungerState
 */
export const RTPMungerState = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.RTPMungerState",
  () => [
    { no: 1, name: "ext_last_sequence_number", kind: "scalar", T: 4 /* ScalarType.UINT64 */ },
    { no: 2, name: "ext_second_last_sequence_number", kind: "scalar", T: 4 /* ScalarType.UINT64 */ },
    { no: 3, name: "ext_last_timestamp", kind: "scalar", T: 4 /* ScalarType.UINT64 */ },
    { no: 4, name: "ext_second_last_timestamp", kind: "scalar", T: 4 /* ScalarType.UINT64 */ },
    { no: 5, name: "last_marker", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 6, name: "second_last_marker", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
  ],
);

/**
 * @generated from message livekit.VP8MungerState
 */
export const VP8MungerState = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.VP8MungerState",
  () => [
    { no: 1, name: "ext_last_picture_id", kind: "scalar", T: 5 /* ScalarType.INT32 */ },
    { no: 2, name: "picture_id_used", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 3, name: "last_tl0_pic_idx", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 4, name: "tl0_pic_idx_used", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 5, name: "tid_used", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 6, name: "last_key_idx", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 7, name: "key_idx_used", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
  ],
);

/**
 * @generated from message livekit.TimedVersion
 */
export const TimedVersion = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.TimedVersion",
  () => [
    { no: 1, name: "unix_micro", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
    { no: 2, name: "ticks", kind: "scalar", T: 5 /* ScalarType.INT32 */ },
  ],
);

/**
 * @generated from message livekit.DataStream
 */
export const DataStream = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.DataStream",
  [],
);

/**
 * enum for operation types (specific to TextHeader)
 *
 * @generated from enum livekit.DataStream.OperationType
 */
export const DataStream_OperationType = /*@__PURE__*/ proto3.makeEnum(
  "livekit.DataStream.OperationType",
  [
    {no: 0, name: "CREATE"},
    {no: 1, name: "UPDATE"},
    {no: 2, name: "DELETE"},
    {no: 3, name: "REACTION"},
  ],
);

/**
 * header properties specific to text streams
 *
 * @generated from message livekit.DataStream.TextHeader
 */
export const DataStream_TextHeader = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.DataStream.TextHeader",
  () => [
    { no: 1, name: "operation_type", kind: "enum", T: proto3.getEnumType(DataStream_OperationType) },
    { no: 2, name: "version", kind: "scalar", T: 5 /* ScalarType.INT32 */ },
    { no: 3, name: "reply_to_stream_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "attached_stream_ids", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
    { no: 5, name: "generated", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
  ],
  {localName: "DataStream_TextHeader"},
);

/**
 * header properties specific to byte or file streams
 *
 * @generated from message livekit.DataStream.ByteHeader
 */
export const DataStream_ByteHeader = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.DataStream.ByteHeader",
  () => [
    { no: 1, name: "name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ],
  {localName: "DataStream_ByteHeader"},
);

/**
 * main DataStream.Header that contains a oneof for specific headers
 *
 * @generated from message livekit.DataStream.Header
 */
export const DataStream_Header = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.DataStream.Header",
  () => [
    { no: 1, name: "stream_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "timestamp", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
    { no: 3, name: "topic", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "mime_type", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 5, name: "total_length", kind: "scalar", T: 4 /* ScalarType.UINT64 */, opt: true },
    { no: 7, name: "encryption_type", kind: "enum", T: proto3.getEnumType(Encryption_Type) },
    { no: 8, name: "attributes", kind: "map", K: 9 /* ScalarType.STRING */, V: {kind: "scalar", T: 9 /* ScalarType.STRING */} },
    { no: 9, name: "text_header", kind: "message", T: DataStream_TextHeader, oneof: "content_header" },
    { no: 10, name: "byte_header", kind: "message", T: DataStream_ByteHeader, oneof: "content_header" },
  ],
  {localName: "DataStream_Header"},
);

/**
 * @generated from message livekit.DataStream.Chunk
 */
export const DataStream_Chunk = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.DataStream.Chunk",
  () => [
    { no: 1, name: "stream_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "chunk_index", kind: "scalar", T: 4 /* ScalarType.UINT64 */ },
    { no: 3, name: "content", kind: "scalar", T: 12 /* ScalarType.BYTES */ },
    { no: 4, name: "version", kind: "scalar", T: 5 /* ScalarType.INT32 */ },
    { no: 5, name: "iv", kind: "scalar", T: 12 /* ScalarType.BYTES */, opt: true },
  ],
  {localName: "DataStream_Chunk"},
);

/**
 * @generated from message livekit.DataStream.Trailer
 */
export const DataStream_Trailer = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.DataStream.Trailer",
  () => [
    { no: 1, name: "stream_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "reason", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "attributes", kind: "map", K: 9 /* ScalarType.STRING */, V: {kind: "scalar", T: 9 /* ScalarType.STRING */} },
  ],
  {localName: "DataStream_Trailer"},
);

/**
 * @generated from message livekit.WebhookConfig
 */
export const WebhookConfig = /*@__PURE__*/ proto3.makeMessageType(
  "livekit.WebhookConfig",
  () => [
    { no: 1, name: "url", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "signing_key", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ],
);

