'use client';

export interface MobileVoiceServiceCallbacks {
  onTranscription?: (text: string, isFinal: boolean) => void;
  onAudioReceived?: (audioUrl: string) => void;
  onError?: (error: Error) => void;
  onConnectionStateChange?: (connected: boolean) => void;
}

export interface MobileTTSOptions {
  text: string;
  language?: string;
  voice?: string;
}

export interface MobileSTTOptions {
  language?: string;
  continuous?: boolean;
  interimResults?: boolean;
}

export class MobileVoiceService {
  private callbacks: MobileVoiceServiceCallbacks = {};
  private speechRecognition: SpeechRecognition | null = null;
  private audioContext: AudioContext | null = null;
  private isRecording = false;
  private isConnected = false;
  private userInteracted = false;
  private currentLanguage = 'en-US';

  constructor(callbacks?: MobileVoiceServiceCallbacks) {
    this.callbacks = callbacks || {};
    this.initialize();
  }

  private async initialize(): Promise<void> {
    if (typeof window === 'undefined') return;

    try {
      // Initialize audio context
      await this.initializeAudioContext();
      
      // Initialize speech recognition
      this.initializeSpeechRecognition();
      
      // Set up user interaction listeners
      this.setupUserInteractionListeners();
      
      this.isConnected = true;
      this.callbacks.onConnectionStateChange?.(true);
      console.log('Mobile voice service initialized');
    } catch (error) {
      console.error('Failed to initialize mobile voice service:', error);
      this.callbacks.onError?.(error as Error);
    }
  }

  private async initializeAudioContext(): Promise<void> {
    if (typeof window === 'undefined') return;

    const AudioContextClass = window.AudioContext || (window as any).webkitAudioContext;
    if (AudioContextClass) {
      this.audioContext = new AudioContextClass();
      
      // Try to resume context immediately if possible
      if (this.audioContext.state === 'suspended') {
        try {
          await this.audioContext.resume();
        } catch (error) {
          console.log('Audio context will be resumed on user interaction');
        }
      }
    }
  }

  private setupUserInteractionListeners(): void {
    const enableAudio = async () => {
      this.userInteracted = true;
      
      if (this.audioContext && this.audioContext.state === 'suspended') {
        try {
          await this.audioContext.resume();
          console.log('Audio context resumed after user interaction');
        } catch (error) {
          console.error('Failed to resume audio context:', error);
        }
      }
      
      // Remove listeners after first interaction
      document.removeEventListener('touchstart', enableAudio);
      document.removeEventListener('click', enableAudio);
      document.removeEventListener('keydown', enableAudio);
    };

    document.addEventListener('touchstart', enableAudio, { once: true });
    document.addEventListener('click', enableAudio, { once: true });
    document.addEventListener('keydown', enableAudio, { once: true });
  }

  private initializeSpeechRecognition(): void {
    if (typeof window === 'undefined') return;

    const SpeechRecognition = window.SpeechRecognition || (window as any).webkitSpeechRecognition;
    if (SpeechRecognition) {
      this.speechRecognition = new SpeechRecognition();
      this.speechRecognition.continuous = false; // Better for mobile
      this.speechRecognition.interimResults = true;
      this.speechRecognition.lang = this.currentLanguage;
      this.speechRecognition.maxAlternatives = 1;

      this.speechRecognition.onresult = (event) => {
        for (let i = event.resultIndex; i < event.results.length; i++) {
          const transcript = event.results[i][0].transcript;
          const isFinal = event.results[i].isFinal;
          this.callbacks.onTranscription?.(transcript, isFinal);
        }
      };

      this.speechRecognition.onend = () => {
        this.isRecording = false;
        console.log('Speech recognition ended');
      };

      this.speechRecognition.onerror = (event) => {
        console.error('Speech recognition error:', event.error);
        this.isRecording = false;
        
        let errorMessage = 'Speech recognition failed';
        switch (event.error) {
          case 'not-allowed':
            errorMessage = 'Microphone access denied. Please allow microphone permissions in your browser settings.';
            break;
          case 'no-speech':
            errorMessage = 'No speech detected. Please try speaking again.';
            break;
          case 'network':
            errorMessage = 'Network error. Please check your internet connection.';
            break;
          case 'audio-capture':
            errorMessage = 'Microphone not available. Please check your microphone.';
            break;
          case 'aborted':
            errorMessage = 'Speech recognition was stopped.';
            break;
        }
        
        this.callbacks.onError?.(new Error(errorMessage));
      };

      this.speechRecognition.onstart = () => {
        this.isRecording = true;
        console.log('Speech recognition started');
      };
    } else {
      console.warn('Speech recognition not supported in this browser');
      this.callbacks.onError?.(new Error('Speech recognition not supported in this browser. Please use Chrome, Safari, or Edge.'));
    }
  }

  async connect(): Promise<void> {
    // Already connected via browser APIs
    return Promise.resolve();
  }

  async disconnect(): Promise<void> {
    if (this.isRecording) {
      await this.stopRecording();
    }
    
    this.isConnected = false;
    this.callbacks.onConnectionStateChange?.(false);
  }

  async synthesizeSpeech(options: MobileTTSOptions): Promise<void> {
    if (!this.userInteracted) {
      throw new Error('Audio playback requires user interaction. Please tap the screen first.');
    }

    try {
      // Use the existing TTS action for consistency
      const { getAudio } = await import('@/lib/actions');
      
      const formData = new FormData();
      formData.append('text', options.text);
      formData.append('language', options.language || this.currentLanguage);

      const result = await getAudio(formData);

      if (result.data?.audioDataUri) {
        await this.playAudio(result.data.audioDataUri);
        this.callbacks.onAudioReceived?.(result.data.audioDataUri);
      } else {
        // Fallback to Web Speech API
        if ('speechSynthesis' in window) {
          const utterance = new SpeechSynthesisUtterance(options.text);
          utterance.lang = options.language || this.currentLanguage;
          speechSynthesis.speak(utterance);
          this.callbacks.onAudioReceived?.('speech-synthesis');
        } else {
          throw new Error('Failed to generate audio and no fallback available');
        }
      }
    } catch (error) {
      console.error('Failed to synthesize speech:', error);
      this.callbacks.onError?.(error as Error);
      throw error;
    }
  }

  private async playAudio(audioUrl: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const audio = new Audio(audioUrl);
      audio.preload = 'auto';
      audio.crossOrigin = 'anonymous';

      audio.oncanplaythrough = async () => {
        try {
          // Ensure audio context is resumed
          if (this.audioContext && this.audioContext.state === 'suspended') {
            await this.audioContext.resume();
          }
          
          await audio.play();
          resolve();
        } catch (error) {
          reject(error);
        }
      };

      audio.onerror = () => {
        reject(new Error('Failed to load audio'));
      };

      audio.onended = () => {
        resolve();
      };

      audio.load();
    });
  }

  async startRecording(options: MobileSTTOptions = {}): Promise<void> {
    if (this.isRecording || !this.speechRecognition) return;

    try {
      // Request microphone permissions explicitly
      if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        try {
          const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
          // Stop the stream immediately as we just needed permission
          stream.getTracks().forEach(track => track.stop());
        } catch (permissionError) {
          throw new Error('Microphone permission denied. Please allow microphone access in your browser settings.');
        }
      }

      // Configure speech recognition
      this.speechRecognition.continuous = options.continuous ?? false;
      this.speechRecognition.interimResults = options.interimResults ?? true;
      this.speechRecognition.lang = options.language || this.currentLanguage;

      // Start recognition
      this.speechRecognition.start();
      
      // Set timeout for mobile (prevent hanging)
      setTimeout(() => {
        if (this.isRecording && this.speechRecognition) {
          this.speechRecognition.stop();
        }
      }, 15000); // 15 second timeout

    } catch (error) {
      console.error('Failed to start recording:', error);
      this.callbacks.onError?.(error as Error);
      throw error;
    }
  }

  async stopRecording(): Promise<void> {
    if (!this.isRecording || !this.speechRecognition) return;

    try {
      this.speechRecognition.stop();
      this.isRecording = false;
    } catch (error) {
      console.error('Failed to stop recording:', error);
      this.callbacks.onError?.(error as Error);
    }
  }

  setLanguage(language: string): void {
    this.currentLanguage = language;
    if (this.speechRecognition) {
      this.speechRecognition.lang = language;
    }
  }

  get isServiceConnected(): boolean {
    return this.isConnected;
  }

  get isCurrentlyRecording(): boolean {
    return this.isRecording;
  }

  get hasUserInteracted(): boolean {
    return this.userInteracted;
  }
}
