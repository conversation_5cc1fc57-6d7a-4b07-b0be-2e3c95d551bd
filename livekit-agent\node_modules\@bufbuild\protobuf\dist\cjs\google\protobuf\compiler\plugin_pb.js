"use strict";
// Copyright 2021-2024 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
Object.defineProperty(exports, "__esModule", { value: true });
exports.CodeGeneratorResponse_File = exports.CodeGeneratorResponse_Feature = exports.CodeGeneratorResponse = exports.CodeGeneratorRequest = exports.Version = void 0;
const message_js_1 = require("../../../message.js");
const proto2_js_1 = require("../../../proto2.js");
const descriptor_pb_js_1 = require("../descriptor_pb.js");
/**
 * The version number of protocol compiler.
 *
 * @generated from message google.protobuf.compiler.Version
 */
class Version extends message_js_1.Message {
    constructor(data) {
        super();
        proto2_js_1.proto2.util.initPartial(data, this);
    }
    static fromBinary(bytes, options) {
        return new Version().fromBinary(bytes, options);
    }
    static fromJson(jsonValue, options) {
        return new Version().fromJson(jsonValue, options);
    }
    static fromJsonString(jsonString, options) {
        return new Version().fromJsonString(jsonString, options);
    }
    static equals(a, b) {
        return proto2_js_1.proto2.util.equals(Version, a, b);
    }
}
exports.Version = Version;
Version.runtime = proto2_js_1.proto2;
Version.typeName = "google.protobuf.compiler.Version";
Version.fields = proto2_js_1.proto2.util.newFieldList(() => [
    { no: 1, name: "major", kind: "scalar", T: 5 /* ScalarType.INT32 */, opt: true },
    { no: 2, name: "minor", kind: "scalar", T: 5 /* ScalarType.INT32 */, opt: true },
    { no: 3, name: "patch", kind: "scalar", T: 5 /* ScalarType.INT32 */, opt: true },
    { no: 4, name: "suffix", kind: "scalar", T: 9 /* ScalarType.STRING */, opt: true },
]);
/**
 * An encoded CodeGeneratorRequest is written to the plugin's stdin.
 *
 * @generated from message google.protobuf.compiler.CodeGeneratorRequest
 */
class CodeGeneratorRequest extends message_js_1.Message {
    constructor(data) {
        super();
        /**
         * The .proto files that were explicitly listed on the command-line.  The
         * code generator should generate code only for these files.  Each file's
         * descriptor will be included in proto_file, below.
         *
         * @generated from field: repeated string file_to_generate = 1;
         */
        this.fileToGenerate = [];
        /**
         * FileDescriptorProtos for all files in files_to_generate and everything
         * they import.  The files will appear in topological order, so each file
         * appears before any file that imports it.
         *
         * Note: the files listed in files_to_generate will include runtime-retention
         * options only, but all other files will include source-retention options.
         * The source_file_descriptors field below is available in case you need
         * source-retention options for files_to_generate.
         *
         * protoc guarantees that all proto_files will be written after
         * the fields above, even though this is not technically guaranteed by the
         * protobuf wire format.  This theoretically could allow a plugin to stream
         * in the FileDescriptorProtos and handle them one by one rather than read
         * the entire set into memory at once.  However, as of this writing, this
         * is not similarly optimized on protoc's end -- it will store all fields in
         * memory at once before sending them to the plugin.
         *
         * Type names of fields and extensions in the FileDescriptorProto are always
         * fully qualified.
         *
         * @generated from field: repeated google.protobuf.FileDescriptorProto proto_file = 15;
         */
        this.protoFile = [];
        /**
         * File descriptors with all options, including source-retention options.
         * These descriptors are only provided for the files listed in
         * files_to_generate.
         *
         * @generated from field: repeated google.protobuf.FileDescriptorProto source_file_descriptors = 17;
         */
        this.sourceFileDescriptors = [];
        proto2_js_1.proto2.util.initPartial(data, this);
    }
    static fromBinary(bytes, options) {
        return new CodeGeneratorRequest().fromBinary(bytes, options);
    }
    static fromJson(jsonValue, options) {
        return new CodeGeneratorRequest().fromJson(jsonValue, options);
    }
    static fromJsonString(jsonString, options) {
        return new CodeGeneratorRequest().fromJsonString(jsonString, options);
    }
    static equals(a, b) {
        return proto2_js_1.proto2.util.equals(CodeGeneratorRequest, a, b);
    }
}
exports.CodeGeneratorRequest = CodeGeneratorRequest;
CodeGeneratorRequest.runtime = proto2_js_1.proto2;
CodeGeneratorRequest.typeName = "google.protobuf.compiler.CodeGeneratorRequest";
CodeGeneratorRequest.fields = proto2_js_1.proto2.util.newFieldList(() => [
    { no: 1, name: "file_to_generate", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
    { no: 2, name: "parameter", kind: "scalar", T: 9 /* ScalarType.STRING */, opt: true },
    { no: 15, name: "proto_file", kind: "message", T: descriptor_pb_js_1.FileDescriptorProto, repeated: true },
    { no: 17, name: "source_file_descriptors", kind: "message", T: descriptor_pb_js_1.FileDescriptorProto, repeated: true },
    { no: 3, name: "compiler_version", kind: "message", T: Version, opt: true },
]);
/**
 * The plugin writes an encoded CodeGeneratorResponse to stdout.
 *
 * @generated from message google.protobuf.compiler.CodeGeneratorResponse
 */
class CodeGeneratorResponse extends message_js_1.Message {
    constructor(data) {
        super();
        /**
         * @generated from field: repeated google.protobuf.compiler.CodeGeneratorResponse.File file = 15;
         */
        this.file = [];
        proto2_js_1.proto2.util.initPartial(data, this);
    }
    static fromBinary(bytes, options) {
        return new CodeGeneratorResponse().fromBinary(bytes, options);
    }
    static fromJson(jsonValue, options) {
        return new CodeGeneratorResponse().fromJson(jsonValue, options);
    }
    static fromJsonString(jsonString, options) {
        return new CodeGeneratorResponse().fromJsonString(jsonString, options);
    }
    static equals(a, b) {
        return proto2_js_1.proto2.util.equals(CodeGeneratorResponse, a, b);
    }
}
exports.CodeGeneratorResponse = CodeGeneratorResponse;
CodeGeneratorResponse.runtime = proto2_js_1.proto2;
CodeGeneratorResponse.typeName = "google.protobuf.compiler.CodeGeneratorResponse";
CodeGeneratorResponse.fields = proto2_js_1.proto2.util.newFieldList(() => [
    { no: 1, name: "error", kind: "scalar", T: 9 /* ScalarType.STRING */, opt: true },
    { no: 2, name: "supported_features", kind: "scalar", T: 4 /* ScalarType.UINT64 */, opt: true },
    { no: 3, name: "minimum_edition", kind: "scalar", T: 5 /* ScalarType.INT32 */, opt: true },
    { no: 4, name: "maximum_edition", kind: "scalar", T: 5 /* ScalarType.INT32 */, opt: true },
    { no: 15, name: "file", kind: "message", T: CodeGeneratorResponse_File, repeated: true },
]);
/**
 * Sync with code_generator.h.
 *
 * @generated from enum google.protobuf.compiler.CodeGeneratorResponse.Feature
 */
var CodeGeneratorResponse_Feature;
(function (CodeGeneratorResponse_Feature) {
    /**
     * @generated from enum value: FEATURE_NONE = 0;
     */
    CodeGeneratorResponse_Feature[CodeGeneratorResponse_Feature["NONE"] = 0] = "NONE";
    /**
     * @generated from enum value: FEATURE_PROTO3_OPTIONAL = 1;
     */
    CodeGeneratorResponse_Feature[CodeGeneratorResponse_Feature["PROTO3_OPTIONAL"] = 1] = "PROTO3_OPTIONAL";
    /**
     * @generated from enum value: FEATURE_SUPPORTS_EDITIONS = 2;
     */
    CodeGeneratorResponse_Feature[CodeGeneratorResponse_Feature["SUPPORTS_EDITIONS"] = 2] = "SUPPORTS_EDITIONS";
})(CodeGeneratorResponse_Feature || (exports.CodeGeneratorResponse_Feature = CodeGeneratorResponse_Feature = {}));
// Retrieve enum metadata with: proto2.getEnumType(CodeGeneratorResponse_Feature)
proto2_js_1.proto2.util.setEnumType(CodeGeneratorResponse_Feature, "google.protobuf.compiler.CodeGeneratorResponse.Feature", [
    { no: 0, name: "FEATURE_NONE" },
    { no: 1, name: "FEATURE_PROTO3_OPTIONAL" },
    { no: 2, name: "FEATURE_SUPPORTS_EDITIONS" },
]);
/**
 * Represents a single generated file.
 *
 * @generated from message google.protobuf.compiler.CodeGeneratorResponse.File
 */
class CodeGeneratorResponse_File extends message_js_1.Message {
    constructor(data) {
        super();
        proto2_js_1.proto2.util.initPartial(data, this);
    }
    static fromBinary(bytes, options) {
        return new CodeGeneratorResponse_File().fromBinary(bytes, options);
    }
    static fromJson(jsonValue, options) {
        return new CodeGeneratorResponse_File().fromJson(jsonValue, options);
    }
    static fromJsonString(jsonString, options) {
        return new CodeGeneratorResponse_File().fromJsonString(jsonString, options);
    }
    static equals(a, b) {
        return proto2_js_1.proto2.util.equals(CodeGeneratorResponse_File, a, b);
    }
}
exports.CodeGeneratorResponse_File = CodeGeneratorResponse_File;
CodeGeneratorResponse_File.runtime = proto2_js_1.proto2;
CodeGeneratorResponse_File.typeName = "google.protobuf.compiler.CodeGeneratorResponse.File";
CodeGeneratorResponse_File.fields = proto2_js_1.proto2.util.newFieldList(() => [
    { no: 1, name: "name", kind: "scalar", T: 9 /* ScalarType.STRING */, opt: true },
    { no: 2, name: "insertion_point", kind: "scalar", T: 9 /* ScalarType.STRING */, opt: true },
    { no: 15, name: "content", kind: "scalar", T: 9 /* ScalarType.STRING */, opt: true },
    { no: 16, name: "generated_code_info", kind: "message", T: descriptor_pb_js_1.GeneratedCodeInfo, opt: true },
]);
