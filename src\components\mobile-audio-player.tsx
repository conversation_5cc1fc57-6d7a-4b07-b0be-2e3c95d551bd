'use client';

import React, { useRef, useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Volume2, VolumeX } from 'lucide-react';

interface MobileAudioPlayerProps {
  audioUrl?: string;
  text?: string;
  onPlay?: () => void;
  onError?: (error: Error) => void;
  autoPlay?: boolean;
  className?: string;
}

export function MobileAudioPlayer({
  audioUrl,
  text,
  onPlay,
  onError,
  autoPlay = false,
  className = ''
}: MobileAudioPlayerProps) {
  const audioRef = useRef<HTMLAudioElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [userInteracted, setUserInteracted] = useState(false);
  const [audioContext, setAudioContext] = useState<AudioContext | null>(null);

  useEffect(() => {
    // Initialize audio context for mobile
    const initAudioContext = () => {
      if (!audioContext && typeof window !== 'undefined') {
        const AudioContextClass = window.AudioContext || (window as any).webkitAudioContext;
        if (AudioContextClass) {
          const ctx = new AudioContextClass();
          setAudioContext(ctx);
          setUserInteracted(true);
        }
      }
    };

    // Listen for user interaction
    const handleInteraction = () => {
      initAudioContext();
      document.removeEventListener('touchstart', handleInteraction);
      document.removeEventListener('click', handleInteraction);
    };

    document.addEventListener('touchstart', handleInteraction, { once: true });
    document.addEventListener('click', handleInteraction, { once: true });

    return () => {
      document.removeEventListener('touchstart', handleInteraction);
      document.removeEventListener('click', handleInteraction);
    };
  }, [audioContext]);

  useEffect(() => {
    if (audioUrl && autoPlay && userInteracted) {
      playAudio();
    }
  }, [audioUrl, autoPlay, userInteracted]);

  const playAudio = async () => {
    if (!audioUrl && !text) return;

    setIsLoading(true);
    setIsPlaying(false);

    try {
      // Resume audio context if suspended (mobile requirement)
      if (audioContext && audioContext.state === 'suspended') {
        await audioContext.resume();
      }

      if (audioRef.current) {
        // If we have an audio URL, use it
        if (audioUrl) {
          audioRef.current.src = audioUrl;
          audioRef.current.load();
          
          // Wait for audio to be ready
          await new Promise((resolve, reject) => {
            if (!audioRef.current) return reject(new Error('Audio element not available'));
            
            const handleCanPlay = () => {
              audioRef.current?.removeEventListener('canplaythrough', handleCanPlay);
              audioRef.current?.removeEventListener('error', handleError);
              resolve(void 0);
            };
            
            const handleError = () => {
              audioRef.current?.removeEventListener('canplaythrough', handleCanPlay);
              audioRef.current?.removeEventListener('error', handleError);
              reject(new Error('Failed to load audio'));
            };
            
            audioRef.current.addEventListener('canplaythrough', handleCanPlay, { once: true });
            audioRef.current.addEventListener('error', handleError, { once: true });
          });

          await audioRef.current.play();
          setIsPlaying(true);
          onPlay?.();
        } else if (text && 'speechSynthesis' in window) {
          // Fallback to Web Speech API
          const utterance = new SpeechSynthesisUtterance(text);
          utterance.onstart = () => {
            setIsPlaying(true);
            onPlay?.();
          };
          utterance.onend = () => {
            setIsPlaying(false);
          };
          utterance.onerror = (event) => {
            setIsPlaying(false);
            onError?.(new Error(`Speech synthesis error: ${event.error}`));
          };
          
          speechSynthesis.speak(utterance);
        }
      }
    } catch (error) {
      console.error('Audio playback failed:', error);
      setIsPlaying(false);
      
      // Try Web Speech API as fallback
      if (text && 'speechSynthesis' in window) {
        try {
          const utterance = new SpeechSynthesisUtterance(text);
          utterance.onstart = () => setIsPlaying(true);
          utterance.onend = () => setIsPlaying(false);
          speechSynthesis.speak(utterance);
          onPlay?.();
        } catch (fallbackError) {
          onError?.(error as Error);
        }
      } else {
        onError?.(error as Error);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const stopAudio = () => {
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
    }
    
    if ('speechSynthesis' in window) {
      speechSynthesis.cancel();
    }
    
    setIsPlaying(false);
  };

  const handleAudioEnd = () => {
    setIsPlaying(false);
  };

  const handleAudioError = (event: React.SyntheticEvent<HTMLAudioElement, Event>) => {
    console.error('Audio error:', event);
    setIsPlaying(false);
    setIsLoading(false);
    onError?.(new Error('Audio playback failed'));
  };

  if (!audioUrl && !text) {
    return null;
  }

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <audio
        ref={audioRef}
        onEnded={handleAudioEnd}
        onError={handleAudioError}
        preload="auto"
        crossOrigin="anonymous"
      />
      
      <Button
        variant="ghost"
        size="sm"
        onClick={isPlaying ? stopAudio : playAudio}
        disabled={isLoading}
        className="p-2"
      >
        {isLoading ? (
          <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
        ) : isPlaying ? (
          <VolumeX className="w-4 h-4" />
        ) : (
          <Volume2 className="w-4 h-4" />
        )}
      </Button>
      
      {!userInteracted && (
        <span className="text-xs text-muted-foreground">
          Tap to enable audio
        </span>
      )}
    </div>
  );
}
